import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-staff-hub',
  standalone: true,
  imports: [
    CommonModule, CardModule, ButtonModule
  ],
  template: `
    <div class="staff-hub-container">
      <p-card>
        <ng-template pTemplate="header">
          <h3>
            <i class="pi pi-circle"></i>
            Staff Hub (Employee Portal)
          <p>Content</p><ng-template pTemplate="content">
          <p>Staff Hub coming soon! This will include:</p>
          <ul>
            <li>Employee dashboard</li>
            <li>Time tracking</li>
            <li>Schedule viewing</li>
            <li>Time-off requests</li>
            <li>Personal information management</li>
          </ul</ng-template></p-card>
    </div>
  `,
  styles: [`
    .staff-hub-container {
      padding: 24px;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class StaffHubComponent {}
