# StaffManager Components Guide

## 📋 Overview

StaffManager uses a component-based architecture with Angular 19 standalone components. This guide provides comprehensive documentation for all major components, their usage, inputs, outputs, and integration patterns.

## 🏗️ Component Architecture

### Component Categories

1. **Layout Components** - Application structure and navigation
2. **Feature Components** - Business logic and user interactions
3. **Shared Components** - Reusable UI elements
4. **Widget Components** - Dashboard widgets and data visualization

## 🎯 Layout Components

### HeaderComponent

**Location**: `src/app/layout/header/header.component.ts`

**Purpose**: Main application header with navigation, user menu, and business selector.

#### Usage
```html
<app-header></app-header>
```

#### Features
- Business selector dropdown
- User profile menu with avatar
- Theme toggle (light/dark)
- Responsive design for mobile/desktop
- Real-time business switching

#### Inputs
```typescript
@Input() showBusinessSelector: boolean = true;
@Input() showUserMenu: boolean = true;
@Input() showThemeToggle: boolean = true;
```

#### Outputs
```typescript
@Output() businessChanged = new EventEmitter<Business>();
@Output() userMenuAction = new EventEmitter<string>();
@Output() themeToggled = new EventEmitter<boolean>();
```

### SidebarComponent

**Location**: `src/app/layout/sidebar/sidebar.component.ts`

**Purpose**: Collapsible navigation sidebar with dynamic menu items.

#### Usage
```html
<app-sidebar 
  [collapsed]="isCollapsed"
  (navigationClick)="onNavigate($event)">
</app-sidebar>
```

#### Features
- Collapsible design (240px ↔ 64px)
- Icon-only mode when collapsed
- Tooltips for collapsed items
- User menu at bottom
- Business selector integration

#### Inputs
```typescript
@Input() collapsed: boolean = false;
@Input() showUserMenu: boolean = true;
@Input() menuItems: MenuItem[] = [];
```

#### Outputs
```typescript
@Output() toggleCollapse = new EventEmitter<boolean>();
@Output() navigationClick = new EventEmitter<string>();
@Output() userAction = new EventEmitter<string>();
```

## 📊 Dashboard Components

### EnhancedDashboardComponent

**Location**: `src/app/features/dashboard/enhanced-dashboard/enhanced-dashboard.component.ts`

**Purpose**: Main dashboard with customizable widgets and drag-drop functionality.

#### Usage
```html
<app-enhanced-dashboard 
  [businessId]="selectedBusinessId"
  [widgets]="dashboardWidgets">
</app-enhanced-dashboard>
```

#### Features
- Drag-drop widget arrangement
- Resizable widgets
- Custom widget builder integration
- Real-time data updates
- Responsive grid layout

#### Inputs
```typescript
@Input() businessId: string | null = null;
@Input() widgets: DashboardWidget[] = [];
@Input() columns: number = 3;
@Input() editable: boolean = true;
```

#### Outputs
```typescript
@Output() widgetAdded = new EventEmitter<DashboardWidget>();
@Output() widgetRemoved = new EventEmitter<string>();
@Output() widgetMoved = new EventEmitter<WidgetMoveEvent>();
@Output() widgetResized = new EventEmitter<WidgetResizeEvent>();
```

### CustomWidgetBuilderComponent

**Location**: `src/app/features/dashboard/custom-widget-builder/custom-widget-builder.component.ts`

**Purpose**: Interface for creating and configuring custom dashboard widgets.

#### Usage
```html
<app-custom-widget-builder 
  [widget]="editingWidget"
  (widgetSaved)="onWidgetSaved($event)"
  (cancelled)="onCancel()">
</app-custom-widget-builder>
```

#### Features
- Visual widget configuration
- Data source selection
- Chart type selection
- Real-time preview
- Template library

## 👥 Staff Management Components

### StaffListComponent

**Location**: `src/app/features/staff/staff-list/staff-list.component.ts`

**Purpose**: Display and manage list of staff members with filtering and actions.

#### Usage
```html
<app-staff-list 
  [businessId]="currentBusinessId"
  [showActions]="canManageStaff"
  (staffSelected)="onStaffSelected($event)">
</app-staff-list>
```

#### Features
- Searchable and filterable list
- Sortable columns
- Bulk actions
- Status indicators
- Quick actions menu

#### Inputs
```typescript
@Input() businessId: string | null = null;
@Input() showActions: boolean = true;
@Input() selectable: boolean = false;
@Input() filters: StaffFilter[] = [];
```

#### Outputs
```typescript
@Output() staffSelected = new EventEmitter<Staff>();
@Output() staffAction = new EventEmitter<StaffActionEvent>();
@Output() bulkAction = new EventEmitter<BulkActionEvent>();
```

### StaffProfileComponent

**Location**: `src/app/features/staff/staff-profile/staff-profile.component.ts`

**Purpose**: Comprehensive staff profile view with tabs for different information sections.

#### Usage
```html
<app-staff-profile 
  [staffId]="selectedStaffId"
  [editable]="canEditStaff"
  (profileUpdated)="onProfileUpdated($event)">
</app-staff-profile>
```

#### Features
- Tabbed interface (Personal, Employment, Availability, Schedule)
- Photo upload
- Availability calendar
- Schedule integration
- Time-off history

### StaffFormComponent

**Location**: `src/app/features/staff/staff-form/staff-form.component.ts`

**Purpose**: Form for creating and editing staff members.

#### Usage
```html
<app-staff-form 
  [staff]="editingStaff"
  [businessId]="currentBusinessId"
  (staffSaved)="onStaffSaved($event)"
  (cancelled)="onCancel()">
</app-staff-form>
```

#### Features
- Multi-step form wizard
- Validation and error handling
- Photo upload
- Availability setup
- Role assignment

## ⏰ Time Management Components

### TimeManagementComponent

**Location**: `src/app/features/time-management/time-management.component.ts`

**Purpose**: Comprehensive time tracking and management interface.

#### Usage
```html
<app-time-management 
  [staffId]="selectedStaffId"
  [businessId]="currentBusinessId"
  [view]="currentView">
</app-time-management>
```

#### Features
- Multiple view modes (daily, weekly, monthly)
- Clock in/out functionality
- Time-off request management
- Schedule overview
- AI-powered insights

#### Inputs
```typescript
@Input() staffId: string | null = null;
@Input() businessId: string | null = null;
@Input() view: 'daily' | 'weekly' | 'monthly' = 'weekly';
@Input() showAIInsights: boolean = true;
```

#### Outputs
```typescript
@Output() timeEntryCreated = new EventEmitter<TimeEntry>();
@Output() timeOffRequested = new EventEmitter<TimeOffRequest>();
@Output() scheduleChanged = new EventEmitter<ScheduleChangeEvent>();
```

## 🏢 Business Profile Components

### BusinessProfileSettingsComponent

**Location**: `src/app/features/business-profile/business-profile-settings/business-profile-settings.component.ts`

**Purpose**: Comprehensive business configuration interface.

#### Usage
```html
<app-business-profile-settings 
  [businessId]="currentBusinessId"
  (settingsUpdated)="onSettingsUpdated($event)">
</app-business-profile-settings>
```

#### Features
- Tabbed configuration interface
- Hours of Operation (HOO) setup
- Hours of Business (HOB) setup
- Operational settings
- Staff management settings

## ⚙️ Settings Components

### SettingsComponent

**Location**: `src/app/features/settings/settings.component.ts`

**Purpose**: Main settings interface with categorized navigation.

#### Usage
```html
<app-settings></app-settings>
```

#### Features
- Sidebar navigation with categories
- Real-time system status
- Quick actions
- Settings search
- Export/import functionality

### AppearanceSettingsComponent

**Location**: `src/app/features/settings/components/appearance-settings.component.ts`

**Purpose**: Complete appearance customization interface.

#### Usage
```html
<app-appearance-settings></app-appearance-settings>
```

#### Features
- Theme selection (light/dark/system)
- Color scheme customization
- Typography settings
- Layout preferences
- Accessibility options
- Real-time preview

### SystemsCheckComponent

**Location**: `src/app/features/settings/components/systems-check.component.ts`

**Purpose**: System health monitoring and diagnostics.

#### Usage
```html
<app-systems-check></app-systems-check>
```

#### Features
- Real-time service status monitoring
- Diagnostic information
- Troubleshooting guides
- Export diagnostics
- Auto-refresh capabilities

## 📅 Calendar Components

### CalendarComponent

**Location**: `src/app/features/calendar/calendar.component.ts`

**Purpose**: FullCalendar integration with staff scheduling.

#### Usage
```html
<app-calendar 
  [businessId]="currentBusinessId"
  [view]="calendarView"
  [events]="calendarEvents"
  (eventClick)="onEventClick($event)"
  (dateSelect)="onDateSelect($event)">
</app-calendar>
```

#### Features
- Multiple calendar views
- Drag-drop event creation
- Staff assignment
- Color-coded events
- Integration with scheduling

## 🔐 Authentication Components

### LoginComponent

**Location**: `src/app/features/auth/login/login.component.ts`

**Purpose**: User authentication interface.

#### Usage
```html
<app-login 
  (loginSuccess)="onLoginSuccess($event)"
  (loginError)="onLoginError($event)">
</app-login>
```

#### Features
- Email/password authentication
- Remember me functionality
- Password reset
- Social login integration
- Form validation

### RegisterComponent

**Location**: `src/app/features/auth/register/register.component.ts`

**Purpose**: User registration interface.

#### Usage
```html
<app-register 
  (registrationSuccess)="onRegistrationSuccess($event)"
  (registrationError)="onRegistrationError($event)">
</app-register>
```

#### Features
- Multi-step registration
- Email verification
- Profile setup
- Business creation option
- Terms acceptance

## 🧩 Shared Components

### BusinessSelectorDialogComponent

**Location**: `src/app/shared/components/business-selector-dialog/business-selector-dialog.component.ts`

**Purpose**: Modal dialog for selecting or creating businesses.

#### Usage
```typescript
const dialogRef = this.dialog.open(BusinessSelectorDialogComponent, {
  data: { currentBusinessId: this.selectedBusinessId }
});

dialogRef.afterClosed().subscribe(result => {
  if (result) {
    this.selectBusiness(result);
  }
});
```

#### Features
- Business list with search
- Create new business option
- Business switching
- Recent businesses

### WidgetSettingsDialogComponent

**Location**: `src/app/shared/components/widget-settings-dialog/widget-settings-dialog.component.ts`

**Purpose**: Modal dialog for configuring widget settings.

#### Usage
```typescript
const dialogRef = this.dialog.open(WidgetSettingsDialogComponent, {
  data: { widget: this.selectedWidget }
});
```

#### Features
- Widget configuration options
- Data source selection
- Appearance settings
- Preview functionality

## 📱 Responsive Design

### Breakpoint System

All components follow a consistent breakpoint system:

```scss
// Mobile first approach
.component {
  // Mobile styles (default)
  
  @media (min-width: 768px) {
    // Tablet styles
  }
  
  @media (min-width: 1024px) {
    // Desktop styles
  }
  
  @media (min-width: 1440px) {
    // Large desktop styles
  }
}
```

### Mobile Adaptations

- **Sidebar**: Overlay mode on mobile
- **Dashboard**: Single column layout
- **Tables**: Horizontal scrolling
- **Forms**: Stacked layout
- **Dialogs**: Full-screen on mobile

## 🎨 Theming and Styling

### Component Styling Pattern

```typescript
@Component({
  selector: 'app-example',
  standalone: true,
  template: `...`,
  styles: [`
    :host {
      display: block;
      // Host styles
    }
    
    .component-container {
      // Container styles
    }
    
    // Responsive styles
    @media (max-width: 768px) {
      .component-container {
        // Mobile styles
      }
    }
  `]
})
```

### Material Design Integration

All components use Angular Material components and follow Material Design principles:

- Consistent spacing (8px grid)
- Material color palette
- Typography scale
- Elevation system
- Motion and transitions

## 🧪 Testing Components

### Component Testing Pattern

```typescript
describe('ExampleComponent', () => {
  let component: ExampleComponent;
  let fixture: ComponentFixture<ExampleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ExampleComponent, ...requiredImports]
    }).compileComponents();

    fixture = TestBed.createComponent(ExampleComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit event on action', () => {
    spyOn(component.actionEmitted, 'emit');
    component.performAction();
    expect(component.actionEmitted.emit).toHaveBeenCalled();
  });
});
```

## 🔄 Component Communication

### Parent-Child Communication

```typescript
// Parent component
@Component({
  template: `
    <app-child 
      [inputData]="parentData"
      (outputEvent)="handleChildEvent($event)">
    </app-child>
  `
})
export class ParentComponent {
  parentData = { /* data */ };
  
  handleChildEvent(event: any) {
    // Handle child event
  }
}

// Child component
@Component({
  selector: 'app-child'
})
export class ChildComponent {
  @Input() inputData: any;
  @Output() outputEvent = new EventEmitter<any>();
  
  emitEvent() {
    this.outputEvent.emit(/* data */);
  }
}
```

### Service-based Communication

```typescript
// Shared service for component communication
@Injectable({ providedIn: 'root' })
export class ComponentCommunicationService {
  private eventSubject = new Subject<ComponentEvent>();
  
  events$ = this.eventSubject.asObservable();
  
  emitEvent(event: ComponentEvent) {
    this.eventSubject.next(event);
  }
}
```

---

This component guide provides comprehensive documentation for all major components in the StaffManager application, enabling developers to effectively use, extend, and maintain the component library.
