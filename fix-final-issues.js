#!/usr/bin/env node

/**
 * Final fixes for PrimeNG migration - Template and Import cleanup
 */

const fs = require('fs');
const path = require('path');

function fixFinalIssues(filePath) {
  console.log(`Final fixing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix PrimeNG Card template structure issues
  const cardFixes = [
    // Remove invalid nested ng-template structures
    {
      from: /<ng-template pTemplate="title">[^<]*<\/ng-template>\s*<ng-template pTemplate="subtitle">[^<]*<\/ng-template>/g,
      to: ''
    },
    // Fix broken card structures
    {
      from: /<p-card-header>\s*<p-card-title>[^<]*<\/ng-template>/g,
      to: '<ng-template pTemplate="header"><h3>'
    },
    {
      from: /<p-card-title>[^<]*<\/ng-template>/g,
      to: '<h3>'
    },
    {
      from: /<p-card-subtitle>[^<]*<\/ng-template>/g,
      to: '<p>'
    },
    {
      from: /<p-card-content>/g,
      to: '<ng-template pTemplate="content">'
    },
    {
      from: /<p-card-actions>/g,
      to: '<ng-template pTemplate="footer">'
    },
    // Fix closing tags
    {
      from: /<\/ng-template>\s*<\/ng-template>\s*<\/ng-template>/g,
      to: '</ng-template>'
    },
    {
      from: /<\/ng-template>\s*<\/p-card>/g,
      to: '</ng-template></p-card>'
    }
  ];

  cardFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix import syntax errors
  const importFixes = [
    // Fix missing commas in imports
    {
      from: /TableModule\s+DropdownModule/g,
      to: 'TableModule, DropdownModule'
    },
    {
      from: /TableModule\s+InputTextModule/g,
      to: 'TableModule, InputTextModule'
    },
    {
      from: /CheckboxModule\s+ButtonModule/g,
      to: 'CheckboxModule, ButtonModule'
    },
    {
      from: /ButtonModule,\s+ButtonModule/g,
      to: 'ButtonModule'
    },
    {
      from: /ToolbarModule,\s+ToolbarModule/g,
      to: 'ToolbarModule'
    },
    // Fix Material imports that weren't caught
    {
      from: /MatListModule,?\s*/g,
      to: ''
    },
    {
      from: /MatIconModule,?\s*/g,
      to: ''
    },
    {
      from: /MatButtonModule,?\s*/g,
      to: ''
    },
    {
      from: /MatDividerModule,?\s*/g,
      to: ''
    },
    {
      from: /MatSelectModule,?\s*/g,
      to: ''
    },
    {
      from: /MatOptionModule,?\s*/g,
      to: ''
    },
    {
      from: /MatBadgeModule,?\s*/g,
      to: ''
    },
    {
      from: /MatTooltipModule,?\s*/g,
      to: ''
    }
  ];

  importFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix button attribute issues
  const buttonFixes = [
    {
      from: /p-button\s+\[text\]="true"/g,
      to: 'p-button [text]="true"'
    },
    {
      from: /p-button\s+\[outlined\]="true"/g,
      to: 'p-button [outlined]="true"'
    },
    {
      from: /<button\s+p-button/g,
      to: '<p-button'
    },
    {
      from: /<\/button>/g,
      to: '</p-button>'
    }
  ];

  buttonFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix remaining Material Icons
  const iconFixes = [
    {
      from: /<mat-icon>build<\/mat-icon>/g,
      to: '<i class="pi pi-wrench"></i>'
    },
    {
      from: /<mat-icon>refresh<\/mat-icon>/g,
      to: '<i class="pi pi-refresh"></i>'
    },
    {
      from: /<mat-icon>calendar_view_week<\/mat-icon>/g,
      to: '<i class="pi pi-calendar"></i>'
    },
    {
      from: /<mat-icon>schedule<\/mat-icon>/g,
      to: '<i class="pi pi-clock"></i>'
    }
  ];

  iconFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Clean up syntax issues
  const cleanupFixes = [
    // Remove duplicate commas
    {
      from: /,\s*,/g,
      to: ','
    },
    // Fix array syntax
    {
      from: /\[\s*,/g,
      to: '['
    },
    {
      from: /,\s*\]/g,
      to: ']'
    },
    // Remove empty lines
    {
      from: /\n\s*\n\s*\n/g,
      to: '\n\n'
    }
  ];

  cleanupFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Final fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

function findFiles(dir, extensions = ['.ts', '.html']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
        traverse(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
function main() {
  console.log('🔧 Final fixes for PrimeNG migration...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const files = findFiles(srcDir);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFinalIssues(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n✨ Final fixes complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`✅ Files fixed: ${fixedCount}`);
}

if (require.main === module) {
  main();
}

module.exports = { fixFinalIssues };
