import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, combineLatest, timer, of } from 'rxjs';
import { map, switchMap, filter, distinctUntilChanged, shareReplay } from 'rxjs/operators';
import { DataSyncService } from './data-sync.service';
import { AuthService } from './auth.service';
import { TimeEntry, TimeBreak, TimeClockStatus, TimeClockSettings, TimeClockIssue } from '../models/time-entry.model';

@Injectable({
  providedIn: 'root'
})
export class TimeTrackingService {
  private dataSyncService = inject(DataSyncService);
  private authService = inject(AuthService);
  
  // Current time clock status
  private timeClockStatusSubject = new BehaviorSubject<TimeClockStatus | null>(null);
  
  // Current active time entry
  private activeTimeEntrySubject = new BehaviorSubject<TimeEntry | null>(null);
  
  // Time clock settings
  private timeClockSettingsSubject = new BehaviorSubject<TimeClockSettings | null>(null);

  // Public observables
  timeClockStatus$ = this.timeClockStatusSubject.asObservable();
  activeTimeEntry$ = this.activeTimeEntrySubject.asObservable();
  timeClockSettings$ = this.timeClockSettingsSubject.asObservable();

  // Current time observable (updates every second)
  currentTime$ = timer(0, 1000).pipe(
    map(() => new Date()),
    shareReplay(1)
  );

  // Elapsed time for current session
  elapsedTime$ = combineLatest([
    this.activeTimeEntry$,
    this.currentTime$
  ]).pipe(
    map(([entry, currentTime]) => {
      if (!entry || !entry.clockInTime) return 0;
      return Math.floor((currentTime.getTime() - entry.clockInTime.getTime()) / 1000);
    }),
    distinctUntilChanged()
  );

  constructor() {
    this.initializeTimeTracking();
  }

  // Initialize time tracking for current user
  private initializeTimeTracking(): void {
    this.authService.getCurrentStaffId().pipe(
      filter(staffId => !!staffId),
      switchMap(staffId => this.loadTimeClockStatus(staffId!))
    ).subscribe();
  }

  // Load current time clock status for staff member
  private loadTimeClockStatus(staffId: string): Observable<TimeClockStatus> {
    return combineLatest([
      this.getCurrentTimeEntry(staffId),
      this.getTodayTimeEntries(staffId),
      this.getWeekTimeEntries(staffId),
      this.getPendingIssues(staffId)
    ]).pipe(
      map(([currentEntry, todayEntries, weekEntries, issues]) => {
        const totalHoursToday = this.calculateTotalHours(todayEntries);
        const totalHoursWeek = this.calculateTotalHours(weekEntries);
        
        const status: TimeClockStatus = {
          staffId,
          isClocked: !!currentEntry && !currentEntry.clockOutTime,
          currentTimeEntry: currentEntry,
          lastClockIn: currentEntry?.clockInTime,
          lastClockOut: this.getLastClockOut(todayEntries),
          totalHoursToday,
          totalHoursWeek,
          onBreak: this.isOnBreak(currentEntry),
          currentBreak: this.getCurrentBreak(currentEntry),
          pendingIssues: issues
        };
        
        this.timeClockStatusSubject.next(status);
        this.activeTimeEntrySubject.next(currentEntry);
        
        return status;
      })
    );
  }

  // Clock in
  async clockIn(staffId: string, location?: GeolocationPosition, deviceInfo?: any): Promise<TimeEntry> {
    try {
      const currentStatus = this.timeClockStatusSubject.value;
      
      // Check if already clocked in
      if (currentStatus?.isClocked) {
        throw new Error('Already clocked in');
      }
      
      // Create new time entry
      const timeEntry: Partial<TimeEntry> = {
        staffId,
        businessId: await this.getCurrentBusinessId(staffId),
        clockInTime: new Date(),
        status: 'active',
        isManualEntry: false,
        requiresApproval: false,
        breaks: [],
        clockInLocation: location ? {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        } : undefined,
        deviceInfo: deviceInfo ? {
          deviceId: deviceInfo.deviceId || 'unknown',
          deviceType: deviceInfo.deviceType || 'unknown',
          userAgent: navigator.userAgent,
          ipAddress: deviceInfo.ipAddress || 'unknown'
        } : undefined
      };
      
      const entryId = await this.dataSyncService.createDocument('timeEntries', timeEntry);
      
      // Reload status
      await this.loadTimeClockStatus(staffId).toPromise();
      
      return { id: entryId, ...timeEntry } as TimeEntry;
    } catch (error) {
      console.error('Clock in error:', error);
      throw error;
    }
  }

  // Clock out
  async clockOut(staffId: string, location?: GeolocationPosition): Promise<TimeEntry> {
    try {
      const currentStatus = this.timeClockStatusSubject.value;
      const activeEntry = currentStatus?.currentTimeEntry;
      
      if (!activeEntry || activeEntry.clockOutTime) {
        throw new Error('No active time entry to clock out');
      }
      
      const clockOutTime = new Date();
      const totalHours = (clockOutTime.getTime() - activeEntry.clockInTime.getTime()) / (1000 * 60 * 60);
      
      // Calculate total break time
      const totalBreakTime = this.calculateBreakTime(activeEntry.breaks);
      
      const updateData = {
        clockOutTime,
        totalHours: Math.round((totalHours - totalBreakTime / 60) * 100) / 100,
        status: 'completed' as const,
        clockOutLocation: location ? {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude
        } : undefined
      };
      
      await this.dataSyncService.updateDocument('timeEntries', activeEntry.id, updateData);
      
      // Reload status
      await this.loadTimeClockStatus(staffId).toPromise();
      
      return { ...activeEntry, ...updateData };
    } catch (error) {
      console.error('Clock out error:', error);
      throw error;
    }
  }

  // Start break
  async startBreak(staffId: string, type: 'lunch' | 'break' | 'personal' | 'other' = 'break'): Promise<TimeBreak> {
    try {
      const currentStatus = this.timeClockStatusSubject.value;
      const activeEntry = currentStatus?.currentTimeEntry;
      
      if (!activeEntry || activeEntry.clockOutTime) {
        throw new Error('No active time entry');
      }
      
      if (currentStatus.onBreak) {
        throw new Error('Already on break');
      }
      
      const breakEntry: TimeBreak = {
        id: this.generateId(),
        timeEntryId: activeEntry.id,
        startTime: new Date(),
        type,
        isPaid: type === 'break', // Typically short breaks are paid
      };
      
      const updatedBreaks = [...activeEntry.breaks, breakEntry];
      
      await this.dataSyncService.updateDocument('timeEntries', activeEntry.id, {
        breaks: updatedBreaks
      });
      
      // Reload status
      await this.loadTimeClockStatus(staffId).toPromise();
      
      return breakEntry;
    } catch (error) {
      console.error('Start break error:', error);
      throw error;
    }
  }

  // End break
  async endBreak(staffId: string): Promise<TimeBreak> {
    try {
      const currentStatus = this.timeClockStatusSubject.value;
      const activeEntry = currentStatus?.currentTimeEntry;
      const currentBreak = currentStatus?.currentBreak;
      
      if (!activeEntry || !currentBreak) {
        throw new Error('No active break to end');
      }
      
      const endTime = new Date();
      const duration = Math.floor((endTime.getTime() - currentBreak.startTime.getTime()) / (1000 * 60));
      
      const updatedBreak = {
        ...currentBreak,
        endTime,
        duration
      };
      
      const updatedBreaks = activeEntry.breaks.map(b => 
        b.id === currentBreak.id ? updatedBreak : b
      );
      
      await this.dataSyncService.updateDocument('timeEntries', activeEntry.id, {
        breaks: updatedBreaks
      });
      
      // Reload status
      await this.loadTimeClockStatus(staffId).toPromise();
      
      return updatedBreak;
    } catch (error) {
      console.error('End break error:', error);
      throw error;
    }
  }

  // Get current time entry for staff
  private getCurrentTimeEntry(staffId: string): Observable<TimeEntry | null> {
    return this.dataSyncService.subscribeToCollection<TimeEntry>(
      'timeEntries',
      [
        { field: 'staffId', operator: '==', value: staffId },
        { field: 'status', operator: '==', value: 'active' }
      ],
      'clockInTime',
      1
    ).pipe(
      map(entries => entries.length > 0 ? entries[0] : null)
    );
  }

  // Get today's time entries
  private getTodayTimeEntries(staffId: string): Observable<TimeEntry[]> {
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);
    
    return this.dataSyncService.subscribeToCollection<TimeEntry>(
      'timeEntries',
      [
        { field: 'staffId', operator: '==', value: staffId },
        { field: 'clockInTime', operator: '>=', value: startOfDay },
        { field: 'clockInTime', operator: '<=', value: endOfDay }
      ],
      'clockInTime'
    );
  }

  // Get this week's time entries
  private getWeekTimeEntries(staffId: string): Observable<TimeEntry[]> {
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    startOfWeek.setHours(0, 0, 0, 0);
    
    return this.dataSyncService.subscribeToCollection<TimeEntry>(
      'timeEntries',
      [
        { field: 'staffId', operator: '==', value: staffId },
        { field: 'clockInTime', operator: '>=', value: startOfWeek }
      ],
      'clockInTime'
    );
  }

  // Get pending issues for staff
  private getPendingIssues(staffId: string): Observable<TimeClockIssue[]> {
    return this.dataSyncService.subscribeToCollection<TimeClockIssue>(
      'timeClockIssues',
      [
        { field: 'staffId', operator: '==', value: staffId },
        { field: 'isResolved', operator: '==', value: false }
      ],
      'date'
    );
  }

  // Helper methods
  private calculateTotalHours(entries: TimeEntry[]): number {
    return entries.reduce((total, entry) => {
      return total + (entry.totalHours || 0);
    }, 0);
  }

  private calculateBreakTime(breaks: TimeBreak[]): number {
    return breaks.reduce((total, breakEntry) => {
      return total + (breakEntry.duration || 0);
    }, 0);
  }

  private isOnBreak(entry: TimeEntry | null): boolean {
    if (!entry) return false;
    return entry.breaks.some(b => b.startTime && !b.endTime);
  }

  private getCurrentBreak(entry: TimeEntry | null): TimeBreak | undefined {
    if (!entry) return undefined;
    return entry.breaks.find(b => b.startTime && !b.endTime);
  }

  private getLastClockOut(entries: TimeEntry[]): Date | undefined {
    const completedEntries = entries.filter(e => e.clockOutTime);
    if (completedEntries.length === 0) return undefined;
    
    return completedEntries.sort((a, b) => 
      b.clockOutTime!.getTime() - a.clockOutTime!.getTime()
    )[0].clockOutTime;
  }

  private async getCurrentBusinessId(staffId: string): Promise<string> {
    // This would typically come from the staff member's profile
    // For now, return a default business ID
    return 'default-business';
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
