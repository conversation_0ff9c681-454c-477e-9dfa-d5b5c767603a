import { Component, OnInit, inject } from '@angular/core';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ToolbarModule } from 'primeng/toolbar';

import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SidebarComponent } from './sidebar.component';
import { HeaderComponent } from './header.component';
import { BusinessService } from '../services/business.service';
import { StaffManagerThemeService } from '../core/theme/staffmanager-theme';
import { UserDataMigrationService } from '../core/services/user-data-migration.service';
import { fadeInAnimation, slideInAnimation, sidebarToggleAnimation } from '../core/animations/staffmanager-animations';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatSidenavModule,
    ToolbarModule
    ButtonModule,
    TooltipModule,
    SidebarComponent,
    HeaderComponent
  ],
  animations: [fadeInAnimation, slideInAnimation, sidebarToggleAnimation],
  template: `
    <div class="app-shell"
         [class.sidebar-collapsed]="collapsed && !isMobile"
         [class.mobile-layout]="isMobile"
         [class.dark-theme]="themeService.isDark()"
         [@fadeIn]>

      <!-- Mobile Overlay Backdrop -->
      <div class="mobile-backdrop"
           *ngIf="isMobile && mobileMenuOpen"
           (click)="closeMobileMenu()"
           [@fadeIn]></div>

      <!-- Sidebar -->
      <aside class="app-sidebar"
             [class.collapsed]="collapsed && !isMobile"
             [class.mobile-open]="isMobile && mobileMenuOpen"
             [@sidebarToggle]="(collapsed && !isMobile) ? 'collapsed' : 'expanded'">

        <!-- Sidebar Content -->
        <app-sidebar
          [collapsed]="collapsed && !isMobile"
          (businessSelectionChange)="onBusinessSelectionChange($event)"
          (toggleSidebar)="toggleSidebar()">
        </app-sidebar>
      </aside>

      <!-- Main Content Area -->
      <div class="app-main"
           [@slideIn]>
        <!-- Header -->
        <header class="app-header">
          <app-header
            [showMenuButton]="isMobile"
            (menuToggle)="toggleMobileMenu()">
          </app-header>
        </header>

        <!-- Content -->
        <main class="app-content">
          <router-outlet></router-outlet>
        </main>
      </div>
    </div>
  `,
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit {
  collapsed = false;
  isMobile = false;
  mobileMenuOpen = false;
  selectedBusinessIds: string[] = ['1'];

  private migrationService = inject(UserDataMigrationService);

  constructor(
    private businessService: BusinessService,
    public themeService: StaffManagerThemeService
  ) {
    if (typeof window !== 'undefined' && window.localStorage) {
      this.collapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    }
    this.updateResponsive();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.updateResponsive.bind(this));
    }
  }

  ngOnInit(): void {
    // Check and migrate user data if needed
    this.migrationService.checkAndMigrateUserData().subscribe({
      next: (success) => {
        if (success) {
          console.log('✅ User data check/migration completed');
        }
      },
      error: (error) => {
        console.error('❌ Error during user data migration:', error);
      }
    });
  }

  updateResponsive() {
    this.isMobile = typeof window !== 'undefined' ? window.innerWidth < 900 : false;
    if (this.isMobile) {
      this.collapsed = false;
    }
  }

  toggleSidebar() {
    console.log('🔥 LAYOUT RECEIVED TOGGLE - Current collapsed:', this.collapsed);
    this.collapsed = !this.collapsed;
    console.log('🔥 LAYOUT NEW STATE - Collapsed:', this.collapsed);
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem('sidebarCollapsed', String(this.collapsed));
    }
  }

  toggleMobileMenu() {
    this.mobileMenuOpen = !this.mobileMenuOpen;
  }

  closeMobileMenu() {
    this.mobileMenuOpen = false;
  }

  onBusinessSelectionChange(businessIds: string[]) {
    this.selectedBusinessIds = businessIds;
    // Update business service with new selection
    this.businessService.setSelectedBusinessIds(businessIds);
    console.log('Business selection changed:', businessIds);
  }
}