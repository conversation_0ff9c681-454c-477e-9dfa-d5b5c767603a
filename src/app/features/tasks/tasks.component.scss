.tasks-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .tasks-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      gap: 24px;

      .title-section {
        h1 {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0 0 8px 0;
          font-size: 2rem;
          font-weight: 500;
          color: var(--mdc-theme-on-surface);

          mat-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
            color: var(--mdc-theme-primary);
          }
        }

        .subtitle {
          margin: 0;
          color: var(--mdc-theme-on-surface-variant);
          font-size: 1rem;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        button {
          min-width: 140px;

          mat-icon {
            margin-right: 8px;
          }
        }
      }
    }

    .filters-section {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
      padding: 16px;
      background: var(--mdc-theme-surface-variant);
      border-radius: 8px;

      .search-field {
        flex: 1;
        min-width: 300px;
      }

      mat-form-field {
        min-width: 150px;
      }
    }
  }

  .loading-bar {
    margin-bottom: 16px;
  }

  .tasks-tabs {
    .mat-mdc-tab-group {
      --mdc-tab-indicator-active-indicator-color: var(--mdc-theme-primary);
    }

    .tab-content {
      padding-top: 24px;
    }
  }

  // Summary Cards
  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .summary-card {
      .summary-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .summary-icon {
          font-size: 2.5rem;
          width: 2.5rem;
          height: 2.5rem;

          &.pending {
            color: #ff9800;
          }

          &.in-progress {
            color: #2196f3;
          }

          &.completed {
            color: #4caf50;
          }

          &.overdue {
            color: #f44336;
          }
        }

        .summary-text {
          h3 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--mdc-theme-on-surface);
          }

          p {
            margin: 4px 0 0 0;
            color: var(--mdc-theme-on-surface-variant);
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  // Table Cards
  .tasks-table-card,
  .checklists-table-card {
    margin-bottom: 24px;

    mat-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;

        mat-icon {
          color: var(--mdc-theme-primary);
        }
      }

      .table-actions {
        display: flex;
        gap: 8px;
      }
    }

    .table-container {
      overflow-x: auto;
      margin-top: 16px;

      .tasks-table,
      .checklists-table {
        width: 100%;
        min-width: 800px;

        .mat-mdc-header-cell {
          font-weight: 600;
          color: var(--mdc-theme-on-surface);
          border-bottom: 2px solid var(--mdc-theme-outline-variant);
        }

        .mat-mdc-cell {
          border-bottom: 1px solid var(--mdc-theme-outline-variant);
          padding: 16px 8px;
        }

        .task-row,
        .checklist-row {
          &:hover {
            background-color: var(--mdc-theme-surface-variant);
          }
        }

        // Column-specific styles
        .task-title-cell,
        .checklist-title-cell {
          h4 {
            margin: 0 0 4px 0;
            font-weight: 500;
            color: var(--mdc-theme-on-surface);
          }

          p {
            margin: 0 0 8px 0;
            color: var(--mdc-theme-on-surface-variant);
            font-size: 0.9rem;
            line-height: 1.4;
          }

          .task-tags {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
            align-items: center;

            .task-tag {
              font-size: 0.75rem;
              height: 20px;
              line-height: 20px;
            }

            .more-tags {
              font-size: 0.75rem;
              color: var(--mdc-theme-on-surface-variant);
            }
          }

          .checklist-info {
            .item-count {
              font-size: 0.8rem;
              color: var(--mdc-theme-on-surface-variant);
              background: var(--mdc-theme-surface-variant);
              padding: 2px 8px;
              border-radius: 12px;
            }
          }
        }

        .assignees {
          font-size: 0.9rem;
          color: var(--mdc-theme-on-surface);
        }

        .due-date {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 0.9rem;

          &.overdue {
            color: #f44336;

            mat-icon {
              color: #f44336;
              font-size: 1rem;
              width: 1rem;
              height: 1rem;
            }
          }
        }

        .progress-cell,
        .completion-cell {
          display: flex;
          align-items: center;
          gap: 8px;

          mat-progress-bar {
            flex: 1;
            height: 8px;
          }

          .progress-text,
          .completion-text {
            font-size: 0.8rem;
            color: var(--mdc-theme-on-surface-variant);
            min-width: 35px;
          }
        }

        .delete-action {
          color: #f44336;

          mat-icon {
            color: #f44336;
          }
        }
      }
    }

    // Empty State
    .empty-state {
      text-align: center;
      padding: 48px 24px;
      color: var(--mdc-theme-on-surface-variant);

      mat-icon {
        font-size: 4rem;
        width: 4rem;
        height: 4rem;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      h3 {
        margin: 0 0 8px 0;
        font-weight: 500;
      }

      p {
        margin: 0 0 24px 0;
        font-size: 0.9rem;
      }
    }
  }

  // Analytics Section
  .analytics-section {
    .analytics-card {
      mat-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        mat-card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0;

          mat-icon {
            color: var(--mdc-theme-primary);
          }
        }

        .analytics-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .tasks-container {
    padding: 16px;

    .tasks-header {
      .header-content {
        flex-direction: column;
        align-items: stretch;

        .header-actions {
          justify-content: center;
        }
      }

      .filters-section {
        flex-direction: column;
        align-items: stretch;

        .search-field {
          min-width: unset;
        }

        mat-form-field {
          min-width: unset;
        }
      }
    }

    .summary-cards {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .table-container {
      .tasks-table,
      .checklists-table {
        min-width: 600px;
      }
    }
  }
}

@media (max-width: 480px) {
  .tasks-container {
    padding: 12px;

    .tasks-header {
      .header-content {
        .title-section {
          h1 {
            font-size: 1.5rem;

            mat-icon {
              font-size: 1.5rem;
              width: 1.5rem;
              height: 1.5rem;
            }
          }
        }

        .header-actions {
          button {
            min-width: unset;
            flex: 1;
          }
        }
      }
    }

    .summary-cards {
      grid-template-columns: 1fr 1fr;

      .summary-card {
        .summary-content {
          flex-direction: column;
          text-align: center;
          gap: 8px;

          .summary-icon {
            font-size: 2rem;
            width: 2rem;
            height: 2rem;
          }

          .summary-text {
            h3 {
              font-size: 1.4rem;
            }
          }
        }
      }
    }
  }
}
