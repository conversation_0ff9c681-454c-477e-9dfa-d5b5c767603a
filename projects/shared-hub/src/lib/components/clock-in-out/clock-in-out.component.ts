import { Component, Input, Output, EventEmitter, inject, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { Observable, Subject, combineLatest } from 'rxjs';
import { takeUntil, map } from 'rxjs/operators';
import { TimeTrackingService } from '../../services/time-tracking.service';
import { AuthService } from '../../services/auth.service';
import { TimeClockStatus, TimeEntry } from '../../models/time-entry.model';

@Component({
  selector: 'shared-clock-in-out',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <mat-card class="clock-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>access_time</mat-icon>
          Time Clock
        </mat-card-title>
        <mat-card-subtitle>{{ currentTime$ | async | date:'medium' }}</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="clock-status" *ngIf="timeClockStatus$ | async as status">
          <!-- Current Status Display -->
          <div class="status-display">
            <div class="status-indicator" [class.clocked-in]="status.isClocked" [class.clocked-out]="!status.isClocked">
              <mat-icon>{{ status.isClocked ? 'work' : 'work_off' }}</mat-icon>
              <span class="status-text">
                {{ status.isClocked ? 'Clocked In' : 'Clocked Out' }}
              </span>
            </div>

            <!-- Break Status -->
            <div class="break-status" *ngIf="status.onBreak">
              <mat-icon color="warn">pause_circle</mat-icon>
              <span>On Break</span>
            </div>
          </div>

          <!-- Elapsed Time -->
          <div class="elapsed-time" *ngIf="status.isClocked">
            <h2>{{ formatElapsedTime(elapsedTime$ | async) }}</h2>
            <span class="elapsed-label">Hours Today</span>
          </div>

          <!-- Today's Summary -->
          <div class="daily-summary">
            <div class="summary-item">
              <span class="label">Today:</span>
              <span class="value">{{ status.totalHoursToday | number:'1.2-2' }}h</span>
            </div>
            <div class="summary-item">
              <span class="label">This Week:</span>
              <span class="value">{{ status.totalHoursWeek | number:'1.2-2' }}h</span>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <!-- Clock In/Out Button -->
            <button
              mat-raised-button
              [color]="status.isClocked ? 'warn' : 'primary'"
              [disabled]="isLoading"
              (click)="toggleClock()"
              class="clock-button">
              <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
              <mat-icon *ngIf="!isLoading">{{ status.isClocked ? 'logout' : 'login' }}</mat-icon>
              {{ status.isClocked ? 'Clock Out' : 'Clock In' }}
            </button>

            <!-- Break Button -->
            <button
              mat-stroked-button
              color="accent"
              [disabled]="!status.isClocked || isLoading"
              (click)="toggleBreak()"
              class="break-button">
              <mat-icon>{{ status.onBreak ? 'play_circle' : 'pause_circle' }}</mat-icon>
              {{ status.onBreak ? 'End Break' : 'Start Break' }}
            </button>
          </div>

          <!-- Pending Issues -->
          <div class="pending-issues" *ngIf="status.pendingIssues.length > 0">
            <mat-icon color="warn">warning</mat-icon>
            <span>{{ status.pendingIssues.length }} pending issue(s)</span>
            <button mat-button color="warn" (click)="viewIssues()">View</button>
          </div>
        </div>

        <!-- Loading State -->
        <div class="loading-state" *ngIf="!(timeClockStatus$ | async)">
          <mat-spinner></mat-spinner>
          <p>Loading time clock...</p>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .clock-card {
      max-width: 400px;
      margin: 0 auto;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .clock-status {
      text-align: center;
      padding: 16px 0;
    }

    .status-display {
      margin-bottom: 24px;
    }

    .status-indicator {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 16px;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .status-indicator.clocked-in {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-indicator.clocked-out {
      background-color: #fafafa;
      color: #757575;
    }

    .status-indicator mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
    }

    .status-text {
      font-size: 18px;
      font-weight: 500;
    }

    .break-status {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      color: #f57c00;
    }

    .elapsed-time {
      margin-bottom: 24px;
    }

    .elapsed-time h2 {
      margin: 0;
      font-size: 36px;
      font-weight: 300;
      color: #1976d2;
    }

    .elapsed-label {
      color: #757575;
      font-size: 14px;
    }

    .daily-summary {
      display: flex;
      justify-content: space-around;
      margin-bottom: 24px;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 8px;
    }

    .summary-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
    }

    .summary-item .label {
      font-size: 12px;
      color: #757575;
      text-transform: uppercase;
    }

    .summary-item .value {
      font-size: 18px;
      font-weight: 500;
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 16px;
    }

    .clock-button {
      height: 56px;
      font-size: 16px;
      font-weight: 500;
    }

    .break-button {
      height: 40px;
    }

    .pending-issues {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background-color: #fff3e0;
      border-radius: 8px;
      color: #f57c00;
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      padding: 32px;
    }

    @media (max-width: 600px) {
      .clock-card {
        margin: 8px;
      }

      .daily-summary {
        flex-direction: column;
        gap: 12px;
      }

      .summary-item {
        flex-direction: row;
        justify-content: space-between;
      }
    }
  `]
})
export class ClockInOutComponent implements OnInit, OnDestroy {
  @Input() staffId?: string;
  @Input() showSummary = true;
  @Input() showBreakButton = true;
  @Input() compactMode = false;

  @Output() clockIn = new EventEmitter<TimeEntry>();
  @Output() clockOut = new EventEmitter<TimeEntry>();
  @Output() breakStart = new EventEmitter<void>();
  @Output() breakEnd = new EventEmitter<void>();
  @Output() issuesView = new EventEmitter<void>();

  private timeTrackingService = inject(TimeTrackingService);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);
  private destroy$ = new Subject<void>();

  // Observables
  timeClockStatus$!: Observable<TimeClockStatus | null>;
  currentTime$!: Observable<Date>;
  elapsedTime$!: Observable<number>;

  // Component state
  isLoading = false;

  ngOnInit(): void {
    this.timeClockStatus$ = this.timeTrackingService.timeClockStatus$;
    this.currentTime$ = this.timeTrackingService.currentTime$;
    this.elapsedTime$ = this.timeTrackingService.elapsedTime$;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async toggleClock(): Promise<void> {
    if (this.isLoading) return;

    this.isLoading = true;

    try {
      const staffId = this.staffId || await this.authService.getCurrentStaffId().toPromise();
      if (!staffId) {
        throw new Error('No staff ID available');
      }

      const status = await this.timeTrackingService.timeClockStatus$.pipe(takeUntil(this.destroy$)).toPromise();

      if (status?.isClocked) {
        // Clock out
        const location = await this.getCurrentLocation();
        const timeEntry = await this.timeTrackingService.clockOut(staffId, location);
        this.clockOut.emit(timeEntry);
        this.showMessage('Successfully clocked out');
      } else {
        // Clock in
        const location = await this.getCurrentLocation();
        const deviceInfo = this.getDeviceInfo();
        const timeEntry = await this.timeTrackingService.clockIn(staffId, location, deviceInfo);
        this.clockIn.emit(timeEntry);
        this.showMessage('Successfully clocked in');
      }
    } catch (error) {
      console.error('Clock toggle error:', error);
      this.showMessage(`Error: ${error instanceof Error ? error.message : String(error)}`, 'error');
    } finally {
      this.isLoading = false;
    }
  }

  async toggleBreak(): Promise<void> {
    if (this.isLoading) return;

    this.isLoading = true;

    try {
      const staffId = this.staffId || await this.authService.getCurrentStaffId().toPromise();
      if (!staffId) {
        throw new Error('No staff ID available');
      }

      const status = await this.timeTrackingService.timeClockStatus$.pipe(takeUntil(this.destroy$)).toPromise();

      if (status?.onBreak) {
        // End break
        await this.timeTrackingService.endBreak(staffId);
        this.breakEnd.emit();
        this.showMessage('Break ended');
      } else {
        // Start break
        await this.timeTrackingService.startBreak(staffId);
        this.breakStart.emit();
        this.showMessage('Break started');
      }
    } catch (error) {
      console.error('Break toggle error:', error);
      this.showMessage(`Error: ${error instanceof Error ? error.message : String(error)}`, 'error');
    } finally {
      this.isLoading = false;
    }
  }

  viewIssues(): void {
    this.issuesView.emit();
  }

  formatElapsedTime(seconds: number | null): string {
    if (!seconds) return '0:00:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  private async getCurrentLocation(): Promise<GeolocationPosition | undefined> {
    if (!navigator.geolocation) {
      return undefined;
    }

    return new Promise((resolve) => {
      navigator.geolocation.getCurrentPosition(
        (position) => resolve(position),
        (error) => {
          console.warn('Geolocation error:', error);
          resolve(undefined);
        },
        { timeout: 10000, enableHighAccuracy: false }
      );
    });
  }

  private getDeviceInfo(): any {
    return {
      deviceType: this.getDeviceType(),
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
  }

  private getDeviceType(): string {
    const userAgent = navigator.userAgent.toLowerCase();

    if (/tablet|ipad|playbook|silk/.test(userAgent)) {
      return 'tablet';
    }

    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/.test(userAgent)) {
      return 'mobile';
    }

    return 'desktop';
  }

  private showMessage(message: string, type: 'success' | 'error' = 'success'): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: type === 'error' ? 'error-snackbar' : 'success-snackbar'
    });
  }
}
