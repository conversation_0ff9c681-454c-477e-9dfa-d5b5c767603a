import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Business {
  id: string;
  name: string;
  logoUrl?: string;
}

export interface BusinessData {
  staffOnShift: number;
  upcomingShifts: number;
  notifications: number;
  messages: number;
  staffList: Array<{name: string, role: string}>;
  scheduleList: Array<{time: string, staff: string}>;
  notificationList: Array<{title: string, time: string}>;
  messageList: Array<{title: string, meta: string}>;
}

@Injectable({
  providedIn: 'root'
})
export class BusinessService {
  private selectedBusinessIdsSubject = new BehaviorSubject<string[]>(['1']);
  public selectedBusinessIds$ = this.selectedBusinessIdsSubject.asObservable();

  private businessDataSubject = new BehaviorSubject<BusinessData>(this.getDefaultData());
  public businessData$ = this.businessDataSubject.asObservable();

  private businesses: Business[] = [
    { id: '1', name: 'S&E Jewelers', logoUrl: '' },
    { id: '2', name: 'Diamond District', logoUrl: '' },
    { id: '3', name: 'Gold Rush', logoUrl: '' },
    { id: '4', name: 'Precious Gems Co.', logoUrl: '' }
  ];

  constructor() {}

  getBusinesses(): Business[] {
    return this.businesses;
  }

  getSelectedBusinessIds(): string[] {
    return this.selectedBusinessIdsSubject.value;
  }

  setSelectedBusinessIds(businessIds: string[]): void {
    this.selectedBusinessIdsSubject.next(businessIds);
    this.updateBusinessData(businessIds);
  }

  private updateBusinessData(businessIds: string[]): void {
    // Simulate different data based on selected businesses
    const data = this.generateBusinessData(businessIds);
    this.businessDataSubject.next(data);
  }

  private generateBusinessData(businessIds: string[]): BusinessData {
    if (businessIds.length > 1) {
      // OneView mode - aggregated data
      return {
        staffOnShift: 12,
        upcomingShifts: 18,
        notifications: 7,
        messages: 15,
        staffList: [
          {name: 'John Doe', role: '(Front Desk)'},
          {name: 'Jane Smith', role: '(Manager)'},
          {name: 'Bob Johnson', role: '(Technician)'},
          {name: 'Alice Williams', role: '(Support)'},
          {name: 'Charlie Brown', role: '(Sales)'},
          {name: 'David Miller', role: '(Security)'},
          {name: 'Eva Garcia', role: '(Supervisor)'}
        ],
        scheduleList: [
          {time: '8:00 AM - 4:00 PM:', staff: 'John Doe'},
          {time: '9:00 AM - 5:00 PM:', staff: 'Jane Smith'},
          {time: '10:00 AM - 6:00 PM:', staff: 'Bob Johnson'},
          {time: '11:00 AM - 7:00 PM:', staff: 'Alice Williams'},
          {time: '12:00 PM - 8:00 PM:', staff: 'Charlie Brown'},
          {time: '1:00 PM - 9:00 PM:', staff: 'David Miller'}
        ],
        notificationList: [
          {title: 'Multi-location schedule published', time: '5 minutes ago'},
          {title: 'Staff shortage at Diamond District', time: '30 minutes ago'},
          {title: 'System maintenance scheduled', time: '1 hour ago'},
          {title: 'New policy update', time: '2 hours ago'}
        ],
        messageList: [
          {title: 'Cross-location transfer request', meta: 'From: John Doe • 3 minutes ago'},
          {title: 'Multi-site meeting tomorrow', meta: 'From: Jane Smith • 20 minutes ago'},
          {title: 'Equipment maintenance update', meta: 'From: Bob Johnson • 45 minutes ago'},
          {title: 'OneView training session', meta: 'From: Alice Williams • 1 hour ago'}
        ]
      };
    } else {
      // Single business data
      const businessId = businessIds[0];
      const business = this.businesses.find(b => b.id === businessId);
      
      if (business?.name === 'S&E Jewelers') {
        return this.getDefaultData();
      } else {
        return this.getAlternateData(business?.name || 'Unknown');
      }
    }
  }

  private getDefaultData(): BusinessData {
    return {
      staffOnShift: 5,
      upcomingShifts: 8,
      notifications: 3,
      messages: 7,
      staffList: [
        {name: 'John Doe', role: '(Front Desk)'},
        {name: 'Jane Smith', role: '(Manager)'},
        {name: 'Bob Johnson', role: '(Technician)'},
        {name: 'Alice Williams', role: '(Support)'},
        {name: 'Charlie Brown', role: '(Sales)'}
      ],
      scheduleList: [
        {time: '9:00 AM - 5:00 PM:', staff: 'John Doe'},
        {time: '10:00 AM - 6:00 PM:', staff: 'Jane Smith'},
        {time: '12:00 PM - 8:00 PM:', staff: 'Bob Johnson'},
        {time: '2:00 PM - 10:00 PM:', staff: 'Alice Williams'},
        {time: '4:00 PM - 12:00 AM:', staff: 'Charlie Brown'},
        {time: '6:00 PM - 2:00 AM:', staff: 'David Miller'}
      ],
      notificationList: [
        {title: 'New schedule published', time: '10 minutes ago'},
        {title: 'Staff shortage on Friday', time: '1 hour ago'},
        {title: 'Urgent: System maintenance', time: '2 hours ago'}
      ],
      messageList: [
        {title: 'Schedule change request', meta: 'From: John Doe • 5 minutes ago'},
        {title: 'Team meeting tomorrow', meta: 'From: Jane Smith • 30 minutes ago'},
        {title: 'Equipment maintenance', meta: 'From: Bob Johnson • 1 hour ago'},
        {title: 'New client onboarding', meta: 'From: Alice Williams • 2 hours ago'}
      ]
    };
  }

  private getAlternateData(businessName: string): BusinessData {
    return {
      staffOnShift: 3,
      upcomingShifts: 6,
      notifications: 2,
      messages: 4,
      staffList: [
        {name: 'Mike Wilson', role: '(Manager)'},
        {name: 'Sarah Davis', role: '(Sales)'},
        {name: 'Tom Anderson', role: '(Support)'}
      ],
      scheduleList: [
        {time: '8:00 AM - 4:00 PM:', staff: 'Mike Wilson'},
        {time: '12:00 PM - 8:00 PM:', staff: 'Sarah Davis'},
        {time: '4:00 PM - 12:00 AM:', staff: 'Tom Anderson'}
      ],
      notificationList: [
        {title: `${businessName} schedule updated`, time: '15 minutes ago'},
        {title: 'Inventory check required', time: '3 hours ago'}
      ],
      messageList: [
        {title: 'Shift coverage needed', meta: 'From: Mike Wilson • 10 minutes ago'},
        {title: 'Customer feedback', meta: 'From: Sarah Davis • 1 hour ago'},
        {title: 'Training reminder', meta: 'From: Tom Anderson • 2 hours ago'}
      ]
    };
  }
}
