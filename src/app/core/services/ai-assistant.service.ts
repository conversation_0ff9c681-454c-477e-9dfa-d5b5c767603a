import { Injectable, inject } from '@angular/core';
import { Observable, from, map, switchMap, combineLatest } from 'rxjs';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { StaffFirestoreService } from '../../features/staff/services/staff-firestore.service';
import { CalendarService } from '../../features/calendar/services/calendar.service';
import { StaffMember, StaffTask, StaffGoalExtended } from '../../features/staff/models/staff.model';
import { CalendarEvent } from '../../features/calendar/models/calendar.model';
import { environment } from '../../../environments/environment';

export interface AIRecommendation {
  type: 'scheduling' | 'task-assignment' | 'goal-optimization' | 'workload-balance';
  title: string;
  description: string;
  confidence: number; // 0-1
  actionItems: string[];
  affectedStaff?: string[];
  priority: 'low' | 'medium' | 'high';
}

export interface SchedulingSuggestion {
  staffId: string;
  staffName: string;
  suggestedTime: Date;
  reason: string;
  confidence: number;
}

@Injectable({
  providedIn: 'root'
})
export class AIAssistantService {
  private staffService = inject(StaffFirestoreService);
  private calendarService = inject(CalendarService);
  private genAI: GoogleGenerativeAI;

  constructor() {
    // Initialize Google AI with environment API key
    this.genAI = new GoogleGenerativeAI(environment.googleAI.apiKey);
  }

  // Smart Staff Scheduling Recommendations
  getSchedulingRecommendations(businessId: string, date: Date): Observable<SchedulingSuggestion[]> {
    return combineLatest([
      this.staffService.getActiveStaff(businessId),
      this.calendarService.getEvents(businessId, date, date)
    ]).pipe(
      map(([staff, events]) => this.analyzeSchedulingNeeds(staff, events, date))
    );
  }

  // Intelligent Task Assignment
  suggestTaskAssignment(task: Partial<StaffTask>, businessId: string): Observable<SchedulingSuggestion[]> {
    return this.staffService.getActiveStaff(businessId).pipe(
      switchMap(staff => this.analyzeTaskFit(task, staff))
    );
  }

  // Goal Progress Analysis and Recommendations
  analyzeGoalProgress(staffId: string): Observable<AIRecommendation[]> {
    return combineLatest([
      this.staffService.subscribeToStaffGoals(staffId),
      this.staffService.subscribeToStaffTasks(staffId)
    ]).pipe(
      map(([goals, tasks]) => this.generateGoalRecommendations(goals, tasks))
    );
  }

  // Workload Balance Analysis
  analyzeWorkloadBalance(businessId: string): Observable<AIRecommendation[]> {
    return combineLatest([
      this.staffService.getActiveStaff(businessId),
      this.calendarService.getEvents(businessId, new Date(), this.getDateInDays(7))
    ]).pipe(
      map(([staff, events]) => this.analyzeWorkloadDistribution(staff, events))
    );
  }

  // AI-Powered Chat Assistant for Staff Management
  async askStaffManagementQuestion(question: string, context: any): Promise<string> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 2048,
        }
      });

      const prompt = `
        You are an expert AI assistant for StaffManager, a comprehensive staff management application.
        You have deep expertise in human resources, team management, scheduling optimization, and workplace productivity.

        Current Context:
        ${JSON.stringify(context, null, 2)}

        User Question: ${question}

        Instructions:
        - Provide specific, actionable advice tailored to the context
        - Consider staff workload, skills, availability, and performance data
        - Suggest concrete steps and best practices
        - Include relevant metrics or KPIs when applicable
        - Be concise but comprehensive
        - If suggesting schedule changes, consider work-life balance
        - For task assignments, match skills to requirements
        - For goal setting, ensure SMART criteria (Specific, Measurable, Achievable, Relevant, Time-bound)

        Response format: Provide a clear, structured answer with bullet points for action items when appropriate.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('AI Assistant Error:', error);
      return 'I apologize, but I cannot process your request at the moment. Please try again later.';
    }
  }

  // Generate automated shift schedules with AI optimization
  generateOptimalSchedule(
    staff: StaffMember[],
    requirements: any,
    dateRange: { start: Date; end: Date }
  ): Observable<CalendarEvent[]> {
    return from(this.calculateOptimalScheduleWithAI(staff, requirements, dateRange));
  }

  // Advanced AI-powered staff performance analysis
  async analyzeStaffPerformance(staffData: any[], performanceMetrics: any): Promise<string> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.3, // Lower temperature for more analytical responses
          topP: 0.9,
          maxOutputTokens: 3000,
        }
      });

      const prompt = `
        As an expert HR analytics consultant, analyze the following staff performance data and provide insights:

        Staff Data: ${JSON.stringify(staffData, null, 2)}
        Performance Metrics: ${JSON.stringify(performanceMetrics, null, 2)}

        Please provide:
        1. Performance trends and patterns
        2. Top performers and areas of excellence
        3. Staff members who may need additional support
        4. Recommendations for performance improvement
        5. Training and development suggestions
        6. Workload optimization opportunities

        Format your response with clear sections and actionable recommendations.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Performance Analysis Error:', error);
      return 'Unable to analyze performance data at this time.';
    }
  }

  // AI-powered conflict resolution and team dynamics analysis
  async analyzeTeamDynamics(teamData: any, conflictReports?: any[]): Promise<string> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.6,
          topP: 0.85,
          maxOutputTokens: 2500,
        }
      });

      const prompt = `
        As an organizational psychology expert, analyze the team dynamics and provide recommendations:

        Team Data: ${JSON.stringify(teamData, null, 2)}
        ${conflictReports ? `Conflict Reports: ${JSON.stringify(conflictReports, null, 2)}` : ''}

        Analyze:
        1. Team collaboration patterns
        2. Communication effectiveness
        3. Potential conflict areas
        4. Leadership dynamics
        5. Team cohesion indicators
        6. Recommendations for improvement

        Provide specific, actionable strategies for enhancing team performance and resolving any identified issues.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Team Dynamics Analysis Error:', error);
      return 'Unable to analyze team dynamics at this time.';
    }
  }

  // Predictive analytics for staffing needs
  async predictStaffingNeeds(historicalData: any, businessProjections: any): Promise<string> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.4,
          topP: 0.9,
          maxOutputTokens: 2000,
        }
      });

      const prompt = `
        As a workforce planning specialist, analyze the data and predict future staffing needs:

        Historical Data: ${JSON.stringify(historicalData, null, 2)}
        Business Projections: ${JSON.stringify(businessProjections, null, 2)}

        Provide predictions for:
        1. Optimal staffing levels for the next 3-6 months
        2. Skills gaps that may emerge
        3. Seasonal staffing adjustments needed
        4. Recommended hiring timeline
        5. Training and development priorities
        6. Budget implications for staffing changes

        Include confidence levels for your predictions and alternative scenarios.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Staffing Prediction Error:', error);
      return 'Unable to generate staffing predictions at this time.';
    }
  }

  // Private helper methods
  private analyzeSchedulingNeeds(staff: StaffMember[], events: CalendarEvent[], date: Date): SchedulingSuggestion[] {
    const suggestions: SchedulingSuggestion[] = [];

    // Analyze staff availability and workload
    staff.forEach(member => {
      const memberEvents = events.filter(e => e.assignedStaff.includes(member.id || ''));
      const workload = this.calculateWorkload(memberEvents);

      if (workload < 0.7) { // Less than 70% capacity
        suggestions.push({
          staffId: member.id || '',
          staffName: `${member.firstName} ${member.lastName}`,
          suggestedTime: new Date(date.getTime() + 9 * 60 * 60 * 1000), // 9 AM
          reason: 'Available capacity for additional shifts',
          confidence: 0.8
        });
      }
    });

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  private async analyzeTaskFit(task: Partial<StaffTask>, staff: StaffMember[]): Promise<SchedulingSuggestion[]> {
    const suggestions: SchedulingSuggestion[] = [];

    for (const member of staff) {
      let score = 0;
      let reasons: string[] = [];

      // Skill matching
      if (task.category && member.skills) {
        const relevantSkills = member.skills.filter(skill =>
          skill.category.toLowerCase().includes(task.category?.toLowerCase() || '')
        );
        if (relevantSkills.length > 0) {
          score += 0.4;
          reasons.push('Has relevant skills');
        }
      }

      // Workload consideration
      const currentTasks = await this.staffService.subscribeToStaffTasks(member.id || '').toPromise();
      const activeTasks = currentTasks?.filter(t => t.status === 'in-progress' || t.status === 'pending') || [];

      if (activeTasks.length < 3) {
        score += 0.3;
        reasons.push('Low current workload');
      }

      // Availability
      if (member.status === 'active') {
        score += 0.3;
        reasons.push('Currently active');
      }

      if (score > 0.5) {
        suggestions.push({
          staffId: member.id || '',
          staffName: `${member.firstName} ${member.lastName}`,
          suggestedTime: new Date(),
          reason: reasons.join(', '),
          confidence: score
        });
      }
    }

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  private generateGoalRecommendations(goals: StaffGoalExtended[], tasks: StaffTask[]): AIRecommendation[] {
    const recommendations: AIRecommendation[] = [];

    goals.forEach(goal => {
      if (goal.progress < 50 && this.isNearDeadline(goal.targetDate)) {
        recommendations.push({
          type: 'goal-optimization',
          title: `Goal "${goal.title}" needs attention`,
          description: `This goal is ${goal.progress}% complete with deadline approaching`,
          confidence: 0.9,
          actionItems: [
            'Break down remaining work into smaller tasks',
            'Allocate more time daily to this goal',
            'Consider extending deadline if necessary'
          ],
          priority: 'high'
        });
      }
    });

    return recommendations;
  }

  private analyzeWorkloadDistribution(staff: StaffMember[], events: CalendarEvent[]): AIRecommendation[] {
    const recommendations: AIRecommendation[] = [];
    const workloads = new Map<string, number>();

    // Calculate workload for each staff member
    staff.forEach(member => {
      const memberEvents = events.filter(e => e.assignedStaff.includes(member.id || ''));
      workloads.set(member.id || '', this.calculateWorkload(memberEvents));
    });

    // Find imbalances
    const avgWorkload = Array.from(workloads.values()).reduce((a, b) => a + b, 0) / workloads.size;
    const overloaded = Array.from(workloads.entries()).filter(([_, load]) => load > avgWorkload * 1.3);
    const underloaded = Array.from(workloads.entries()).filter(([_, load]) => load < avgWorkload * 0.7);

    if (overloaded.length > 0 && underloaded.length > 0) {
      recommendations.push({
        type: 'workload-balance',
        title: 'Workload imbalance detected',
        description: 'Some staff members are overloaded while others have capacity',
        confidence: 0.85,
        actionItems: [
          'Redistribute tasks from overloaded to underloaded staff',
          'Review scheduling patterns',
          'Consider hiring additional staff if consistently overloaded'
        ],
        affectedStaff: [...overloaded.map(([id]) => id), ...underloaded.map(([id]) => id)],
        priority: 'medium'
      });
    }

    return recommendations;
  }

  private async calculateOptimalScheduleWithAI(
    staff: StaffMember[],
    requirements: any,
    dateRange: { start: Date; end: Date }
  ): Promise<CalendarEvent[]> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.2, // Low temperature for consistent scheduling
          topP: 0.9,
          maxOutputTokens: 4000,
        }
      });

      const prompt = `
        As an expert workforce scheduling consultant, create an optimal schedule based on the following data:

        Staff Information: ${JSON.stringify(staff.map(s => ({
          id: s.id,
          name: `${s.firstName} ${s.lastName}`,
          position: s.position,
          skills: s.skills?.map(skill => skill.name) || [],
          availability: s.availability,
          hourlyRate: s.hourlyRate,
          status: s.status
        })), null, 2)}

        Requirements: ${JSON.stringify(requirements, null, 2)}
        Date Range: ${dateRange.start.toISOString()} to ${dateRange.end.toISOString()}

        Create a schedule that:
        1. Maximizes staff utilization while respecting availability
        2. Matches skills to requirements
        3. Ensures fair distribution of shifts
        4. Considers cost optimization
        5. Maintains work-life balance
        6. Covers all required time slots

        Return a JSON array of shift assignments with this structure:
        [
          {
            "staffId": "staff-id",
            "staffName": "Full Name",
            "date": "YYYY-MM-DD",
            "startTime": "HH:MM",
            "endTime": "HH:MM",
            "position": "role",
            "reasoning": "why this assignment is optimal"
          }
        ]

        Only return the JSON array, no additional text.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const scheduleData = JSON.parse(response.text());

      // Convert AI response to CalendarEvent format
      const events: CalendarEvent[] = scheduleData.map((shift: any, index: number) => {
        const shiftDate = new Date(shift.date);
        const [startHour, startMinute] = shift.startTime.split(':').map(Number);
        const [endHour, endMinute] = shift.endTime.split(':').map(Number);

        const startTime = new Date(shiftDate);
        startTime.setHours(startHour, startMinute, 0, 0);

        const endTime = new Date(shiftDate);
        endTime.setHours(endHour, endMinute, 0, 0);

        return {
          id: `ai-schedule-${Date.now()}-${index}`,
          title: `${shift.position} - ${shift.staffName}`,
          start: startTime,
          end: endTime,
          type: 'shift',
          status: 'scheduled',
          assignedStaff: [shift.staffId],
          createdBy: 'ai-assistant',
          businessId: staff[0]?.primaryBusinessId || '',
          description: shift.reasoning,
          createdAt: new Date(),
          updatedAt: new Date()
        };
      });

      return events;
    } catch (error) {
      console.error('AI Scheduling Error:', error);
      // Fallback to simple scheduling
      return this.calculateSimpleSchedule(staff, requirements, dateRange);
    }
  }

  private calculateSimpleSchedule(
    staff: StaffMember[],
    requirements: any,
    dateRange: { start: Date; end: Date }
  ): CalendarEvent[] {
    const events: CalendarEvent[] = [];
    let staffIndex = 0;
    const currentDate = new Date(dateRange.start);

    while (currentDate <= dateRange.end) {
      if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) { // Weekdays only
        const selectedStaff = staff[staffIndex % staff.length];

        events.push({
          id: `simple-${Date.now()}-${staffIndex}`,
          title: `Shift - ${selectedStaff.firstName} ${selectedStaff.lastName}`,
          start: new Date(currentDate.getTime() + 9 * 60 * 60 * 1000), // 9 AM
          end: new Date(currentDate.getTime() + 17 * 60 * 60 * 1000), // 5 PM
          type: 'shift',
          status: 'scheduled',
          assignedStaff: [selectedStaff.id || ''],
          createdBy: 'ai-assistant-fallback',
          businessId: selectedStaff.primaryBusinessId,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        staffIndex++;
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return events;
  }

  private calculateWorkload(events: CalendarEvent[]): number {
    // Calculate workload as a percentage of full capacity
    const totalHours = events.reduce((sum, event) => {
      const duration = (event.end.getTime() - event.start.getTime()) / (1000 * 60 * 60);
      return sum + duration;
    }, 0);

    const maxWeeklyHours = 40;
    return Math.min(totalHours / maxWeeklyHours, 1);
  }

  private isNearDeadline(targetDate: Date): boolean {
    const now = new Date();
    const daysUntilDeadline = (targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
    return daysUntilDeadline <= 7; // Within 7 days
  }

  private getDateInDays(days: number): Date {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date;
  }
}
