import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface DashboardWidget {
  id: string;
  title: string;
  settings?: any;
}

@Injectable({ providedIn: 'root' })
export class DashboardStateService {
  private widgetsSubject = new BehaviorSubject<DashboardWidget[]>([
    { id: 'staff-on-shift', title: 'Staff On Shift' },
    { id: 'upcoming-shifts', title: 'Upcoming Shifts' },
    { id: 'notifications', title: 'Notifications' },
    { id: 'messages', title: 'Messages' }
  ]);
  widgets$ = this.widgetsSubject.asObservable();

  private editModeSubject = new BehaviorSubject<boolean>(false);
  editMode$ = this.editModeSubject.asObservable();

  setWidgets(widgets: DashboardWidget[]) {
    this.widgetsSubject.next(widgets);
    // TODO: Persist to localStorage
  }

  setEditMode(editMode: boolean) {
    this.editModeSubject.next(editMode);
  }

  updateWidgetSettings(widgetId: string, settings: any) {
    const widgets = this.widgetsSubject.getValue().map(w =>
      w.id === widgetId ? { ...w, settings } : w
    );
    this.setWidgets(widgets);
  }
} 