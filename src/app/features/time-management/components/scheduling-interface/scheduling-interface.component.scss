.scheduling-container {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;

  .scheduling-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 4px;

    h2 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      color: #333;

      mat-icon {
        color: #1976d2;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      button {
        mat-icon, mat-spinner {
          margin-right: 8px;
        }
      }
    }
  }

  .scheduling-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;

    ::ng-deep .mat-mdc-tab-group {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    ::ng-deep .mat-mdc-tab-body-wrapper {
      flex: 1;
      overflow: hidden;
    }

    ::ng-deep .mat-mdc-tab-body {
      height: 100%;
      overflow: auto;
    }

    .tab-content {
      padding: 24px 0;
      height: 100%;
      overflow: auto;
    }

    // Manual Scheduling Tab
    .manual-scheduling-section {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .scheduling-controls {
        .schedule-form {
          .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;

            mat-form-field {
              flex: 1;
            }
          }
        }

        mat-card-actions {
          display: flex;
          gap: 12px;

          button {
            mat-icon {
              margin-right: 8px;
            }
          }
        }
      }

      .schedule-grid-card {
        flex: 1;

        .schedule-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
          text-align: center;
          color: #666;

          mat-icon {
            font-size: 64px;
            width: 64px;
            height: 64px;
            color: #1976d2;
            margin-bottom: 16px;
          }

          p {
            margin: 0;
            font-size: 1rem;
          }
        }
      }
    }

    // AI Scheduling Tab
    .ai-scheduling-section {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .ai-controls {
        .ai-form {
          .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;

            mat-form-field {
              flex: 1;
            }
          }

          .ai-features {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #1976d2;

            h4 {
              margin: 0 0 12px 0;
              font-size: 0.875rem;
              font-weight: 600;
              color: #1976d2;
            }

            .feature-list {
              display: flex;
              flex-direction: column;
              gap: 8px;

              .feature-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 0.875rem;

                mat-icon {
                  font-size: 18px;
                  width: 18px;
                  height: 18px;
                  color: #4caf50;
                }
              }
            }
          }
        }

        mat-card-actions {
          display: flex;
          gap: 12px;

          button {
            mat-icon, mat-spinner {
              margin-right: 8px;
            }
          }
        }
      }

      .ai-results {
        border: 2px solid #1976d2;
        background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);

        mat-card-header {
          mat-card-title {
            color: #1976d2;
          }

          mat-card-subtitle {
            color: #666;
            font-weight: 500;
          }
        }

        .ai-insights {
          margin-bottom: 24px;
          padding: 16px;
          background: white;
          border-radius: 8px;
          border-left: 4px solid #4caf50;

          h4 {
            margin: 0 0 12px 0;
            font-size: 1rem;
            font-weight: 600;
            color: #4caf50;
          }

          ::ng-deep {
            ul {
              margin: 8px 0;
              padding-left: 20px;

              li {
                margin-bottom: 8px;
                color: #555;
              }
            }

            strong {
              color: #333;
            }
          }
        }

        .schedule-preview {
          padding: 16px;
          background: white;
          border-radius: 8px;
          text-align: center;
          color: #666;
        }

        mat-card-actions {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;

          button {
            flex: 1;
            min-width: 120px;

            mat-icon {
              margin-right: 8px;
            }
          }
        }
      }
    }

    // Templates Tab
    .templates-section {
      .templates-card {
        .templates-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
          text-align: center;
          color: #666;

          mat-icon {
            font-size: 64px;
            width: 64px;
            height: 64px;
            color: #1976d2;
            margin-bottom: 16px;
          }

          p {
            margin: 0 0 24px 0;
            font-size: 1rem;
          }

          button {
            mat-icon {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .scheduling-container {
    .scheduling-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .scheduling-tabs {
      .manual-scheduling-section,
      .ai-scheduling-section {
        .form-row {
          flex-direction: column;
          gap: 12px;
        }
      }

      .ai-results {
        mat-card-actions {
          button {
            font-size: 0.875rem;
          }
        }
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .scheduling-container {
    .scheduling-header h2 {
      color: #e0e0e0;
    }

    .scheduling-tabs {
      .ai-controls .ai-features {
        background: #424242;
        border-left-color: #42a5f5;

        h4 {
          color: #42a5f5;
        }
      }

      .ai-results {
        background: linear-gradient(135deg, #1e3a8a 0%, #424242 100%);
        border-color: #42a5f5;

        .ai-insights {
          background: #424242;
          border-left-color: #66bb6a;

          h4 {
            color: #66bb6a;
          }
        }

        .schedule-preview {
          background: #424242;
          color: #bbb;
        }
      }
    }
  }
}
