import { Component, inject } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from './core/auth/auth.service';
import { Auth } from '@angular/fire/auth';
import { Firestore, doc, setDoc } from '@angular/fire/firestore';

@Component({
  selector: 'app-simple-auth-fix',
  standalone: true,
  imports: [
    ButtonModule,
    CommonModule, FormsModule
  ],
  template: `
    <div style="padding: 20px; max-width: 500px; margin: 50px auto; border: 2px solid #f44336; border-radius: 8px; background: #fff;">
      <h2 style="color: #f44336; text-align: center;">🚨 Emergency Authentication Fix</h2>

      <div style="margin: 20px 0;">
        <h3>Step 1: Login</h3>
        <input type="email" [(ngModel)]="email" placeholder="Email"
               style="width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ccc; border-radius: 4px;"
               value="<EMAIL>">
        <input type="password" [(ngModel)]="password" placeholder="Password"
               style="width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ccc; border-radius: 4px;">
        <button (click)="login()" [disabled]="loading"
                style="width: 100%; padding: 12px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">
          {{ loading ? 'Logging in...' : 'Emergency Login' }}
        </p-button>
      </div>

      <div style="margin: 20px 0;">
        <h3>Step 2: Fix Profile</h3>
        <button (click)="createProfile()" [disabled]="loading"
                style="width: 100%; padding: 12px; background: #ff9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
          {{ loading ? 'Creating...' : 'Create Missing Profile' }}
        </p-button>
      </div>

      <div style="margin: 20px 0;">
        <h3>Step 3: Test Access</h3>
        <button (click)="testDashboard()"
                style="width: 100%; padding: 12px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Go to Dashboard
        </p-button>
      </div>

      <div *ngIf="messages.length > 0" style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
        <h4>Status:</h4>
        <div *ngFor="let msg of messages"
             [style.color]="msg.includes('Error') || msg.includes('Failed') ? 'red' : 'green'"
             style="margin: 5px 0;">
          {{ msg }}
        </div>
    </div>
  `
})
export class SimpleAuthFixComponent {
  private authService = inject(AuthService);
  private router = inject(Router);
  private auth = inject(Auth);
  private firestore = inject(Firestore);

  email = '<EMAIL>';
  password = '';
  loading = false;
  messages: string[] = [];

  async login() {
    if (!this.password) {
      this.addMessage('Error: Please enter password');
      return;
    }

    this.loading = true;
    this.addMessage('Attempting login...');

    try {
      this.authService.signInWithEmail(this.email, this.password).subscribe({
        next: (profile) => {
          if (profile) {
            this.addMessage('✅ Login successful!');
            this.addMessage('✅ Profile loaded successfully!');
            this.addMessage('Ready to use dashboard');
          } else {
            this.addMessage('⚠️ Login successful but no profile found');
            this.addMessage('Use "Create Missing Profile" button');
          }
          this.loading = false;
        },
        error: (error) => {
          this.addMessage(`❌ Login failed: ${error.message}`);
          this.loading = false;
        }
      });
    } catch (error: any) {
      this.addMessage(`❌ Login error: ${error.message}`);
      this.loading = false;
    }
  }

  async createProfile() {
    this.loading = true;
    this.addMessage('Creating missing profile...');

    try {
      const user = this.auth.currentUser;
      if (!user) {
        this.addMessage('❌ No authenticated user found. Login first.');
        this.loading = false;
        return;
      }

      // Create profile
      const userProfile = {
        uid: user.uid,
        email: user.email!,
        displayName: user.displayName || 'User',
        role: 'admin',
        businessIds: [],
        primaryBusinessId: '',
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      const userDoc = doc(this.firestore, `users/${user.uid}`);
      await setDoc(userDoc, userProfile);

      this.addMessage('✅ Profile created successfully!');
      this.addMessage('✅ All features should now work');
      this.loading = false;

    } catch (error: any) {
      this.addMessage(`❌ Profile creation failed: ${error.message}`);
      this.loading = false;
    }
  }

  testDashboard() {
    this.addMessage('Navigating to dashboard...');
    this.router.navigate(['/dashboard']);
  }

  private addMessage(message: string) {
    this.messages.unshift(`${new Date().toLocaleTimeString()}: ${message}`);
    console.log('🚨 Auth Fix:', message);
  }
}
