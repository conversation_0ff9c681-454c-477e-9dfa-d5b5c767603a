import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';

import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { buttonPressAnimation, fadeInAnimation } from '../../../core/animations/staffmanager-animations';
import { IconService } from '../../../core/services/icon.service';

@Component({
  selector: 'app-modern-button',
  standalone: true,
  imports: [
    CommonModule, ButtonModule ProgressSpinnerModule
  ],
  animations: [buttonPressAnimation, fadeInAnimation],
  template: `
    <button [type]="type"
            [disabled]="disabled || loading"
            [class]="buttonClasses"
            (click)="onClick()"
            [@buttonPress]
            [@fadeIn]>
      
      <!-- Loading Spinner -->
      <mat-spinner *ngIf="loading" 
                   diameter="20" 
                   strokeWidth="3"
                   class="button-spinner">
      </mat-spinner>
      
      <!-- Icon -->
      <i class="pi pi-circle"></i>
      
      <!-- Button Text -->
      <span *ngIf="!iconOnly" class="button-text">
        <ng-content></ng-content>
      </span>
      
      <!-- Ripple Effect -->
      <div class="button-ripple" 
           [class.ripple-active]="rippleActive"
           [style.left.px]="rippleX"
           [style.top.px]="rippleY">
      </div>
    </p-button>
  `,
  styleUrls: ['./modern-button.component.scss']
})
export class ModernButtonComponent {
  @Input() variant: 'filled' | 'outlined' | 'text' | 'fab' | 'icon' = 'filled';
  @Input() color: 'primary' | 'secondary' | 'accent' | 'warn' | 'success' = 'primary';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() type: 'button' | 'submit' | 'reset' = 'button';
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() icon?: string;
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() iconOnly: boolean = false;
  @Input() fullWidth: boolean = false;
  @Input() rounded: boolean = false;
  @Input() elevation: boolean = true;

  @Output() clicked = new EventEmitter<MouseEvent>();

  rippleActive = false;
  rippleX = 0;
  rippleY = 0;

  constructor(private iconService: IconService) {}

  get buttonClasses(): string {
    const classes = [
      'modern-button',
      `variant-${this.variant}`,
      `color-${this.color}`,
      `size-${this.size}`
    ];

    if (this.disabled) classes.push('disabled');
    if (this.loading) classes.push('loading');
    if (this.iconOnly) classes.push('icon-only');
    if (this.fullWidth) classes.push('full-width');
    if (this.rounded) classes.push('rounded');
    if (this.elevation) classes.push('elevated');

    return classes.join(' ');
  }

  getIcon(semanticName: string): string {
    return this.iconService.getIconName(semanticName);
  }

  onClick(event?: MouseEvent): void {
    if (this.disabled || this.loading) return;

    // Create ripple effect
    if (event) {
      const rect = (event.target as HTMLElement).getBoundingClientRect();
      this.rippleX = event.clientX - rect.left;
      this.rippleY = event.clientY - rect.top;
      this.rippleActive = true;

      setTimeout(() => {
        this.rippleActive = false;
      }, 600);
    }

    this.clicked.emit(event);
  }
}
