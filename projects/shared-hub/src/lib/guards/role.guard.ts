import { Injectable, inject } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

export interface RoleGuardData {
  roles?: string[];
  permissions?: string[];
  requireAll?: boolean; // If true, user must have ALL specified roles/permissions
  redirectTo?: string;
  errorMessage?: string;
}

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate, CanActivateChild {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkRoleAccess(route, state);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkRoleAccess(childRoute, state);
  }

  private checkRoleAccess(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    const guardData = route.data as RoleGuardData;
    
    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        // First check if user is authenticated
        if (!authState.isAuthenticated) {
          this.handleUnauthorized(guardData, 'not-authenticated');
          return false;
        }

        // Check roles if specified
        if (guardData.roles && guardData.roles.length > 0) {
          const hasRequiredRoles = this.checkRoles(
            authState.profile?.role || '',
            guardData.roles,
            guardData.requireAll || false
          );
          
          if (!hasRequiredRoles) {
            this.handleUnauthorized(guardData, 'insufficient-role');
            return false;
          }
        }

        // Check permissions if specified
        if (guardData.permissions && guardData.permissions.length > 0) {
          const userPermissions = authState.staff?.permissions || [];
          const hasRequiredPermissions = this.checkPermissions(
            userPermissions,
            guardData.permissions,
            guardData.requireAll || false
          );
          
          if (!hasRequiredPermissions) {
            this.handleUnauthorized(guardData, 'insufficient-permissions');
            return false;
          }
        }

        return true;
      }),
      catchError(error => {
        console.error('Role guard error:', error);
        this.handleError(guardData);
        return of(false);
      })
    );
  }

  private checkRoles(userRole: string, requiredRoles: string[], requireAll: boolean): boolean {
    if (requireAll) {
      // User must have all specified roles (not typical, but supported)
      return requiredRoles.every(role => this.hasRoleOrHigher(userRole, role));
    } else {
      // User must have at least one of the specified roles
      return requiredRoles.some(role => this.hasRoleOrHigher(userRole, role));
    }
  }

  private checkPermissions(userPermissions: string[], requiredPermissions: string[], requireAll: boolean): boolean {
    if (requireAll) {
      // User must have all specified permissions
      return requiredPermissions.every(permission => userPermissions.includes(permission));
    } else {
      // User must have at least one of the specified permissions
      return requiredPermissions.some(permission => userPermissions.includes(permission));
    }
  }

  private hasRoleOrHigher(userRole: string, requiredRole: string): boolean {
    const roleHierarchy = {
      'staff': 1,
      'manager': 2,
      'admin': 3
    };
    
    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    
    return userLevel >= requiredLevel;
  }

  private handleUnauthorized(guardData: RoleGuardData, reason: string): void {
    const redirectTo = guardData.redirectTo || '/access-denied';
    
    this.router.navigate([redirectTo], {
      queryParams: {
        reason,
        message: guardData.errorMessage || 'Access denied',
        roles: guardData.roles?.join(','),
        permissions: guardData.permissions?.join(',')
      }
    });
  }

  private handleError(guardData: RoleGuardData): void {
    const redirectTo = guardData.redirectTo || '/error';
    this.router.navigate([redirectTo], {
      queryParams: { reason: 'role-check-error' }
    });
  }
}

// Specific guards for common role checks
@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
  private roleGuard = inject(RoleGuard);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    // Inject admin role requirement into route data
    route.data = {
      ...route.data,
      roles: ['admin'],
      errorMessage: 'Administrator access required'
    };
    
    return this.roleGuard.canActivate(route, state);
  }
}

@Injectable({
  providedIn: 'root'
})
export class ManagerGuard implements CanActivate {
  private roleGuard = inject(RoleGuard);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    // Inject manager or admin role requirement into route data
    route.data = {
      ...route.data,
      roles: ['manager', 'admin'],
      errorMessage: 'Manager or Administrator access required'
    };
    
    return this.roleGuard.canActivate(route, state);
  }
}

@Injectable({
  providedIn: 'root'
})
export class StaffGuard implements CanActivate {
  private roleGuard = inject(RoleGuard);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    // Allow any authenticated staff member
    route.data = {
      ...route.data,
      roles: ['staff', 'manager', 'admin'],
      errorMessage: 'Staff access required'
    };
    
    return this.roleGuard.canActivate(route, state);
  }
}

// Permission-based guards
@Injectable({
  providedIn: 'root'
})
export class TimeClockPermissionGuard implements CanActivate {
  private roleGuard = inject(RoleGuard);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    route.data = {
      ...route.data,
      permissions: ['time-clock.use'],
      errorMessage: 'Time clock access not permitted'
    };
    
    return this.roleGuard.canActivate(route, state);
  }
}

@Injectable({
  providedIn: 'root'
})
export class TaskManagementPermissionGuard implements CanActivate {
  private roleGuard = inject(RoleGuard);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    route.data = {
      ...route.data,
      permissions: ['tasks.view', 'tasks.manage'],
      requireAll: false, // User needs either view OR manage permission
      errorMessage: 'Task management access not permitted'
    };
    
    return this.roleGuard.canActivate(route, state);
  }
}

@Injectable({
  providedIn: 'root'
})
export class ScheduleViewPermissionGuard implements CanActivate {
  private roleGuard = inject(RoleGuard);

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    route.data = {
      ...route.data,
      permissions: ['schedule.view'],
      errorMessage: 'Schedule viewing access not permitted'
    };
    
    return this.roleGuard.canActivate(route, state);
  }
}

// Utility functions for role and permission checking
export class RoleUtils {
  static hasRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy = {
      'staff': 1,
      'manager': 2,
      'admin': 3
    };
    
    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    
    return userLevel >= requiredLevel;
  }

  static hasPermission(userPermissions: string[], requiredPermission: string): boolean {
    return userPermissions.includes(requiredPermission);
  }

  static hasAnyPermission(userPermissions: string[], requiredPermissions: string[]): boolean {
    return requiredPermissions.some(permission => userPermissions.includes(permission));
  }

  static hasAllPermissions(userPermissions: string[], requiredPermissions: string[]): boolean {
    return requiredPermissions.every(permission => userPermissions.includes(permission));
  }

  static getRoleLevel(role: string): number {
    const roleHierarchy = {
      'staff': 1,
      'manager': 2,
      'admin': 3
    };
    
    return roleHierarchy[role as keyof typeof roleHierarchy] || 0;
  }

  static isHigherRole(userRole: string, compareRole: string): boolean {
    return this.getRoleLevel(userRole) > this.getRoleLevel(compareRole);
  }
}
