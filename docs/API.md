# StaffManager API Documentation

## 📋 Overview

StaffManager uses Firebase Firestore as its primary database with real-time synchronization capabilities. The API layer consists of Angular services that provide type-safe interfaces to Firebase collections and external services.

## 🔐 Authentication API

### AuthService

#### Methods

```typescript
interface AuthService {
  // Authentication state
  userProfile$: Observable<UserProfile | null>;
  isAuthenticated$: Observable<boolean>;
  
  // Authentication methods
  signIn(email: string, password: string): Promise<UserCredential>;
  signUp(email: string, password: string, profile: UserProfile): Promise<UserCredential>;
  signOut(): Promise<void>;
  resetPassword(email: string): Promise<void>;
  
  // Profile management
  updateProfile(profile: Partial<UserProfile>): Promise<void>;
  getCurrentUser(): Promise<User | null>;
}
```

#### User Profile Model

```typescript
interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: 'admin' | 'manager' | 'staff';
  businessIds: string[];
  createdAt: Date;
  updatedAt: Date;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    notifications: NotificationPreferences;
  };
}

interface NotificationPreferences {
  email: boolean;
  push: boolean;
  inApp: boolean;
  scheduleChanges: boolean;
  timeOffRequests: boolean;
  systemUpdates: boolean;
}
```

## 🏢 Business API

### BusinessService

#### Methods

```typescript
interface BusinessService {
  // Business management
  businesses$: Observable<Business[]>;
  selectedBusiness$: Observable<Business | null>;
  
  createBusiness(business: CreateBusinessRequest): Promise<string>;
  updateBusiness(id: string, updates: Partial<Business>): Promise<void>;
  deleteBusiness(id: string): Promise<void>;
  selectBusiness(businessId: string): Promise<void>;
  
  // Business operations
  getBusinessById(id: string): Observable<Business | null>;
  getUserBusinesses(userId: string): Observable<Business[]>;
  addUserToBusiness(businessId: string, userId: string, role: BusinessRole): Promise<void>;
  removeUserFromBusiness(businessId: string, userId: string): Promise<void>;
}
```

#### Business Model

```typescript
interface Business {
  id: string;
  name: string;
  description?: string;
  address: Address;
  contact: ContactInfo;
  settings: BusinessSettings;
  hoursOfOperation: HoursOfOperation;
  hoursOfBusiness: HoursOfBusiness;
  authorizedUsers: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface BusinessSettings {
  timezone: string;
  currency: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6; // Sunday = 0
  
  // Operational settings
  maxStaffCapacity: number;
  defaultShiftDuration: number; // minutes
  breakDuration: number; // minutes
  overtimeThreshold: number; // hours per week
  
  // Time tracking
  timeTrackingMethod: 'manual' | 'automatic' | 'hybrid';
  requireBreakConfirmation: boolean;
  allowEarlyClockIn: boolean;
  allowLateClockOut: boolean;
  
  // Scheduling
  scheduleAdvanceNotice: number; // days
  allowStaffSelfScheduling: boolean;
  requireManagerApproval: boolean;
}

interface HoursOfOperation {
  [key: string]: DaySchedule; // 'monday', 'tuesday', etc.
}

interface HoursOfBusiness {
  [key: string]: DaySchedule;
}

interface DaySchedule {
  isOpen: boolean;
  openTime?: string; // HH:mm format
  closeTime?: string; // HH:mm format
  breaks?: TimeSlot[];
}

interface TimeSlot {
  startTime: string;
  endTime: string;
  description?: string;
}
```

## 👥 Staff API

### StaffService

#### Methods

```typescript
interface StaffService {
  // Staff management
  staff$: Observable<Staff[]>;
  selectedStaff$: Observable<Staff | null>;
  
  createStaff(staff: CreateStaffRequest): Promise<string>;
  updateStaff(id: string, updates: Partial<Staff>): Promise<void>;
  deleteStaff(id: string): Promise<void>;
  
  // Staff operations
  getStaffById(id: string): Observable<Staff | null>;
  getStaffByBusiness(businessId: string): Observable<Staff[]>;
  updateAvailability(staffId: string, availability: WeeklyAvailability): Promise<void>;
  
  // Staff scheduling
  getStaffSchedule(staffId: string, startDate: Date, endDate: Date): Observable<Shift[]>;
  assignShift(staffId: string, shift: Shift): Promise<void>;
  removeShift(staffId: string, shiftId: string): Promise<void>;
}
```

#### Staff Model

```typescript
interface Staff {
  id: string;
  userId?: string; // Link to user account
  businessId: string;
  
  // Personal information
  personalInfo: PersonalInfo;
  contactInfo: ContactInfo;
  emergencyContact: EmergencyContact;
  
  // Employment details
  employmentInfo: EmploymentInfo;
  
  // Scheduling
  availability: WeeklyAvailability;
  preferences: StaffPreferences;
  
  // Status
  status: 'active' | 'inactive' | 'terminated';
  createdAt: Date;
  updatedAt: Date;
}

interface PersonalInfo {
  firstName: string;
  lastName: string;
  dateOfBirth?: Date;
  photoURL?: string;
  notes?: string;
}

interface EmploymentInfo {
  employeeId: string;
  position: string;
  department?: string;
  hireDate: Date;
  hourlyRate?: number;
  salaryAmount?: number;
  payType: 'hourly' | 'salary';
  role: 'staff' | 'manager' | 'admin';
}

interface WeeklyAvailability {
  monday: DayAvailability;
  tuesday: DayAvailability;
  wednesday: DayAvailability;
  thursday: DayAvailability;
  friday: DayAvailability;
  saturday: DayAvailability;
  sunday: DayAvailability;
}

interface DayAvailability {
  isAvailable: boolean;
  timeSlots: TimeSlot[];
  notes?: string;
}
```

## ⏰ Time Management API

### TimeService

#### Methods

```typescript
interface TimeService {
  // Time tracking
  clockIn(staffId: string, location?: GeoLocation): Promise<TimeEntry>;
  clockOut(staffId: string, location?: GeoLocation): Promise<void>;
  getCurrentTimeEntry(staffId: string): Observable<TimeEntry | null>;
  
  // Time entries
  getTimeEntries(staffId: string, startDate: Date, endDate: Date): Observable<TimeEntry[]>;
  updateTimeEntry(entryId: string, updates: Partial<TimeEntry>): Promise<void>;
  deleteTimeEntry(entryId: string): Promise<void>;
  
  // Time off requests
  timeOffRequests$: Observable<TimeOffRequest[]>;
  createTimeOffRequest(request: CreateTimeOffRequest): Promise<string>;
  approveTimeOffRequest(requestId: string, approverId: string): Promise<void>;
  denyTimeOffRequest(requestId: string, approverId: string, reason: string): Promise<void>;
  
  // Scheduling
  schedules$: Observable<Schedule[]>;
  createSchedule(schedule: CreateScheduleRequest): Promise<string>;
  updateSchedule(scheduleId: string, updates: Partial<Schedule>): Promise<void>;
  generateAISchedule(constraints: ScheduleConstraints): Promise<Schedule>;
}
```

#### Time Models

```typescript
interface TimeEntry {
  id: string;
  staffId: string;
  businessId: string;
  clockInTime: Date;
  clockOutTime?: Date;
  breakTime?: number; // minutes
  totalHours?: number;
  location?: GeoLocation;
  notes?: string;
  status: 'active' | 'completed' | 'pending_approval';
  createdAt: Date;
  updatedAt: Date;
}

interface TimeOffRequest {
  id: string;
  staffId: string;
  businessId: string;
  type: 'vacation' | 'sick' | 'personal' | 'other';
  startDate: Date;
  endDate: Date;
  reason?: string;
  status: 'pending' | 'approved' | 'denied';
  approverId?: string;
  approvedAt?: Date;
  denyReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Schedule {
  id: string;
  businessId: string;
  name: string;
  startDate: Date;
  endDate: Date;
  shifts: Shift[];
  status: 'draft' | 'published' | 'archived';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Shift {
  id: string;
  staffId: string;
  startTime: Date;
  endTime: Date;
  position: string;
  notes?: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
}
```

## ⚙️ Settings API

### SettingsService

#### Methods

```typescript
interface SettingsService {
  // User settings
  userSettings$: Observable<UserSettings | null>;
  updateSettings(userId: string, settings: Partial<UserSettings>): Promise<void>;
  
  // System monitoring
  systemStatus$: Observable<SystemStatus | null>;
  performSystemCheck(): Promise<void>;
  
  // Theme management
  applySettings(settings: UserSettings): Promise<void>;
}
```

#### Settings Models

```typescript
interface UserSettings {
  userId: string;
  appearance: AppearanceSettings;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  integrations: IntegrationSettings;
  createdAt: Date;
  updatedAt: Date;
}

interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system';
  colorScheme: {
    primary: string;
    accent: string;
    warn: string;
  };
  typography: {
    fontSize: 'small' | 'medium' | 'large' | 'extra-large';
    fontFamily: string;
    lineHeight: 'compact' | 'normal' | 'relaxed';
  };
  layout: {
    density: 'compact' | 'comfortable' | 'spacious';
    sidebarBehavior: 'push' | 'overlay' | 'auto';
    sidebarDefaultState: 'expanded' | 'collapsed';
    dashboardColumns: number;
    widgetSpacing: 'tight' | 'normal' | 'loose';
  };
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    focusIndicators: boolean;
    screenReaderOptimized: boolean;
  };
}

interface SystemStatus {
  overall: 'healthy' | 'warning' | 'error';
  services: {
    ai: ServiceStatus;
    database: ServiceStatus;
    pwa: ServiceStatus;
    auth: ServiceStatus;
    storage: ServiceStatus;
    notifications: ServiceStatus;
  };
  lastChecked: Date;
}

interface ServiceStatus {
  status: 'online' | 'offline' | 'degraded' | 'maintenance' | 'error' | 'warning';
  responseTime?: number;
  lastError?: string;
  uptime?: number;
  version?: string;
}
```

## 🤖 AI Integration API

### GeminiService

#### Methods

```typescript
interface GeminiService {
  // Schedule generation
  generateSchedule(constraints: ScheduleConstraints): Promise<Schedule>;
  optimizeSchedule(schedule: Schedule, constraints: ScheduleConstraints): Promise<Schedule>;
  
  // Insights and analytics
  generateStaffInsights(staffId: string, timeframe: TimeFrame): Promise<StaffInsights>;
  generateBusinessInsights(businessId: string, timeframe: TimeFrame): Promise<BusinessInsights>;
  
  // Recommendations
  getSchedulingRecommendations(businessId: string): Promise<SchedulingRecommendation[]>;
  getStaffingRecommendations(businessId: string): Promise<StaffingRecommendation[]>;
}
```

## 📅 Calendar API

### CalendarService

#### Methods

```typescript
interface CalendarService {
  // Calendar events
  events$: Observable<CalendarEvent[]>;
  createEvent(event: CreateEventRequest): Promise<string>;
  updateEvent(eventId: string, updates: Partial<CalendarEvent>): Promise<void>;
  deleteEvent(eventId: string): Promise<void>;
  
  // Calendar integration
  syncWithExternalCalendar(calendarType: 'google' | 'outlook' | 'apple'): Promise<void>;
  importEvents(events: ExternalEvent[]): Promise<void>;
  exportEvents(startDate: Date, endDate: Date): Promise<CalendarEvent[]>;
}
```

## 🔄 Real-time Updates

All services use Firebase's real-time capabilities through observables:

```typescript
// Example: Real-time staff updates
this.staffService.staff$.subscribe(staff => {
  // Component automatically updates when staff data changes
  this.staff = staff;
});

// Example: Real-time system status
this.settingsService.systemStatus$.subscribe(status => {
  // UI updates automatically when system status changes
  this.systemStatus = status;
});
```

## 🚨 Error Handling

All API methods include comprehensive error handling:

```typescript
// Service error handling pattern
async createStaff(staff: CreateStaffRequest): Promise<string> {
  try {
    const docRef = await this.firestore.collection('staff').add(staff);
    return docRef.id;
  } catch (error) {
    console.error('Error creating staff:', error);
    throw new Error('Failed to create staff member');
  }
}
```

## 📊 Response Formats

All API responses follow consistent patterns:

```typescript
// Success response
interface ApiResponse<T> {
  success: true;
  data: T;
  timestamp: Date;
}

// Error response
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
}
```

---

This API documentation provides comprehensive coverage of all StaffManager services and their interfaces, enabling developers to effectively integrate with and extend the system.
