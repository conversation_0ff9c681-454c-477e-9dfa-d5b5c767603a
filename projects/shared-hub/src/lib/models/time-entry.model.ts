// Time Tracking Models for StaffHub and TimeHub
export interface TimeEntry {
  id: string;
  staffId: string;
  businessId: string;

  // Time tracking
  clockInTime: Date;
  clockOutTime?: Date;
  totalHours?: number;

  // Location and device info
  clockInLocation?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  clockOutLocation?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  deviceInfo?: {
    deviceId: string;
    deviceType: 'mobile' | 'tablet' | 'desktop' | 'kiosk';
    userAgent: string;
    ipAddress: string;
  };

  // Status and validation
  status: 'active' | 'completed' | 'pending-approval' | 'approved' | 'rejected';
  isManualEntry: boolean;
  requiresApproval: boolean;

  // Break tracking
  breaks: TimeBreak[];
  totalBreakTime?: number;

  // Notes and metadata
  notes?: string;
  tags?: string[];

  // Approval workflow
  approvedBy?: string;
  approvedAt?: Date;
  rejectionReason?: string;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

export interface TimeBreak {
  id: string;
  timeEntryId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number; // minutes
  type: 'lunch' | 'break' | 'personal' | 'other';
  isPaid: boolean;
  notes?: string;
}

export interface TimeClockSettings {
  businessId: string;

  // Clock in/out rules
  allowEarlyClockIn: boolean;
  earlyClockInMinutes: number;
  allowLateClockOut: boolean;
  lateClockOutMinutes: number;

  // Location requirements
  requireLocation: boolean;
  allowedLocations?: {
    name: string;
    latitude: number;
    longitude: number;
    radius: number; // meters
  }[];

  // Break settings
  automaticBreaks: boolean;
  minimumBreakDuration: number; // minutes
  maximumBreakDuration: number; // minutes

  // Approval settings
  requireApprovalForOvertime: boolean;
  overtimeThreshold: number; // hours
  requireApprovalForManualEntries: boolean;

  // PIN settings
  pinLength: number;
  pinExpirationDays: number;
  maxFailedAttempts: number;
  lockoutDurationMinutes: number;
}

export interface TimeClockStatus {
  staffId: string;
  isClocked: boolean;
  currentTimeEntry?: TimeEntry | null;
  lastClockIn?: Date;
  lastClockOut?: Date;
  totalHoursToday: number;
  totalHoursWeek: number;
  onBreak: boolean;
  currentBreak?: TimeBreak;
  pendingIssues: TimeClockIssue[];
}

export interface TimeClockIssue {
  id: string;
  staffId: string;
  type: 'missed-clock-out' | 'missed-clock-in' | 'long-break' | 'location-mismatch' | 'overtime';
  description: string;
  severity: 'low' | 'medium' | 'high';
  date: Date;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  resolution?: string;
}

// Time reporting and analytics
export interface TimeReport {
  staffId: string;
  businessId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  totalHours: number;
  regularHours: number;
  overtimeHours: number;
  breakHours: number;
  entries: TimeEntry[];
  summary: {
    daysWorked: number;
    averageHoursPerDay: number;
    totalBreaks: number;
    punctualityScore: number;
  };
}
