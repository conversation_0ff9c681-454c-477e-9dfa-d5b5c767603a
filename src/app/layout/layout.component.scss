// CSS Custom Properties for dynamic layout
:root {
  --sidebar-width-expanded: 240px;
  --sidebar-width-collapsed: 64px;
  --header-height: 64px;
  --transition-duration: 0.3s;
  --transition-timing: cubic-bezier(0.4, 0.0, 0.2, 1);
}

// Main App Shell - Enhanced with modern styling
.app-shell {
  height: 100vh;
  width: 100vw;
  display: flex;
  background: var(--sm-background-default);
  overflow: hidden;
  position: relative;
  transition: all var(--sm-transition-normal);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(25, 118, 210, 0.02) 0%,
      transparent 50%,
      rgba(124, 58, 237, 0.02) 100%);
    pointer-events: none;
    z-index: 0;
  }
}

// Sidebar - Fixed width control
.app-sidebar {
  width: var(--sidebar-width-expanded);
  height: 100vh;
  background: var(--sm-background-paper, #ffffff);
  border-right: 1px solid var(--sm-border-color, #e0e0e0);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
  overflow: hidden;
  transition: width var(--transition-duration) var(--transition-timing);
  flex-shrink: 0;

  // Collapsed state
  &.collapsed {
    width: var(--sidebar-width-collapsed);
  }

  // Mobile states
  .mobile-layout & {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    transform: translateX(-100%);
    transition: transform var(--transition-duration) var(--transition-timing);
    z-index: 1100;

    &.mobile-open {
      transform: translateX(0);
    }
  }
}



// Remove old sidebar toggle button styles - now handled in sidebar component

// Main Content Area
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

// Header - Fixed positioning
.app-header {
  height: var(--header-height);
  background: var(--sm-background-paper, #ffffff);
  border-bottom: 1px solid var(--sm-border-color, #e0e0e0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 999;
  position: relative;
  flex-shrink: 0;
}

// Content - Main content area
.app-content {
  flex: 1;
  background: var(--sm-background-default, #fafafa);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  padding: 0;

  // Smooth scrolling
  scroll-behavior: smooth;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--sm-background-default, #fafafa);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--sm-text-secondary, #666);
    border-radius: 4px;

    &:hover {
      background: var(--sm-text-primary, #333);
    }
  }
}

// Mobile Backdrop - Enhanced
.mobile-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  z-index: var(--sm-z-modal-backdrop);
  backdrop-filter: blur(8px);
  transition: all var(--sm-transition-fast);

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
}

// Responsive Design
@media (max-width: 900px) {
  // Mobile responsive styles handled in sidebar component
}

@media (max-width: 600px) {
  .app-sidebar {
    .mobile-layout & {
      width: 100vw;
    }
  }
}

// Focus and Accessibility - handled in sidebar component

// Dark Theme Support
.dark-theme {
  .app-shell {
    background: var(--sm-background-default-dark, #121212);
  }

  .app-sidebar {
    background: var(--sm-background-paper-dark, #1e1e1e);
    border-right-color: rgba(255, 255, 255, 0.1);
  }

  .app-header {
    background: var(--sm-background-paper-dark, #1e1e1e);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .app-content {
    background: var(--sm-background-default-dark, #121212);
  }
}

// Loading State Support
.app-shell.loading {
  .app-content {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Animation Performance Optimizations
.app-shell,
.app-sidebar,
.app-main,
.app-header,
.app-content {
  will-change: transform, width, grid-template-columns;
}

// Prevent layout shifts during transitions
.app-shell * {
  box-sizing: border-box;
}