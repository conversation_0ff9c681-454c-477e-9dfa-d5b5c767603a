# Authentication System Troubleshooting

## 🚨 Critical Issue: User Registration Not Working

**Problem**: User registration fails, "no users exist" error
**Impact**: Cannot create any user accounts, system unusable
**Priority**: CRITICAL - Blocking all functionality

---

## 🔍 Diagnostic Steps

### **Step 1: Test Registration with Debug Component**
1. Go to: http://localhost:52213/debug/auth-test
2. Use test credentials:
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
3. Click "Test Registration" button
4. Monitor console output for specific error messages

### **Step 2: Check Firebase Console**
1. Go to: https://console.firebase.google.com/project/staffmanager-9b0f2/authentication
2. Check if Authentication is enabled
3. Verify Email/Password provider is enabled
4. Check for any security rules blocking registration

### **Step 3: Verify Firebase Configuration**
Current config in `src/environments/environment.ts`:
```typescript
firebase: {
  apiKey: "AIzaSyAJVN9dhfaRhnD686tytSg48IGI_wVtzQ8",
  authDomain: "staffmanager-9b0f2.firebaseapp.com",
  projectId: "staffmanager-9b0f2",
  storageBucket: "staffmanager-9b0f2.firebasestorage.app",
  messagingSenderId: "666727178550",
  appId: "1:666727178550:web:187e1798f2d7976b72d397"
}
```

---

## 🚨 Potential Issues

### **1. Firebase Authentication Not Enabled**
**Symptoms**: Registration fails with "operation-not-allowed" error
**Solution**: Enable Authentication in Firebase Console
**Check**: Go to Authentication > Sign-in method > Email/Password

### **2. Firebase Security Rules Too Restrictive**
**Symptoms**: User creation succeeds but profile creation fails
**Solution**: Check Firestore security rules
**Check**: Go to Firestore > Rules

### **3. Network/CORS Issues**
**Symptoms**: Network errors during registration
**Solution**: Check browser network tab for failed requests
**Check**: Look for 403, 404, or CORS errors

### **4. Firebase Project Configuration**
**Symptoms**: "Firebase project not found" errors
**Solution**: Verify project ID and configuration
**Check**: Ensure project exists and config is correct

---

## 🔧 Immediate Fixes to Try

### **Fix 1: Enable Firebase Authentication**
1. Go to Firebase Console: https://console.firebase.google.com/project/staffmanager-9b0f2
2. Navigate to Authentication > Sign-in method
3. Enable "Email/Password" provider
4. Save changes

### **Fix 2: Check Firestore Security Rules**
Default rules should allow authenticated users:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read/write staff data
    match /staff/{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### **Fix 3: Test with Minimal Registration**
Try registering with the debug component using:
- Simple email: `<EMAIL>`
- Strong password: `Password123!`
- Monitor browser console for specific error codes

---

## 📊 Expected Error Codes and Solutions

### **auth/operation-not-allowed**
- **Cause**: Email/Password authentication not enabled
- **Solution**: Enable in Firebase Console > Authentication > Sign-in method

### **auth/weak-password**
- **Cause**: Password doesn't meet requirements
- **Solution**: Use password with 6+ characters

### **auth/email-already-in-use**
- **Cause**: Email already registered
- **Solution**: Use different email or try login instead

### **auth/invalid-email**
- **Cause**: Malformed email address
- **Solution**: Use valid email format

### **permission-denied**
- **Cause**: Firestore security rules blocking profile creation
- **Solution**: Update Firestore rules to allow user profile creation

---

## 🧪 Testing Protocol

### **Test 1: Basic Registration**
```typescript
// Test data
email: '<EMAIL>'
password: 'AdminPassword123!'
displayName: 'Admin User'
role: 'admin'

// Expected result
✅ User created in Firebase Auth
✅ Profile document created in Firestore
✅ Authentication state updated
✅ Redirect to dashboard
```

### **Test 2: Login with Created User**
```typescript
// Use same credentials from registration
email: '<EMAIL>'
password: 'AdminPassword123!'

// Expected result
✅ Login successful
✅ User profile loaded
✅ Dashboard accessible
```

### **Test 3: Session Persistence**
```typescript
// After successful login
1. Refresh page
2. Check if user remains logged in
3. Verify dashboard is accessible

// Expected result
✅ User session persists
✅ No redirect to login
✅ Profile data available
```

---

## 🔍 Debug Console Commands

### **Check Firebase Connection**
```javascript
// In browser console
console.log('Firebase Auth:', firebase.auth());
console.log('Firebase Firestore:', firebase.firestore());
```

### **Check Current User**
```javascript
// In browser console
firebase.auth().onAuthStateChanged(user => {
  console.log('Current user:', user);
});
```

### **Test Registration Manually**
```javascript
// In browser console
firebase.auth().createUserWithEmailAndPassword('<EMAIL>', 'password123')
  .then(result => console.log('Registration success:', result))
  .catch(error => console.error('Registration error:', error));
```

---

## 📞 Next Steps

### **If Registration Still Fails**:
1. **Check Firebase Console**: Verify project exists and authentication is enabled
2. **Review Browser Console**: Look for specific error codes and messages
3. **Test Network**: Check if Firebase API calls are reaching the server
4. **Verify Configuration**: Ensure environment.ts has correct Firebase config

### **If Registration Succeeds But Login Fails**:
1. **Check User Profile Creation**: Verify Firestore document was created
2. **Test Authentication State**: Monitor auth state changes
3. **Review Route Guards**: Ensure guards allow authenticated users

### **If Everything Works But No Persistence**:
1. **Check Session Storage**: Verify Firebase auth persistence
2. **Test Page Refresh**: Ensure user remains logged in
3. **Review Auth Service**: Check user$ observable implementation

---

## 🎯 Success Criteria

**Authentication is working when**:
- [ ] User registration completes without errors
- [ ] User can login with created credentials
- [ ] User profile is created in Firestore
- [ ] Authentication state persists across page refreshes
- [ ] Dashboard is accessible after login
- [ ] Debug component shows user data

**🚨 CRITICAL: Until authentication works, all other features are blocked. Focus on fixing registration first.**
