import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, Subject, merge, fromEvent } from 'rxjs';
import { map, filter, distinctUntilChanged, shareReplay } from 'rxjs/operators';
import { DataSyncService } from './data-sync.service';
import { AuthService } from './auth.service';
import type { Notification, NotificationPreferences, NotificationEvent } from '../models/notification.model';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private dataSyncService = inject(DataSyncService);
  private authService = inject(AuthService);

  // Notification state
  private notificationsSubject = new BehaviorSubject<Notification[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private preferencesSubject = new BehaviorSubject<NotificationPreferences | null>(null);

  // Real-time events
  private notificationEventsSubject = new Subject<NotificationEvent>();

  // Push notification support
  private swRegistration: ServiceWorkerRegistration | null = null;
  private vapidPublicKey = 'YOUR_VAPID_PUBLIC_KEY'; // Replace with actual key

  // Public observables
  notifications$ = this.notificationsSubject.asObservable();
  unreadCount$ = this.unreadCountSubject.asObservable();
  preferences$ = this.preferencesSubject.asObservable();
  notificationEvents$ = this.notificationEventsSubject.asObservable();

  constructor() {
    this.initializeNotifications();
    this.initializePushNotifications();
  }

  // Initialize notification system
  private initializeNotifications(): void {
    this.authService.getCurrentStaffId().pipe(
      filter(staffId => !!staffId)
    ).subscribe(staffId => {
      if (staffId) {
        this.loadNotifications(staffId);
        this.loadPreferences(staffId);
      }
    });
  }

  // Initialize push notifications
  private async initializePushNotifications(): Promise<void> {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      try {
        this.swRegistration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered:', this.swRegistration);
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  // Load notifications for current user
  private loadNotifications(staffId: string): void {
    this.dataSyncService.subscribeToCollection<Notification>(
      'notifications',
      [{ field: 'recipientId', operator: '==', value: staffId }],
      'createdAt'
    ).subscribe(notifications => {
      this.notificationsSubject.next(notifications);

      const unreadCount = notifications.filter(n => !n.isRead).length;
      this.unreadCountSubject.next(unreadCount);
    });
  }

  // Load notification preferences
  private loadPreferences(staffId: string): void {
    this.dataSyncService.subscribeToDocument<NotificationPreferences>(
      'notificationPreferences',
      staffId
    ).subscribe(preferences => {
      this.preferencesSubject.next(preferences);
    });
  }

  // Send notification
  async sendNotification(notification: Partial<Notification>): Promise<string> {
    try {
      const notificationData: Partial<Notification> = {
        ...notification,
        status: 'pending',
        isRead: false,
        channels: this.determineChannels(notification),
        createdAt: new Date()
      };

      const notificationId = await this.dataSyncService.createDocument('notifications', notificationData);

      // Process notification delivery
      await this.processNotificationDelivery(notificationId, notificationData as Notification);

      return notificationId;
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  // Process notification delivery across channels
  private async processNotificationDelivery(notificationId: string, notification: Notification): Promise<void> {
    const preferences = this.preferencesSubject.value;

    for (const channel of notification.channels) {
      try {
        switch (channel.type) {
          case 'push':
            await this.sendPushNotification(notification);
            break;
          case 'email':
            await this.sendEmailNotification(notification);
            break;
          case 'sms':
            await this.sendSmsNotification(notification);
            break;
          case 'in-app':
            // In-app notifications are handled by real-time updates
            break;
        }

        // Update channel status
        channel.status = 'sent';
        channel.sentAt = new Date();

      } catch (error) {
        console.error(`Error sending ${channel.type} notification:`, error);
        channel.status = 'failed';
        channel.errorMessage = error instanceof Error ? error.message : String(error);
      }
    }

    // Update notification with channel statuses
    await this.dataSyncService.updateDocument('notifications', notificationId, {
      channels: notification.channels,
      status: 'sent'
    });
  }

  // Send push notification
  private async sendPushNotification(notification: Notification): Promise<void> {
    if (!this.swRegistration) {
      throw new Error('Service Worker not registered');
    }

    const preferences = this.preferencesSubject.value;
    if (!preferences?.channels.push.enabled) {
      throw new Error('Push notifications disabled');
    }

    // Check quiet hours
    if (this.isQuietHours(preferences.channels.push.quietHours)) {
      throw new Error('Quiet hours active');
    }

    const payload = {
      title: notification.title,
      body: notification.message,
      icon: '/assets/icons/icon-192x192.png',
      badge: '/assets/icons/badge-72x72.png',
      data: {
        notificationId: notification.id,
        actionUrl: notification.actionUrl,
        timestamp: Date.now()
      },
      actions: notification.actionRequired ? [
        {
          action: 'view',
          title: notification.actionText || 'View'
        }
      ] : undefined
    };

    // Send to all registered devices
    for (const deviceToken of preferences.channels.push.deviceTokens) {
      try {
        await this.sendPushToDevice(deviceToken, payload);
      } catch (error) {
        console.error('Error sending push to device:', error);
      }
    }
  }

  // Send push to specific device
  private async sendPushToDevice(deviceToken: string, payload: any): Promise<void> {
    // This would typically call your backend API to send the push notification
    // For now, we'll simulate the call
    const response = await fetch('/api/notifications/push', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        deviceToken,
        payload
      })
    });

    if (!response.ok) {
      throw new Error(`Push notification failed: ${response.statusText}`);
    }
  }

  // Send email notification
  private async sendEmailNotification(notification: Notification): Promise<void> {
    const preferences = this.preferencesSubject.value;
    if (!preferences?.channels.email.enabled) {
      throw new Error('Email notifications disabled');
    }

    // This would typically call your backend API to send the email
    const response = await fetch('/api/notifications/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: preferences.channels.email.emailAddress,
        subject: notification.title,
        body: notification.message,
        actionUrl: notification.actionUrl
      })
    });

    if (!response.ok) {
      throw new Error(`Email notification failed: ${response.statusText}`);
    }
  }

  // Send SMS notification
  private async sendSmsNotification(notification: Notification): Promise<void> {
    const preferences = this.preferencesSubject.value;
    if (!preferences?.channels.sms.enabled || !preferences.channels.sms.phoneNumber) {
      throw new Error('SMS notifications disabled or no phone number');
    }

    if (preferences.channels.sms.urgentOnly && !notification.isUrgent) {
      throw new Error('SMS only for urgent notifications');
    }

    // This would typically call your backend API to send the SMS
    const response = await fetch('/api/notifications/sms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: preferences.channels.sms.phoneNumber,
        message: `${notification.title}: ${notification.message}`
      })
    });

    if (!response.ok) {
      throw new Error(`SMS notification failed: ${response.statusText}`);
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      await this.dataSyncService.updateDocument('notifications', notificationId, {
        isRead: true,
        readAt: new Date()
      });

      // Emit event
      this.notificationEventsSubject.next({
        type: 'notification-read',
        staffId: await this.authService.getCurrentStaffId().toPromise() || '',
        businessId: '', // Would get from context
        data: { notificationId },
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read
  async markAllAsRead(): Promise<void> {
    const notifications = this.notificationsSubject.value;
    const unreadNotifications = notifications.filter(n => !n.isRead);

    for (const notification of unreadNotifications) {
      await this.markAsRead(notification.id);
    }
  }

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      await this.dataSyncService.deleteDocument('notifications', notificationId);

      // Emit event
      this.notificationEventsSubject.next({
        type: 'notification-deleted',
        staffId: await this.authService.getCurrentStaffId().toPromise() || '',
        businessId: '', // Would get from context
        data: { notificationId },
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // Update notification preferences
  async updatePreferences(preferences: Partial<NotificationPreferences>): Promise<void> {
    try {
      const staffId = await this.authService.getCurrentStaffId().toPromise();
      if (!staffId) throw new Error('No staff ID available');

      const updateData = {
        ...preferences,
        updatedAt: new Date()
      };

      await this.dataSyncService.updateDocument('notificationPreferences', staffId, updateData);

      // Emit event
      this.notificationEventsSubject.next({
        type: 'preferences-updated',
        staffId,
        businessId: '', // Would get from context
        data: updateData,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error updating preferences:', error);
      throw error;
    }
  }

  // Request push notification permission
  async requestPushPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  // Subscribe to push notifications
  async subscribeToPush(): Promise<string | null> {
    if (!this.swRegistration) {
      throw new Error('Service Worker not registered');
    }

    try {
      const subscription = await this.swRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
      });

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);

      return subscription.endpoint;
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      return null;
    }
  }

  // Helper methods
  private determineChannels(notification: Partial<Notification>): any[] {
    // Default channels based on notification type and preferences
    return [
      { type: 'in-app', status: 'pending', retryCount: 0, maxRetries: 1 },
      { type: 'push', status: 'pending', retryCount: 0, maxRetries: 3 }
    ];
  }

  private isQuietHours(quietHours?: { start: string; end: string }): boolean {
    if (!quietHours) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const [startHour, startMin] = quietHours.start.split(':').map(Number);
    const [endHour, endMin] = quietHours.end.split(':').map(Number);

    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // Quiet hours span midnight
      return currentTime >= startTime || currentTime <= endTime;
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    // Send subscription to your backend
    const response = await fetch('/api/notifications/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscription)
    });

    if (!response.ok) {
      throw new Error('Failed to send subscription to server');
    }
  }
}
