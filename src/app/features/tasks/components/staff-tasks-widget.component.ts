import { Component, Input, OnInit, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, map, combineLatest } from 'rxjs';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ChipModule } from 'primeng/chip';
import { ProgressBarModule } from 'primeng/progressbar';
import { BadgeModule } from 'primeng/badge';
import { MenuModule } from 'primeng/menu';
import { DividerModule } from 'primeng/divider';
import { ListboxModule } from 'primeng/listbox';
import { TabViewModule } from 'primeng/tabview';
import { AccordionModule } from 'primeng/accordion';
import { CheckboxModule } from 'primeng/checkbox';
import { MessageService } from '@angular/material/snack-bar';

// Services
import { TaskManagementService } from '../services/task-management.service';
import { AITaskAssistantService } from '../services/ai-task-assistant.service';

// Models
import { Task, Checklist, ChecklistItem } from '../models/task.model';

@Component({
  selector: 'app-staff-tasks-widget',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule
    ChipModule,
    ProgressBarModule,
    BadgeModule,
    MenuModule,
    DividerModule,
    ListboxModule,
    TabViewModule,
    AccordionModule,
    CheckboxModule
  ],
  template: `
    <p-card class="staff-tasks-widget">
      <ng-template pTemplate="header">
        <h3>
          <i class="pi pi-circle"></i>
          My Tasks & Checklists
          <p-chip *ngIf="totalActiveTasks() > 0" [matBadge]="totalActiveTasks()" matBadgeColor="warn">
            {{ totalActiveTasks() }} Active
          </p-chip</ng-template>
        <div class="widget-actions">
          <p-button [text]="true" [matMenuTriggerFor]="actionsMenu" pTooltip="Actions">
            <i class="pi pi-ellipsis-v"></i>
          </p-button>
          <p-menu #actionsMenu="matMenu">
            <button mat-menu-item (click)="getAITaskSuggestions()">
              <i class="pi pi-brain"></i>
              AI Suggestions
            </p-button>
            <button mat-menu-item (click)="viewAllTasks()">
              <i class="pi pi-circle"></i>
              View All Tasks
            </p-button>
            <button mat-menu-item (click)="analyzeProductivity()">
              <i class="pi pi-circle"></i>
              Productivity Analysis
            </p-button>
          </p-menu>
        </div></ng-template>

      <ng-template pTemplate="content">
        <p-tabView class="tasks-tabs">

          <!-- Active Tasks Tab -->
          <p-tabPanel>
            <ng-template mat-tab-label>
              <i class="pi pi-circle"></i>
              Tasks
              <p-chip *ngIf="activeTasks().length > 0" class="tab-badge">{{ activeTasks().length }}</p-chip</ng-template>

            <div class="tab-content">
              <div *ngIf="activeTasks().length === 0" class="empty-state">
                <i class="pi pi-circle"></i>
                <h4>No active tasks</h4>
                <p>You're all caught up! Great work!</p>
              </div>

              <div *ngFor="let task of activeTasks() | slice:0:5" class="task-item">
                <div class="task-header">
                  <h4>{{ task.title }}</h4>
                  <div class="task-meta">
                    <p-chip [color]="getPriorityColor(task.priority)" class="priority-chip">
                      {{ task.priority | titlecase }}
                    </p-chip>
                    <p-chip [color]="getStatusColor(task.status)" class="status-chip">
                      {{ task.status | titlecase }}
                    </p-chip>
                  </div>
                </div>

                <p *ngIf="task.description" class="task-description">
                  {{ task.description | slice:0:100 }}{{ task.description.length > 100 ? '...' : '' }}
                </p>

                <div class="task-progress">
                  <div class="progress-info">
                    <span>Progress: {{ task.progress }}%</span>
                    <span *ngIf="task.dueDate" class="due-date"
                          [class.overdue]="task.dueDate < today && task.status !== 'completed'">
                      <i class="pi pi-exclamation-triangle"></i>
                      Due: {{ task.dueDate | date:'mediumDate' }}
                    </span>
                  </div>
                  <p-progressBar [value]="task.progress" mode="determinate"></p-progressBar>
                </div>

                <div class="task-actions">
                  <p-button [outlined]="true" color="primary" (click)="updateTaskProgress(task)">
                    <i class="pi pi-pencil"></i>
                    Update Progress
                  </p-button>
                  <p-button color="accent"
                          *ngIf="task.progress === 100 || task.status === 'completed'"
                          (click)="markTaskComplete(task)">
                    <i class="pi pi-check-circle"></i>
                    Mark Complete
                  </p-button>
                </div>
              </div>

              <div *ngIf="activeTasks().length > 5" class="show-more">
                <p-button [outlined]="true" (click)="viewAllTasks()">
                  View {{ activeTasks().length - 5 }} More Tasks
                </p-button>
              </div>
            </div>
          </p-tabPanel>

          <!-- Active Checklists Tab -->
          <p-tabPanel>
            <ng-template mat-tab-label>
              <i class="pi pi-circle"></i>
              Checklists
              <p-chip *ngIf="activeChecklists().length > 0" class="tab-badge">{{ activeChecklists().length }}</p-chip</ng-template>

            <div class="tab-content">
              <div *ngIf="activeChecklists().length === 0" class="empty-state">
                <i class="pi pi-circle"></i>
                <h4>No active checklists</h4>
                <p>All checklists are complete!</p>
              </div>

              <div *ngFor="let checklist of activeChecklists() | slice:0:3" class="checklist-item">
                <p-accordionTab>
                  <p-accordionTab-header>
                    <mat-panel-title>
                      <div class="checklist-header">
                        <h4>{{ checklist.title }}</h4>
                        <div class="checklist-meta">
                          <p-chip [color]="getPriorityColor(checklist.priority)" class="priority-chip">
                            {{ checklist.priority | titlecase }}
                          </p-chip>
                          <span class="completion-badge">
                            {{ checklist.completionPercentage }}% Complete
                          </span>
                        </div>
                      </div>
                    </mat-panel-title>
                    <mat-panel-description>
                      <div class="checklist-progress">
                        <p-progressBar [value]="checklist.completionPercentage" mode="determinate"></p-progressBar>
                        <span class="item-count">{{ getCompletedItemsCount(checklist) }}/{{ checklist.items.length || 0 }} items</span>
                      </div>
                    </mat-panel-description>
                  </mat-expansion-panel-header>

                  <div class="checklist-items">
                    <div *ngFor="let item of checklist.items" class="checklist-item-row">
                      <p-checkbox
                        [binary]="true" [ngModel]="item.isCompleted"
                        [disabled]="item.isCompleted && item.requiresVerification && !item.verifiedBy"
                        (change)="toggleChecklistItem(checklist, item, $event.checked)">
                        {{ item.title }}
                      </p-checkbox>

                      <div class="item-status">
                        <i class="pi pi-circle"></i>
                        <i class="pi pi-circle"></i>
                        <i class="pi pi-circle"></i>
                      </div>
                    </div>
                  </div>

                  <div class="checklist-actions">
                    <p-button [outlined]="true" color="primary" (click)="viewChecklistDetails(checklist)">
                      <i class="pi pi-eye"></i>
                      View Details
                    </p-button>
                    <p-button color="accent"
                            *ngIf="checklist.completionPercentage === 100"
                            (click)="markChecklistComplete(checklist)">
                      <i class="pi pi-check-circle"></i>
                      Mark Complete
                    </p-button>
                  </div>
                </p-accordionTab>
              </div>

              <div *ngIf="activeChecklists().length > 3" class="show-more">
                <p-button [outlined]="true" (click)="viewAllTasks()">
                  View {{ activeChecklists().length - 3 }} More Checklists
                </p-button>
              </div>
            </div>
          </p-tabPanel>

          <!-- Overdue Items Tab -->
          <p-tabPanel *ngIf="overdueItems().length > 0">
            <ng-template mat-tab-label>
              <i class="pi pi-exclamation-triangle"></i>
              Overdue
              <p-chip color="warn" class="tab-badge">{{ overdueItems().length }}</p-chip</ng-template>

            <div class="tab-content">
              <div class="overdue-notice">
                <i class="pi pi-exclamation-triangle"></i>
                <p>You have {{ overdueItems().length }} overdue items that need attention.</p>
              </div>

              <div *ngFor="let item of overdueItems()" class="overdue-item">
                <div class="item-header">
                  <h4>{{ item.title }}</h4>
                  <p-chip color="warn">
                    {{ item.type === 'checklist' ? 'Checklist' : 'Task' }}
                  </p-chip>
                </div>
                <div class="overdue-info">
                  <span class="due-date">
                    <i class="pi pi-clock"></i>
                    Due: {{ item.dueDate | date:'mediumDate' }}
                  </span>
                  <span class="days-overdue">
                    {{ getDaysOverdue(item.dueDate!) }} days overdue
                  </span>
                </div>
                <div class="item-actions">
                  <p-button color="primary" (click)="workOnItem(item)">
                    <i class="pi pi-circle"></i>
                    Work on This
                  </p-button>
                </div>
              </div>
            </div>
          </p-tabPanel>
        </p-tabView</ng-template></p-card>
  `,
  styles: [`
    .staff-tasks-widget {
      height: 100%;

      mat-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        mat-card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0;

          mat-icon {
            color: var(--mdc-theme-primary);
          }

          mat-chip {
            margin-left: 8px;
          }
        }

        .widget-actions {
          display: flex;
          gap: 8px;
        }
      }

      .tasks-tabs {
        .tab-badge {
          margin-left: 8px;
          font-size: 0.7rem;
          height: 18px;
          line-height: 18px;
        }

        .tab-content {
          padding: 16px 0;
          max-height: 500px;
          overflow-y: auto;

          .empty-state {
            text-align: center;
            padding: 32px 16px;
            color: var(--mdc-theme-on-surface-variant);

            mat-icon {
              font-size: 3rem;
              width: 3rem;
              height: 3rem;
              margin-bottom: 16px;
              opacity: 0.5;
            }

            h4 {
              margin: 0 0 8px 0;
              font-weight: 500;
            }

            p {
              margin: 0;
              font-size: 0.9rem;
            }
          }

          .task-item,
          .overdue-item {
            padding: 16px;
            border: 1px solid var(--mdc-theme-outline-variant);
            border-radius: 8px;
            margin-bottom: 12px;
            background: var(--mdc-theme-surface);

            .task-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;

              h4 {
                margin: 0;
                font-weight: 500;
                color: var(--mdc-theme-on-surface);
                flex: 1;
              }

              .task-meta {
                display: flex;
                gap: 4px;
                flex-wrap: wrap;

                .priority-chip,
                .status-chip {
                  font-size: 0.75rem;
                  height: 20px;
                  line-height: 20px;
                }
              }
            }

            .task-description {
              margin: 0 0 12px 0;
              color: var(--mdc-theme-on-surface-variant);
              font-size: 0.9rem;
              line-height: 1.4;
            }

            .task-progress {
              margin-bottom: 12px;

              .progress-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
                font-size: 0.8rem;

                .due-date {
                  display: flex;
                  align-items: center;
                  gap: 4px;

                  &.overdue {
                    color: #f44336;

                    mat-icon {
                      font-size: 1rem;
                      width: 1rem;
                      height: 1rem;
                    }
                  }
                }
              }

              mat-progress-bar {
                height: 6px;
              }
            }

            .task-actions,
            .item-actions {
              display: flex;
              gap: 8px;
              justify-content: flex-end;
            }
          }

          .checklist-item {
            margin-bottom: 12px;

            mat-expansion-panel {
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);

              .checklist-header {
                width: 100%;

                h4 {
                  margin: 0 0 4px 0;
                  font-weight: 500;
                }

                .checklist-meta {
                  display: flex;
                  align-items: center;
                  gap: 8px;

                  .completion-badge {
                    font-size: 0.8rem;
                    color: var(--mdc-theme-on-surface-variant);
                  }
                }
              }

              .checklist-progress {
                display: flex;
                align-items: center;
                gap: 8px;
                width: 100%;

                mat-progress-bar {
                  flex: 1;
                  height: 4px;
                }

                .item-count {
                  font-size: 0.8rem;
                  color: var(--mdc-theme-on-surface-variant);
                  white-space: nowrap;
                }
              }

              .checklist-items {
                margin: 16px 0;

                .checklist-item-row {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 8px 0;
                  border-bottom: 1px solid var(--mdc-theme-outline-variant);

                  &:last-child {
                    border-bottom: none;
                  }

                  mat-checkbox {
                    flex: 1;
                  }

                  .item-status {
                    display: flex;
                    gap: 4px;

                    mat-icon {
                      font-size: 1.2rem;
                      width: 1.2rem;
                      height: 1.2rem;
                    }
                  }
                }
              }

              .checklist-actions {
                display: flex;
                gap: 8px;
                justify-content: flex-end;
                margin-top: 16px;
              }
            }
          }

          .overdue-notice {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 8px;
            margin-bottom: 16px;

            mat-icon {
              color: #ff9800;
            }

            p {
              margin: 0;
              color: #e65100;
              font-weight: 500;
            }
          }

          .overdue-item {
            .item-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              h4 {
                margin: 0;
                color: var(--mdc-theme-on-surface);
              }
            }

            .overdue-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              font-size: 0.9rem;

              .due-date {
                display: flex;
                align-items: center;
                gap: 4px;
                color: var(--mdc-theme-on-surface-variant);
              }

              .days-overdue {
                color: #f44336;
                font-weight: 500;
              }
            }
          }

          .show-more {
            text-align: center;
            margin-top: 16px;
          }
        }
      }
    }

    @media (max-width: 600px) {
      .staff-tasks-widget {
        .tasks-tabs {
          .tab-content {
            .task-item,
            .overdue-item {
              .task-header {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;

                .task-meta {
                  justify-content: flex-start;
                }
              }

              .task-actions,
              .item-actions {
                flex-direction: column;
                align-items: stretch;
              }
            }

            .checklist-item {
              mat-expansion-panel {
                .checklist-actions {
                  flex-direction: column;
                  align-items: stretch;
                }
              }
            }
          }
        }
      }
    }
  `]
})
export class StaffTasksWidgetComponent implements OnInit {
  @Input() staffId!: string;
  @Input() businessId!: string;

  private taskService = inject(TaskManagementService);
  private aiService = inject(AITaskAssistantService);
  private snackBar = inject(MessageService);

  // Data signals
  tasks = signal<Task[]>([]);
  checklists = signal<Checklist[]>([]);
  today = new Date();

  // Computed values
  activeTasks = computed(() =>
    this.tasks().filter(task => !['completed', 'cancelled'].includes(task.status))
  );

  activeChecklists = computed(() =>
    this.checklists().filter(checklist => !['completed', 'cancelled'].includes(checklist.status))
  );

  overdueItems = computed(() => {
    const overdueTasks = this.activeTasks().filter(task =>
      task.dueDate && task.dueDate < this.today
    );
    const overdueChecklists = this.activeChecklists().filter(checklist =>
      checklist.dueDate && checklist.dueDate < this.today
    );
    return [...overdueTasks, ...overdueChecklists];
  });

  totalActiveTasks = computed(() =>
    this.activeTasks().length + this.activeChecklists().length
  );

  ngOnInit(): void {
    this.loadStaffTasks();
  }

  private loadStaffTasks(): void {
    // Load tasks assigned to this staff member
    this.taskService.getStaffTasks(this.staffId, false).subscribe(tasks => {
      this.tasks.set(tasks);
    });

    // Load checklists assigned to this staff member
    this.taskService.getChecklists({
      businessId: this.businessId,
      assignedTo: [this.staffId],
      includeCompleted: false
    }).subscribe(checklists => {
      this.checklists.set(checklists);
    });
  }

  // Task actions
  updateTaskProgress(task: Task): void {
    // Open task progress update dialog
    console.log('Update task progress:', task);
  }

  markTaskComplete(task: Task): void {
    this.taskService.updateTask(task.id, {
      status: 'completed',
      progress: 100,
      completedDate: new Date(),
      lastModifiedBy: this.staffId
    }).subscribe({
      next: () => {
        this.snackBar.open('Task marked as complete!', 'Close', { duration: 3000 });
        this.loadStaffTasks();
      },
      error: (error) => {
        this.snackBar.open('Error updating task', 'Close', { duration: 3000 });
        console.error('Task update error:', error);
      }
    });
  }

  // Checklist actions
  toggleChecklistItem(checklist: Checklist, item: ChecklistItem, completed: boolean): void {
    this.taskService.updateChecklistItem(checklist.id, item.id, {
      isCompleted: completed,
      completedBy: completed ? this.staffId : undefined,
      completedAt: completed ? new Date() : undefined
    }).subscribe({
      next: () => {
        this.loadStaffTasks();
      },
      error: (error) => {
        this.snackBar.open('Error updating checklist item', 'Close', { duration: 3000 });
        console.error('Checklist update error:', error);
      }
    });
  }

  markChecklistComplete(checklist: Checklist): void {
    this.taskService.updateTask(checklist.id, {
      status: 'completed',
      completedDate: new Date(),
      lastModifiedBy: this.staffId
    }).subscribe({
      next: () => {
        this.snackBar.open('Checklist marked as complete!', 'Close', { duration: 3000 });
        this.loadStaffTasks();
      },
      error: (error) => {
        this.snackBar.open('Error updating checklist', 'Close', { duration: 3000 });
        console.error('Checklist update error:', error);
      }
    });
  }

  viewChecklistDetails(checklist: Checklist): void {
    // Navigate to checklist details or open dialog
    console.log('View checklist details:', checklist);
  }

  // AI and analytics
  async getAITaskSuggestions(): Promise<void> {
    try {
      const suggestions = await this.aiService.generateTaskSuggestions(
        this.staffId,
        this.businessId,
        'Personal productivity improvement suggestions'
      );

      // Show suggestions in a dialog or notification
      this.snackBar.open(`Generated ${suggestions.length} AI task suggestions`, 'View', {
        duration: 5000
      });
    } catch (error) {
      this.snackBar.open('Error getting AI suggestions', 'Close', { duration: 3000 });
    }
  }

  async analyzeProductivity(): Promise<void> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const analysis = await this.aiService.analyzeStaffProductivity(this.staffId, {
        start: startDate,
        end: endDate
      });

      // Show analysis results
      this.snackBar.open('Productivity analysis complete', 'View', { duration: 5000 });
    } catch (error) {
      this.snackBar.open('Error analyzing productivity', 'Close', { duration: 3000 });
    }
  }

  // Navigation
  viewAllTasks(): void {
    // Navigate to full tasks view
    console.log('Navigate to all tasks');
  }

  workOnItem(item: Task | Checklist): void {
    // Navigate to specific task/checklist
    console.log('Work on item:', item);
  }

  // Utility methods
  getPriorityColor(priority: string): string {
    switch (priority) {
      case 'critical':
      case 'urgent':
        return 'warn';
      case 'high':
        return 'accent';
      case 'medium':
        return 'primary';
      default:
        return 'basic';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'accent';
      case 'in-progress':
        return 'primary';
      case 'overdue':
        return 'warn';
      default:
        return 'basic';
    }
  }

  getCompletedItemsCount(checklist: Checklist): number {
    return checklist.items?.filter(item => item.isCompleted).length || 0;
  }

  getDaysOverdue(dueDate: Date): number {
    const diffTime = this.today.getTime() - dueDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
