<div
  class="business-selector"
  [class.collapsed]="collapsed"
  tabindex="0"
  role="button"
  [attr.aria-label]="ariaLabel"
  (click)="openSelectorDialog()"
  (keydown.enter)="openSelectorDialog()"
  (keydown.space)="openSelectorDialog()"
>
  <ng-container *ngIf="!collapsed; else collapsedView">
    <div class="business-header">
      <div class="logo-group">
        <ng-container *ngFor="let b of selectedBusinesses.slice(0, 1)">
          <div class="business-logo-container">
            <img *ngIf="b.logoUrl" [src]="b.logoUrl" [alt]="b.name" class="business-logo" />
            <div *ngIf="!b.logoUrl" class="business-icon-container">
              <i class="pi pi-building business-icon"></i>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="business-info">
        <span class="business-name">
          {{ isOneView ? 'OneView: Multiple' : (selectedBusinesses[0].name || 'Select business') }}
        </span>
        <p-chip *ngIf="isOneView"
                label="OneView"
                icon="pi pi-eye"
                styleClass="oneview-chip">
        </p-chip>
        <div class="business-subtitle" *ngIf="!isOneView">
          {{ selectedBusinesses.length }} location{{ selectedBusinesses.length !== 1 ? 's' : '' }}
        </div>
      </div>
    </div>
  </ng-container>
  <ng-template #collapsedView>
    <div class="business-selector-collapsed">
      <p-button icon="pi pi-building"
                [text]="true"
                [rounded]="true"
                [pTooltip]="selectedBusinesses[0]?.name || 'Select business'"
                tooltipPosition="right"
                aria-label="Select business"
                class="collapsed-business-btn"
                [ngStyle]="{
                  'font-size': '48px',
                  'width': '48px',
                  'height': '48px',
                  'display': 'flex',
                  'align-items': 'center',
                  'justify-content': 'center',
                  'transition': 'all 0.3s ease'
                }">
      </p-button>
    </div></ng-template>
</div>
