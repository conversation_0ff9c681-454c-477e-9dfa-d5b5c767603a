import { Injectable, inject } from '@angular/core';
import { Auth, User, user, signInWithEmailAndPassword, signOut } from '@angular/fire/auth';
import { Firestore, doc, getDoc, collection, query, where, getDocs } from '@angular/fire/firestore';
import { Observable, from, map, switchMap, of, BehaviorSubject, combineLatest } from 'rxjs';
import { StaffMember, StaffPinAuth } from '../models/staff.model';

export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: 'admin' | 'manager' | 'staff';
  staffId?: string;
  businessIds: string[];
  primaryBusinessId: string;
  createdAt: Date;
  lastLoginAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private auth = inject(Auth);
  private firestore = inject(Firestore);
  
  // Current user observable
  user$ = user(this.auth);
  
  // Current user profile with additional data
  userProfile$: Observable<UserProfile | null> = this.user$.pipe(
    switchMap(user => {
      if (!user) return of(null);
      return this.getUserProfile(user.uid);
    })
  );
  
  // Current staff member data
  staffMember$: Observable<StaffMember | null> = this.userProfile$.pipe(
    switchMap(profile => {
      if (!profile?.staffId) return of(null);
      return this.getStaffMember(profile.staffId);
    })
  );
  
  // Combined authentication state
  authState$ = combineLatest([
    this.user$,
    this.userProfile$,
    this.staffMember$
  ]).pipe(
    map(([user, profile, staff]) => ({
      user,
      profile,
      staff,
      isAuthenticated: !!user,
      isStaff: !!staff,
      hasRole: (role: string) => profile?.role === role,
      hasPermission: (permission: string) => staff?.permissions?.includes(permission) || false
    }))
  );

  constructor() {}

  // Email/password authentication
  signInWithEmail(email: string, password: string): Observable<UserProfile | null> {
    return from(signInWithEmailAndPassword(this.auth, email, password)).pipe(
      switchMap(credential => {
        this.updateLastLogin(credential.user.uid);
        return this.getUserProfile(credential.user.uid);
      })
    );
  }

  // PIN-based authentication for TimeHub
  signInWithPin(pin: string, deviceId?: string): Observable<{ success: boolean; staffMember?: StaffMember; error?: string }> {
    return from(this.validatePin(pin)).pipe(
      switchMap(result => {
        if (!result.isValid || !result.staffId) {
          return of({ success: false, error: result.error || 'Invalid PIN' });
        }
        
        return this.getStaffMember(result.staffId).pipe(
          map(staffMember => {
            if (!staffMember) {
              return { success: false, error: 'Staff member not found' };
            }
            
            // Update last used timestamp for PIN
            this.updatePinLastUsed(result.staffId!, deviceId);
            
            return { success: true, staffMember };
          })
        );
      })
    );
  }

  // Sign out
  signOut(): Observable<void> {
    return from(signOut(this.auth));
  }

  // Get user profile from Firestore
  private getUserProfile(uid: string): Observable<UserProfile | null> {
    const userDoc = doc(this.firestore, 'users', uid);
    return from(getDoc(userDoc)).pipe(
      map(docSnap => {
        if (!docSnap.exists()) return null;
        const data = docSnap.data();
        return {
          uid,
          email: data['email'],
          displayName: data['displayName'],
          photoURL: data['photoURL'],
          role: data['role'],
          staffId: data['staffId'],
          businessIds: data['businessIds'] || [],
          primaryBusinessId: data['primaryBusinessId'],
          createdAt: data['createdAt']?.toDate(),
          lastLoginAt: data['lastLoginAt']?.toDate()
        } as UserProfile;
      })
    );
  }

  // Get staff member data
  private getStaffMember(staffId: string): Observable<StaffMember | null> {
    const staffDoc = doc(this.firestore, 'staff', staffId);
    return from(getDoc(staffDoc)).pipe(
      map(docSnap => {
        if (!docSnap.exists()) return null;
        const data = docSnap.data();
        return {
          id: staffId,
          ...data,
          hireDate: data['hireDate']?.toDate(),
          terminationDate: data['terminationDate']?.toDate(),
          createdAt: data['createdAt']?.toDate(),
          updatedAt: data['updatedAt']?.toDate(),
          lastLoginAt: data['lastLoginAt']?.toDate()
        } as StaffMember;
      })
    );
  }

  // Validate PIN for TimeHub authentication
  private async validatePin(pin: string): Promise<{ isValid: boolean; staffId?: string; error?: string }> {
    try {
      // Hash the PIN for comparison (in production, use proper hashing)
      const hashedPin = await this.hashPin(pin);
      
      // Query for staff with matching PIN
      const pinQuery = query(
        collection(this.firestore, 'staffPinAuth'),
        where('pin', '==', hashedPin),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(pinQuery);
      
      if (querySnapshot.empty) {
        return { isValid: false, error: 'Invalid PIN' };
      }
      
      const pinDoc = querySnapshot.docs[0];
      const pinData = pinDoc.data() as StaffPinAuth;
      
      // Check if account is locked
      if (pinData.lockedUntil && pinData.lockedUntil > new Date()) {
        return { isValid: false, error: 'Account temporarily locked' };
      }
      
      // Reset failed attempts on successful validation
      if (pinData.failedAttempts > 0) {
        // Reset failed attempts (implement this)
      }
      
      return { isValid: true, staffId: pinData.staffId };
    } catch (error) {
      console.error('PIN validation error:', error);
      return { isValid: false, error: 'Authentication error' };
    }
  }

  // Hash PIN (implement proper hashing in production)
  private async hashPin(pin: string): Promise<string> {
    // This is a simple example - use proper hashing like bcrypt in production
    const encoder = new TextEncoder();
    const data = encoder.encode(pin + 'salt'); // Add proper salt
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  // Update last login timestamp
  private updateLastLogin(uid: string): void {
    // Implement Firestore update
  }

  // Update PIN last used timestamp
  private updatePinLastUsed(staffId: string, deviceId?: string): void {
    // Implement Firestore update
  }

  // Check if user has specific role
  hasRole(role: string): Observable<boolean> {
    return this.userProfile$.pipe(
      map(profile => profile?.role === role || false)
    );
  }

  // Check if user has specific permission
  hasPermission(permission: string): Observable<boolean> {
    return this.staffMember$.pipe(
      map(staff => staff?.permissions?.includes(permission) || false)
    );
  }

  // Get current user ID
  getCurrentUserId(): string | null {
    return this.auth.currentUser?.uid || null;
  }

  // Get current staff ID
  getCurrentStaffId(): Observable<string | null> {
    return this.userProfile$.pipe(
      map(profile => profile?.staffId || null)
    );
  }
}
