# StaffManager Deployment Guide

## 📋 Overview

This guide covers deployment strategies for StaffManager across different environments, from development to production. The application supports multiple deployment targets including Firebase Hosting, traditional web servers, and containerized environments.

## 🚀 Quick Deployment Checklist

- [ ] Environment configuration
- [ ] Firebase project setup
- [ ] Build optimization
- [ ] Security configuration
- [ ] Performance testing
- [ ] Monitoring setup
- [ ] Backup strategy

## 🔧 Environment Configuration

### Development Environment

```bash
# Install dependencies
npm install

# Start development server
npm start

# The app will automatically find an available port
# Default: http://localhost:4200 (or next available port)
```

### Environment Files

Create environment files for different deployment targets:

#### `src/environments/environment.ts` (Development)
```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: "dev-api-key",
    authDomain: "staffmanager-dev.firebaseapp.com",
    projectId: "staffmanager-dev",
    storageBucket: "staffmanager-dev.appspot.com",
    messagingSenderId: "123456789",
    appId: "dev-app-id"
  },
  geminiApiKey: "dev-gemini-key",
  enableAnalytics: false,
  enableLogging: true,
  apiUrl: "http://localhost:3000/api"
};
```

#### `src/environments/environment.prod.ts` (Production)
```typescript
export const environment = {
  production: true,
  firebase: {
    apiKey: "prod-api-key",
    authDomain: "staffmanager-prod.firebaseapp.com",
    projectId: "staffmanager-prod",
    storageBucket: "staffmanager-prod.appspot.com",
    messagingSenderId: "987654321",
    appId: "prod-app-id"
  },
  geminiApiKey: "prod-gemini-key",
  enableAnalytics: true,
  enableLogging: false,
  apiUrl: "https://api.staffmanager.com"
};
```

#### `src/environments/environment.staging.ts` (Staging)
```typescript
export const environment = {
  production: false,
  firebase: {
    apiKey: "staging-api-key",
    authDomain: "staffmanager-staging.firebaseapp.com",
    projectId: "staffmanager-staging",
    storageBucket: "staffmanager-staging.appspot.com",
    messagingSenderId: "555666777",
    appId: "staging-app-id"
  },
  geminiApiKey: "staging-gemini-key",
  enableAnalytics: true,
  enableLogging: true,
  apiUrl: "https://staging-api.staffmanager.com"
};
```

## 🔥 Firebase Deployment

### Prerequisites

1. **Firebase CLI Installation**
```bash
npm install -g firebase-tools
```

2. **Firebase Login**
```bash
firebase login
```

3. **Firebase Project Initialization**
```bash
firebase init hosting
```

### Firebase Configuration

#### `firebase.json`
```json
{
  "hosting": {
    "public": "dist/staffmanager-web",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      },
      {
        "source": "**/*.@(jpg|jpeg|gif|png|svg|webp)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      }
    ]
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  }
}
```

### Deployment Commands

#### Production Deployment
```bash
# Build for production
npm run build:prod

# Deploy to Firebase
firebase deploy --project production

# Deploy specific services
firebase deploy --only hosting --project production
firebase deploy --only firestore:rules --project production
```

#### Staging Deployment
```bash
# Build for staging
npm run build:staging

# Deploy to staging
firebase deploy --project staging
```

### Multi-Environment Setup

#### `.firebaserc`
```json
{
  "projects": {
    "default": "staffmanager-dev",
    "development": "staffmanager-dev",
    "staging": "staffmanager-staging",
    "production": "staffmanager-prod"
  },
  "targets": {
    "staffmanager-prod": {
      "hosting": {
        "app": ["staffmanager-prod"]
      }
    }
  }
}
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build:prod

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=build /app/dist/staffmanager-web /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

### Nginx Configuration

#### `nginx.conf`
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Angular routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API proxy (if needed)
        location /api/ {
            proxy_pass http://api-server:3000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

### Docker Commands

```bash
# Build image
docker build -t staffmanager:latest .

# Run container
docker run -d -p 80:80 --name staffmanager staffmanager:latest

# Docker Compose
docker-compose up -d
```

### Docker Compose

#### `docker-compose.yml`
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  # Optional: Add reverse proxy
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
      - ./proxy.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    restart: unless-stopped
```

## ☁️ Cloud Platform Deployment

### AWS S3 + CloudFront

#### Build and Deploy Script
```bash
#!/bin/bash

# Build application
npm run build:prod

# Sync to S3
aws s3 sync dist/staffmanager-web/ s3://staffmanager-prod --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id E1234567890 --paths "/*"
```

#### CloudFront Configuration
```json
{
  "Origins": [
    {
      "DomainName": "staffmanager-prod.s3.amazonaws.com",
      "Id": "S3-staffmanager-prod",
      "S3OriginConfig": {
        "OriginAccessIdentity": ""
      }
    }
  ],
  "DefaultCacheBehavior": {
    "TargetOriginId": "S3-staffmanager-prod",
    "ViewerProtocolPolicy": "redirect-to-https",
    "Compress": true,
    "DefaultTTL": 86400
  },
  "CustomErrorResponses": [
    {
      "ErrorCode": 404,
      "ResponseCode": 200,
      "ResponsePagePath": "/index.html"
    }
  ]
}
```

### Vercel Deployment

#### `vercel.json`
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist/staffmanager-web"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "SAMEORIGIN"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ]
}
```

### Netlify Deployment

#### `netlify.toml`
```toml
[build]
  publish = "dist/staffmanager-web"
  command = "npm run build:prod"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
```

## 🔒 Security Configuration

### Content Security Policy

#### `index.html`
```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://apis.google.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://firestore.googleapis.com https://generativelanguage.googleapis.com;
">
```

### Environment Variables Security

```bash
# Use environment variables for sensitive data
export FIREBASE_API_KEY="your-api-key"
export GEMINI_API_KEY="your-gemini-key"

# Or use .env files (not committed to version control)
echo "FIREBASE_API_KEY=your-api-key" > .env.local
echo "GEMINI_API_KEY=your-gemini-key" >> .env.local
```

## 📊 Performance Optimization

### Build Optimization

#### `angular.json` Production Configuration
```json
{
  "configurations": {
    "production": {
      "budgets": [
        {
          "type": "initial",
          "maximumWarning": "500kb",
          "maximumError": "1mb"
        },
        {
          "type": "anyComponentStyle",
          "maximumWarning": "2kb",
          "maximumError": "4kb"
        }
      ],
      "outputHashing": "all",
      "sourceMap": false,
      "namedChunks": false,
      "aot": true,
      "extractLicenses": true,
      "vendorChunk": false,
      "buildOptimizer": true
    }
  }
}
```

### Service Worker Configuration

#### `ngsw-config.json`
```json
{
  "$schema": "./node_modules/@angular/service-worker/config/schema.json",
  "index": "/index.html",
  "assetGroups": [
    {
      "name": "app",
      "installMode": "prefetch",
      "resources": {
        "files": [
          "/favicon.ico",
          "/index.html",
          "/manifest.webmanifest",
          "/*.css",
          "/*.js"
        ]
      }
    },
    {
      "name": "assets",
      "installMode": "lazy",
      "updateMode": "prefetch",
      "resources": {
        "files": [
          "/assets/**",
          "/*.(eot|svg|cur|jpg|png|webp|gif|otf|ttf|woff|woff2|ani)"
        ]
      }
    }
  ]
}
```

## 📈 Monitoring and Analytics

### Firebase Analytics Setup

```typescript
// app.config.ts
import { initializeApp } from 'firebase/app';
import { getAnalytics } from 'firebase/analytics';

export const appConfig: ApplicationConfig = {
  providers: [
    // ... other providers
    {
      provide: 'analytics',
      useFactory: () => {
        const app = initializeApp(environment.firebase);
        return getAnalytics(app);
      }
    }
  ]
};
```

### Error Monitoring

```typescript
// error-handler.service.ts
@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
  handleError(error: any): void {
    console.error('Global error:', error);
    
    // Send to monitoring service
    if (environment.production) {
      // Sentry, LogRocket, etc.
    }
  }
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions

#### `.github/workflows/deploy.yml`
```yaml
name: Deploy to Firebase

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test:ci
    
    - name: Build application
      run: npm run build:prod
    
    - name: Deploy to Firebase
      uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
        projectId: staffmanager-prod
```

## 🆘 Troubleshooting

### Common Deployment Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Clear npm cache: `npm cache clean --force`
   - Delete node_modules and reinstall

2. **Firebase Deployment Issues**
   - Verify Firebase CLI version
   - Check project permissions
   - Validate firebase.json configuration

3. **Environment Configuration**
   - Verify environment variables
   - Check API key permissions
   - Validate Firebase project settings

### Health Checks

```bash
# Check application health
curl -f http://localhost/health || exit 1

# Check Firebase connectivity
firebase projects:list

# Verify build output
ls -la dist/staffmanager-web/
```

## 📋 Post-Deployment Checklist

- [ ] Application loads correctly
- [ ] Authentication works
- [ ] Database connectivity verified
- [ ] All features functional
- [ ] Performance metrics acceptable
- [ ] Security headers present
- [ ] SSL certificate valid
- [ ] Monitoring alerts configured
- [ ] Backup strategy implemented

---

This deployment guide provides comprehensive instructions for deploying StaffManager across various platforms and environments, ensuring reliable and secure production deployments.
