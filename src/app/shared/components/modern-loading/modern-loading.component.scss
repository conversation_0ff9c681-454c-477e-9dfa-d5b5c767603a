.modern-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--sm-spacing-lg);
  min-height: 120px;
  
  &.loading-spinner {
    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--sm-spacing-md);
      
      mat-spinner {
        ::ng-deep circle {
          stroke: var(--sm-primary-main);
        }
      }
    }
  }
  
  &.loading-dots {
    .loading-dots {
      display: flex;
      align-items: center;
      gap: var(--sm-spacing-sm);
      flex-direction: column;
      
      .dot {
        width: 12px;
        height: 12px;
        background: var(--sm-primary-main);
        border-radius: 50%;
        animation: dotBounce 1.4s infinite ease-in-out both;
        display: inline-block;
        margin: 0 2px;
      }
    }
  }
  
  &.loading-skeleton {
    width: 100%;
    max-width: 300px;
    
    .loading-skeleton {
      width: 100%;
      
      .skeleton-line {
        height: 16px;
        background: linear-gradient(90deg, 
          var(--sm-background-surface) 25%, 
          var(--sm-border-light) 50%, 
          var(--sm-background-surface) 75%);
        background-size: 200% 100%;
        animation: skeletonShimmer 1.5s infinite;
        border-radius: var(--sm-border-radius-sm);
        margin-bottom: var(--sm-spacing-sm);
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  &.loading-pulse {
    .loading-pulse {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--sm-spacing-md);
      
      .pulse-circle {
        width: 60px;
        height: 60px;
        background: var(--sm-primary-main);
        border-radius: 50%;
        animation: pulseScale 2s infinite ease-in-out;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          top: -10px;
          left: -10px;
          right: -10px;
          bottom: -10px;
          border: 2px solid var(--sm-primary-main);
          border-radius: 50%;
          opacity: 0;
          animation: pulseRing 2s infinite ease-in-out;
        }
      }
    }
  }
  
  &.loading-wave {
    .loading-wave {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-direction: column;
      
      .wave-bar {
        width: 4px;
        height: 40px;
        background: var(--sm-primary-main);
        border-radius: 2px;
        animation: waveStretch 1.2s infinite ease-in-out;
        display: inline-block;
        margin: 0 1px;
      }
    }
  }
}

.loading-message {
  margin: 0;
  font-size: 0.875rem;
  color: var(--sm-text-secondary);
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.025em;
  animation: fadeInOut 2s infinite ease-in-out;
}

// Animations
@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulseScale {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes waveStretch {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

// Dark theme support
.dark-theme {
  .modern-loading-container {
    .dot {
      background: var(--sm-primary-light);
    }
    
    .skeleton-line {
      background: linear-gradient(90deg, 
        var(--sm-background-surface) 25%, 
        var(--sm-border-light) 50%, 
        var(--sm-background-surface) 75%);
    }
    
    .pulse-circle {
      background: var(--sm-primary-light);
      
      &::before {
        border-color: var(--sm-primary-light);
      }
    }
    
    .wave-bar {
      background: var(--sm-primary-light);
    }
    
    mat-spinner {
      ::ng-deep circle {
        stroke: var(--sm-primary-light);
      }
    }
  }
  
  .loading-message {
    color: var(--sm-text-secondary);
  }
}

// Responsive design
@media (max-width: 600px) {
  .modern-loading-container {
    padding: var(--sm-spacing-md);
    min-height: 80px;
    
    &.loading-pulse .pulse-circle {
      width: 40px;
      height: 40px;
    }
    
    &.loading-wave .wave-bar {
      height: 30px;
    }
  }
  
  .loading-message {
    font-size: 0.75rem;
  }
}
