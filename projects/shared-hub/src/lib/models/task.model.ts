// Task and Checklist Models for StaffHub
export interface Task {
  id: string;
  title: string;
  description?: string;
  type: 'task' | 'checklist';
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'urgent' | 'critical';
  category: 'administrative' | 'customer-service' | 'training' | 'maintenance' | 'project' | 'safety' | 'compliance' | 'custom';

  // Assignment and ownership
  assignedTo: string[]; // Staff IDs
  assignedBy: string; // Manager/Admin ID
  createdBy: string;
  businessId: string;
  departmentId?: string;

  // Scheduling and deadlines
  dueDate?: Date;
  startDate?: Date;
  completedDate?: Date;
  estimatedDuration?: number; // minutes
  actualDuration?: number; // minutes

  // Progress and completion
  progress: number; // 0-100
  completionRequirements?: CompletionRequirement[];
  verificationRequired: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;

  // Checklist items (for checklist type tasks)
  checklistItems?: ChecklistItem[];

  // Metadata and organization
  tags?: string[];
  location?: string;
  attachments?: TaskAttachment[];
  comments?: TaskComment[];
  dependencies?: string[]; // Task IDs that must be completed first

  // AI and automation
  aiGenerated?: boolean;
  aiSuggestions?: AISuggestion[];

  // Calendar integration
  calendarEventId?: string;
  syncWithCalendar: boolean;
  reminderSettings?: ReminderSettings;

  // Recurrence settings
  isRecurring?: boolean;
  recurrencePattern?: RecurrencePattern;
  recurrenceEndDate?: Date;
  parentTaskId?: string; // For recurring task instances

  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

export interface ChecklistItem {
  id: string;
  taskId: string;
  title: string;
  description?: string;
  isCompleted: boolean;
  completedBy?: string;
  completedAt?: Date;
  order: number;
  isRequired: boolean;
  verificationRequired: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;
  notes?: string;
}

export interface CompletionRequirement {
  id: string;
  type: 'photo' | 'signature' | 'location' | 'time-spent' | 'approval' | 'custom';
  description: string;
  isRequired: boolean;
  isMet: boolean;
  metBy?: string;
  metAt?: Date;
  data?: any; // Flexible data for different requirement types
}

export interface TaskAttachment {
  id: string;
  taskId: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  uploadedBy: string;
  uploadedAt: Date;
  description?: string;
}

export interface TaskComment {
  id: string;
  taskId: string;
  authorId: string;
  authorName: string;
  content: string;
  createdAt: Date;
  updatedAt?: Date;
  isInternal: boolean; // Internal comments vs. visible to assigned staff
  attachments?: TaskAttachment[];
}

export interface AISuggestion {
  id: string;
  type: 'optimization' | 'resource' | 'timeline' | 'priority' | 'dependency';
  title: string;
  description: string;
  confidence: number; // 0-1
  isApplied: boolean;
  appliedBy?: string;
  appliedAt?: Date;
  data?: any;
}

export interface ReminderSettings {
  enabled: boolean;
  reminderTimes: number[]; // Minutes before due date
  methods: ('email' | 'push' | 'sms')[];
  customMessage?: string;
}

export interface RecurrencePattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
  interval: number; // Every X days/weeks/months/years
  daysOfWeek?: number[]; // For weekly recurrence (0-6, Sunday-Saturday)
  dayOfMonth?: number; // For monthly recurrence
  endType: 'never' | 'after-occurrences' | 'on-date';
  occurrences?: number; // For 'after-occurrences' end type
  endDate?: Date; // For 'on-date' end type
}

// Task analytics and reporting
export interface TaskAnalytics {
  staffId: string;
  businessId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  
  // Overall metrics
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  averageCompletionTime: number; // hours
  completionRate: number; // percentage
  
  // By category
  tasksByCategory: { [category: string]: number };
  completionByCategory: { [category: string]: number };
  
  // By priority
  tasksByPriority: { [priority: string]: number };
  
  // Performance trends
  productivityScore: number;
  improvementAreas: string[];
  strengths: string[];
}
