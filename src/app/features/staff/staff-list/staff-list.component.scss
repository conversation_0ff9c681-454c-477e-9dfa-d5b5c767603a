.staff-list-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

// Header
.staff-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  .header-left {
    h1 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
      font-size: 2rem;
      font-weight: 600;
      color: var(--sm-text-primary, #1a1a1a);

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: var(--sm-primary-main, #1976d2);
      }
    }

    p {
      margin: 0;
      color: var(--sm-text-secondary, #666666);
      font-size: 1rem;
    }
  }

  .header-actions {
    button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// Filters
.filters-card {
  margin-bottom: 24px;

  .filters-row {
    display: flex;
    gap: 16px;
    align-items: center;

    .search-field {
      flex: 1;
      max-width: 400px;
    }
  }
}

// Stats Row
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .stat-icon {
        font-size: 2.5rem;
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        padding: 12px;

        &.active {
          background: rgba(25, 118, 210, 0.1);
          color: var(--sm-primary-main, #1976d2);
        }

        &.online {
          background: rgba(76, 175, 80, 0.1);
          color: #4caf50;
        }

        &.departments {
          background: rgba(156, 39, 176, 0.1);
          color: var(--sm-secondary-main, #9c27b0);
        }
      }

      .stat-text {
        h3 {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--sm-text-primary, #1a1a1a);
        }

        p {
          margin: 0;
          color: var(--sm-text-secondary, #666666);
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Table
.table-card {
  .table-container {
    overflow-x: auto;
  }

  .staff-table {
    width: 100%;

    .staff-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--sm-primary-main, #1976d2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;

        mat-icon {
          font-size: 1.25rem;
          width: 1.25rem;
          height: 1.25rem;
        }
      }

      .info {
        .name {
          font-weight: 500;
          color: var(--sm-text-primary, #1a1a1a);
        }

        .email {
          font-size: 0.875rem;
          color: var(--sm-text-secondary, #666666);
        }
      }
    }

    .position-info {
      .position {
        font-weight: 500;
        color: var(--sm-text-primary, #1a1a1a);
      }

      .department {
        font-size: 0.875rem;
        color: var(--sm-text-secondary, #666666);
      }
    }

    .actions {
      display: flex;
      gap: 4px;
    }

    .staff-row {
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }
}

// Status chips
mat-chip {
  &.status-active {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
  }

  &.status-inactive {
    background: rgba(158, 158, 158, 0.1);
    color: #9e9e9e;
  }

  &.status-on-leave {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
  }

  &.status-terminated {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
  }
}

// Pagination
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;

  .pagination-info {
    color: var(--sm-text-secondary, #666666);
    font-size: 0.875rem;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    span {
      font-size: 0.875rem;
      color: var(--sm-text-secondary, #666666);
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .staff-list-container {
    padding: 16px;
  }

  .staff-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .stats-row {
    grid-template-columns: 1fr;
  }

  .filters-row {
    flex-direction: column;
    align-items: stretch;

    .search-field {
      max-width: none;
    }
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .staff-table {
    .staff-info {
      .info {
        .email {
          display: none;
        }
      }
    }

    .position-info {
      .department {
        display: none;
      }
    }
  }
}
