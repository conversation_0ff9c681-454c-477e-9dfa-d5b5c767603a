<div class="dialog-header">
  <span>Select businesses</span>
  <p-button text="true" (click)="cancel()" aria-label="Close dialog">
    <i class="pi pi-times"></i>
  </p-button>
</div>
<div class="dialog-search">
  <i class="pi pi-search"></i>
  <input
    type="text"
    [(ngModel)]="search"
    placeholder="Search businesses"
    aria-label="Search businesses"
  />
</div>
<div class="dialog-actions">
  <p-button outlined="true" (click)="selectAll()" [disabled]="selectedIds.length === data.businesses.length">Select All</p-button>
  <p-button outlined="true" (click)="clearAll()" [disabled]="selectedIds.length === 0">Clear</p-button>
  <p-chip *ngIf="selectedIds.length > 1" severity="secondary" class="oneview-chip">
    <i class="pi pi-eye"></i>
    OneView Active
  </p-chip>
</div>
<div class="dialog-list">
  <div
    class="dialog-list-item"
    *ngFor="let b of filteredBusinesses"
    (click)="toggleBusiness(b.id)"
    [class.selected]="selectedIds.includes(b.id)"
    tabindex="0"
    (keydown.enter)="toggleBusiness(b.id)"
    (keydown.space)="toggleBusiness(b.id)"
    attr.aria-checked="{{selectedIds.includes(b.id)}}"
    role="checkbox"
  >
    <img *ngIf="b.logoUrl" [src]="b.logoUrl" [alt]="b.name" class="business-logo" />
    <i class="pi pi-circle"></i>
    <span class="business-name">{{ b.name }}</span>
    <p-checkbox [binary]="true" [ngModel]="selectedIds.includes(b.id)" tabindex="-1"></p-checkbox>
  </div>
<div class="dialog-footer">
  <span class="selected-count">{{ selectedIds.length }}/20 selected</span>
  <p-button severity="primary" (click)="confirm()" [disabled]="selectedIds.length === 0">Confirm</p-button>
</div>
