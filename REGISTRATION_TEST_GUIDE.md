# Registration Testing Guide

## 🧪 Registration Test Plan

**Test Date**: December 19, 2024  
**Test URL**: http://localhost:52213/auth/register  
**Objective**: Verify user registration and authentication flow

---

## 📋 Test Cases

### **Test Case 1: Admin User Registration**
**Purpose**: Create an admin user for full system access

**Test Data**:
- **Full Name**: `Admin User`
- **Email**: `<EMAIL>`
- **Password**: `AdminPassword123!`
- **Confirm Password**: `AdminPassword123!`
- **Role**: `Admin`

**Expected Results**:
- ✅ Registration form accepts all inputs
- ✅ Password validation passes
- ✅ User account created in Firebase Auth
- ✅ User profile created in Firestore
- ✅ Success message displayed
- ✅ Redirect to dashboard
- ✅ User remains logged in

### **Test Case 2: Manager User Registration**
**Purpose**: Create a manager user for testing role-based access

**Test Data**:
- **Full Name**: `Manager User`
- **Email**: `<EMAIL>`
- **Password**: `ManagerPassword123!`
- **Confirm Password**: `ManagerPassword123!`
- **Role**: `Manager`

### **Test Case 3: Staff User Registration**
**Purpose**: Create a staff user for testing basic access

**Test Data**:
- **Full Name**: `Staff User`
- **Email**: `<EMAIL>`
- **Password**: `StaffPassword123!`
- **Confirm Password**: `StaffPassword123!`
- **Role**: `Staff`

---

## 🔍 What to Monitor During Testing

### **Browser Console Output**
Watch for:
- Authentication state changes
- Profile creation logs
- Any error messages
- Firebase connection status

### **Network Tab**
Monitor:
- Firebase Auth API calls
- Firestore document creation
- Response status codes
- Error responses

### **Application Behavior**
Observe:
- Form validation
- Loading states
- Success/error messages
- Navigation after registration

---

## 📊 Expected Console Output

### **Successful Registration Should Show**:
```
🔧 Auth Test [INFO]: Auth state changed: User <EMAIL>
🔧 Auth Test [INFO]: Profile state changed: <NAME_EMAIL>
```

### **Firebase Operations Should Include**:
- User creation in Firebase Auth
- Profile document creation in Firestore
- Authentication state update
- Redirect to dashboard

---

## 🚨 Common Issues to Watch For

### **Registration Failures**:
- **Email already exists**: Try different email
- **Weak password**: Ensure password meets requirements
- **Network errors**: Check Firebase connection
- **Validation errors**: Verify all fields completed

### **Authentication Issues**:
- **User not persisting**: Check session management
- **Profile not created**: Verify Firestore permissions
- **Redirect failures**: Check route configuration

---

## 🔧 Debugging Steps

### **If Registration Fails**:
1. **Check Browser Console**: Look for error messages
2. **Verify Form Data**: Ensure all fields are valid
3. **Check Network Tab**: Look for failed API calls
4. **Test Firebase Connection**: Use debug component

### **If Authentication Doesn't Persist**:
1. **Check Auth State**: Monitor user observable
2. **Verify Profile Creation**: Check Firestore console
3. **Test Session**: Refresh page and check if user remains logged in

---

## 🎯 Success Criteria

### **Registration Test Passes When**:
- [ ] User account created successfully
- [ ] No console errors during registration
- [ ] Success message displayed
- [ ] User redirected to dashboard
- [ ] Authentication state shows logged-in user
- [ ] User profile created in Firestore

### **Authentication Test Passes When**:
- [ ] User remains logged in after page refresh
- [ ] Auth debug component shows user data
- [ ] Dashboard is accessible without redirect to login
- [ ] User can navigate protected routes

---

## 📝 Test Results Template

### **Test Execution Log**:

**Test Case 1 - Admin Registration**:
- [ ] Form submission: ✅ Success / ❌ Failed
- [ ] User creation: ✅ Success / ❌ Failed  
- [ ] Profile creation: ✅ Success / ❌ Failed
- [ ] Authentication: ✅ Success / ❌ Failed
- [ ] Dashboard access: ✅ Success / ❌ Failed

**Console Output**:
```
[Record any console messages here]
```

**Issues Found**:
```
[Record any issues or errors here]
```

---

## 🚀 Next Steps After Successful Registration

### **Immediate Actions**:
1. **Test Login Flow**: Logout and login with created user
2. **Verify Persistence**: Refresh page and check auth state
3. **Test Route Guards**: Re-enable guards and test access
4. **Create Additional Users**: Test different roles

### **Development Progression**:
1. **Enable Route Guards**: Restore authentication protection
2. **Replace Mock Data**: Connect staff forms to real database
3. **Test Full Workflows**: End-to-end user journeys
4. **Add Error Handling**: Improve user experience

---

## 📞 Support Information

### **If Tests Fail**:
- Check `AUTHENTICATION_DIAGNOSIS.md` for troubleshooting
- Use debug component at `/debug/auth-test`
- Review Firebase console for user creation
- Check browser developer tools for errors

### **Firebase Console Access**:
- Project: `staffmanager-9b0f2`
- Auth URL: https://console.firebase.google.com/project/staffmanager-9b0f2/authentication
- Firestore URL: https://console.firebase.google.com/project/staffmanager-9b0f2/firestore

**🎯 Ready to test registration! Start with Test Case 1 (Admin User) and monitor console output for success/failure indicators.**
