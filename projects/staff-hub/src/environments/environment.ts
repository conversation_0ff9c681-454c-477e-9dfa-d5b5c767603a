export const environment = {
  production: false,
  appName: 'StaffHub',
  version: '1.0.0',
  
  // Firebase configuration (shared with ManagerHub)
  firebase: {
    apiKey: "AIzaSyAJVN9dhfaRhnD686tytSg48IGI_wVtzQ8",
    authDomain: "staffmanager-9b0f2.firebaseapp.com",
    projectId: "staffmanager-9b0f2",
    storageBucket: "staffmanager-9b0f2.firebasestorage.app",
    messagingSenderId: "666727178550",
    appId: "1:666727178550:web:187e1798f2d7976b72d397",
    measurementId: "G-MLKN6XMC3H"
  },
  
  // AI configuration
  googleAI: {
    apiKey: "AIzaSyB10eDv92ao-_yFl_V7clBV2WpJeFp-5LE"
  },
  
  // API endpoints
  api: {
    baseUrl: 'https://api.staffmanager.com',
    timeout: 30000
  },
  
  // Feature flags
  features: {
    aiAssistant: true,
    pushNotifications: true,
    offlineMode: true,
    geolocation: true,
    biometricAuth: false,
    darkMode: true,
    multiLanguage: false
  },
  
  // PWA configuration
  pwa: {
    updateInterval: 60000, // Check for updates every minute
    cacheStrategy: 'networkFirst',
    offlinePages: ['/dashboard', '/tasks', '/schedule', '/profile']
  },
  
  // Sync configuration
  sync: {
    retryAttempts: 3,
    retryDelay: 1000,
    batchSize: 50,
    syncInterval: 30000 // Sync every 30 seconds
  },
  
  // Notification configuration
  notifications: {
    vapidPublicKey: 'YOUR_VAPID_PUBLIC_KEY',
    defaultIcon: '/assets/icons/icon-192x192.png',
    defaultBadge: '/assets/icons/badge-72x72.png'
  }
};
