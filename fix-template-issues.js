#!/usr/bin/env node

/**
 * Quick fix for template migration issues
 */

const fs = require('fs');
const path = require('path');

function fixTemplateIssues(filePath) {
  console.log(`Fixing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix mixed template syntax issues
  const fixes = [
    // Fix card template structure
    {
      from: /<mat-card-header>/g,
      to: '<ng-template pTemplate="header">'
    },
    {
      from: /<\/mat-card-header>/g,
      to: '</ng-template>'
    },
    {
      from: /<mat-card-title>/g,
      to: '<ng-template pTemplate="title">'
    },
    {
      from: /<\/mat-card-title>/g,
      to: '</ng-template>'
    },
    {
      from: /<mat-card-subtitle>/g,
      to: '<ng-template pTemplate="subtitle">'
    },
    {
      from: /<\/mat-card-subtitle>/g,
      to: '</ng-template>'
    },
    {
      from: /<mat-card-content>/g,
      to: '<ng-template pTemplate="content">'
    },
    {
      from: /<\/mat-card-content>/g,
      to: '</ng-template>'
    },
    {
      from: /<mat-card-actions>/g,
      to: '<ng-template pTemplate="footer">'
    },
    {
      from: /<\/mat-card-actions>/g,
      to: '</ng-template>'
    },
    // Fix chip syntax
    {
      from: /<mat-chip-set>/g,
      to: '<div class="chip-container">'
    },
    {
      from: /<\/mat-chip-set>/g,
      to: '</div>'
    },
    {
      from: /<mat-chip/g,
      to: '<p-chip'
    },
    {
      from: /<\/mat-chip>/g,
      to: '</p-chip>'
    },
    // Fix button syntax
    {
      from: /mat-raised-button/g,
      to: 'p-button'
    },
    {
      from: /mat-stroked-button/g,
      to: 'p-button [outlined]="true"'
    },
    {
      from: /mat-flat-button/g,
      to: 'p-button'
    },
    {
      from: /mat-icon-button/g,
      to: 'p-button [text]="true"'
    },
    // Fix Material Icons to PrimeIcons
    {
      from: /<mat-icon>schedule<\/mat-icon>/g,
      to: '<i class="pi pi-clock"></i>'
    },
    {
      from: /<mat-icon>close<\/mat-icon>/g,
      to: '<i class="pi pi-times"></i>'
    },
    {
      from: /<mat-icon>search<\/mat-icon>/g,
      to: '<i class="pi pi-search"></i>'
    },
    {
      from: /<mat-icon>visibility<\/mat-icon>/g,
      to: '<i class="pi pi-eye"></i>'
    },
    {
      from: /<mat-icon>business<\/mat-icon>/g,
      to: '<i class="pi pi-building"></i>'
    },
    // Fix checkbox
    {
      from: /<mat-checkbox/g,
      to: '<p-checkbox'
    },
    {
      from: /<\/mat-checkbox>/g,
      to: '</p-checkbox>'
    },
    {
      from: /\[checked\]=/g,
      to: '[binary]="true" [ngModel]='
    }
  ];

  fixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix import syntax errors
  const importFixes = [
    {
      from: /CheckboxModule ButtonModule/g,
      to: 'CheckboxModule, ButtonModule'
    },
    {
      from: /TableModule\s+DropdownModule/g,
      to: 'TableModule, DropdownModule'
    },
    {
      from: /TableModule\s+InputTextModule/g,
      to: 'TableModule, InputTextModule'
    },
    {
      from: /ToolbarModule,\s+ToolbarModule/g,
      to: 'ToolbarModule'
    },
    {
      from: /ButtonModule,\s+ButtonModule/g,
      to: 'ButtonModule'
    }
  ];

  importFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

function findFiles(dir, extensions = ['.ts', '.html']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
        traverse(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
function main() {
  console.log('🔧 Fixing template migration issues...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const files = findFiles(srcDir);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixTemplateIssues(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n✨ Template fixes complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`✅ Files fixed: ${fixedCount}`);
}

if (require.main === module) {
  main();
}

module.exports = { fixTemplateIssues };
