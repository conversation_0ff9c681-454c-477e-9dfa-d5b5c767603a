.time-management-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .time-header {
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 10;

    mat-toolbar {
      display: flex;
      align-items: center;
      gap: 12px;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      span:not(.spacer) {
        font-size: 1.25rem;
        font-weight: 500;
      }

      .spacer {
        flex: 1;
      }
    }
  }

  .time-content {
    flex: 1;
    overflow: hidden;
    background: #f5f5f5;

    .time-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      ::ng-deep .mat-mdc-tab-group {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      ::ng-deep .mat-mdc-tab-header {
        flex-shrink: 0;
        background: white;
        border-bottom: 1px solid #e0e0e0;
      }

      ::ng-deep .mat-mdc-tab-body-wrapper {
        flex: 1;
        overflow: hidden;
      }

      ::ng-deep .mat-mdc-tab-body {
        height: 100%;
        overflow: auto;
      }

      .tab-content {
        padding: 24px;
        height: 100%;
        overflow: auto;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .time-management-container {
    .time-header {
      mat-toolbar {
        padding: 0 16px;
        
        span:not(.spacer) {
          font-size: 1.125rem;
        }
      }
    }

    .time-content {
      .tab-content {
        padding: 16px;
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .time-management-container {
    .time-content {
      background: #303030;

      .time-tabs {
        ::ng-deep .mat-mdc-tab-header {
          background: #424242;
          border-bottom-color: #616161;
        }
      }
    }
  }
}
