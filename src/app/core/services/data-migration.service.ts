import { Injectable, inject } from '@angular/core';
import { Observable, from, forkJoin } from 'rxjs';
import { StaffFirestoreService } from '../../features/staff/services/staff-firestore.service';
import { CalendarService } from '../../features/calendar/services/calendar.service';
import { AuthService } from '../auth/auth.service';
import { 
  StaffMember, 
  StaffGoalExtended, 
  StaffTask, 
  TimeOffRequest 
} from '../../features/staff/models/staff.model';
import { CalendarEvent, Shift } from '../../features/calendar/models/calendar.model';

@Injectable({
  providedIn: 'root'
})
export class DataMigrationService {
  private staffService = inject(StaffFirestoreService);
  private calendarService = inject(CalendarService);
  private authService = inject(AuthService);

  // Sample data for demonstration
  private sampleStaff: Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>[] = [
    {
      employeeId: 'EMP001',
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      hireDate: new Date('2023-01-15'),
      position: 'Senior Developer',
      department: 'Engineering',
      employmentType: 'full-time',
      status: 'active',
      hourlyRate: 45,
      skills: [
        { id: 'skill_1', name: 'JavaScript', category: 'Programming', level: 'expert', verified: true },
        { id: 'skill_2', name: 'Angular', category: 'Frontend', level: 'advanced', verified: true },
        { id: 'skill_3', name: 'Firebase', category: 'Backend', level: 'intermediate', verified: false }
      ],
      certifications: [],
      education: [],
      workExperience: [],
      availability: {
        monday: { available: true, startTime: '09:00', endTime: '17:00' },
        tuesday: { available: true, startTime: '09:00', endTime: '17:00' },
        wednesday: { available: true, startTime: '09:00', endTime: '17:00' },
        thursday: { available: true, startTime: '09:00', endTime: '17:00' },
        friday: { available: true, startTime: '09:00', endTime: '17:00' },
        saturday: { available: false },
        sunday: { available: false }
      },
      timeZone: 'America/New_York',
      createdBy: 'admin',
      businessIds: ['business_1'],
      primaryBusinessId: 'business_1',
      roles: ['developer'],
      permissions: ['read', 'write'],
      accessLevel: 'advanced'
    },
    {
      employeeId: 'EMP002',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '******-0124',
      hireDate: new Date('2023-02-01'),
      position: 'Project Manager',
      department: 'Management',
      employmentType: 'full-time',
      status: 'active',
      hourlyRate: 55,
      skills: [
        { id: 'skill_4', name: 'Project Management', category: 'Management', level: 'expert', verified: true },
        { id: 'skill_5', name: 'Agile', category: 'Methodology', level: 'advanced', verified: true }
      ],
      certifications: [],
      education: [],
      workExperience: [],
      availability: {
        monday: { available: true, startTime: '08:00', endTime: '16:00' },
        tuesday: { available: true, startTime: '08:00', endTime: '16:00' },
        wednesday: { available: true, startTime: '08:00', endTime: '16:00' },
        thursday: { available: true, startTime: '08:00', endTime: '16:00' },
        friday: { available: true, startTime: '08:00', endTime: '16:00' },
        saturday: { available: false },
        sunday: { available: false }
      },
      timeZone: 'America/New_York',
      createdBy: 'admin',
      businessIds: ['business_1'],
      primaryBusinessId: 'business_1',
      roles: ['manager'],
      permissions: ['read', 'write', 'manage'],
      accessLevel: 'admin'
    },
    {
      employeeId: 'EMP003',
      firstName: 'Mike',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '******-0125',
      hireDate: new Date('2023-03-10'),
      position: 'UI/UX Designer',
      department: 'Design',
      employmentType: 'full-time',
      status: 'active',
      hourlyRate: 40,
      skills: [
        { id: 'skill_6', name: 'Figma', category: 'Design', level: 'expert', verified: true },
        { id: 'skill_7', name: 'Adobe Creative Suite', category: 'Design', level: 'advanced', verified: true }
      ],
      certifications: [],
      education: [],
      workExperience: [],
      availability: {
        monday: { available: true, startTime: '10:00', endTime: '18:00' },
        tuesday: { available: true, startTime: '10:00', endTime: '18:00' },
        wednesday: { available: true, startTime: '10:00', endTime: '18:00' },
        thursday: { available: true, startTime: '10:00', endTime: '18:00' },
        friday: { available: true, startTime: '10:00', endTime: '18:00' },
        saturday: { available: false },
        sunday: { available: false }
      },
      timeZone: 'America/New_York',
      createdBy: 'admin',
      businessIds: ['business_1'],
      primaryBusinessId: 'business_1',
      roles: ['designer'],
      permissions: ['read', 'write'],
      accessLevel: 'basic'
    }
  ];

  // Migrate all sample data
  migrateSampleData(): Observable<any> {
    console.log('Starting data migration...');
    
    return forkJoin({
      staff: this.migrateStaff(),
      // Add other migrations as needed
    });
  }

  // Migrate staff data
  private migrateStaff(): Observable<StaffMember[]> {
    console.log('Migrating staff data...');
    
    const staffPromises = this.sampleStaff.map(staff => 
      this.staffService.createStaff(staff)
    );
    
    return forkJoin(staffPromises);
  }

  // Create sample goals for staff
  createSampleGoals(staffMembers: StaffMember[]): Observable<StaffGoalExtended[]> {
    const sampleGoals: Omit<StaffGoalExtended, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        title: 'Complete Angular Certification',
        description: 'Obtain Angular certification to improve frontend development skills',
        type: 'individual',
        category: 'training',
        targetValue: 1,
        currentValue: 0,
        unit: 'certification',
        assignedBy: 'admin',
        assignedTo: [staffMembers[0]?.id || ''],
        priority: 'medium',
        targetDate: new Date('2024-06-30'),
        status: 'in-progress',
        progress: 25,
        tags: ['certification', 'angular', 'frontend'],
        comments: [],
        milestones: [
          {
            id: 'milestone_1',
            title: 'Complete Course Material',
            targetDate: new Date('2024-05-15'),
            completed: false
          },
          {
            id: 'milestone_2',
            title: 'Pass Practice Exams',
            targetDate: new Date('2024-06-15'),
            completed: false
          }
        ]
      },
      {
        title: 'Improve Team Productivity',
        description: 'Increase team productivity by 20% through better project management',
        type: 'team',
        category: 'performance',
        targetValue: 20,
        currentValue: 5,
        unit: 'percentage',
        assignedBy: 'admin',
        assignedTo: [staffMembers[1]?.id || ''],
        priority: 'high',
        targetDate: new Date('2024-12-31'),
        status: 'in-progress',
        progress: 15,
        tags: ['productivity', 'management', 'team'],
        comments: [],
        milestones: []
      }
    ];

    const goalPromises = sampleGoals.map(goal => 
      this.staffService.createGoal(goal)
    );
    
    return forkJoin(goalPromises);
  }

  // Create sample tasks
  createSampleTasks(staffMembers: StaffMember[]): Observable<StaffTask[]> {
    const sampleTasks: Omit<StaffTask, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        title: 'Implement User Authentication',
        description: 'Set up Firebase authentication for the StaffManager application',
        status: 'in-progress',
        priority: 'high',
        category: 'project',
        assignedTo: [staffMembers[0]?.id || ''],
        assignedBy: 'admin',
        createdBy: 'admin',
        dueDate: new Date('2024-02-15'),
        estimatedHours: 16,
        actualHours: 8,
        tags: ['firebase', 'authentication', 'security'],
        businessId: 'business_1',
        comments: [],
        subtasks: [
          {
            id: 'subtask_1',
            title: 'Set up Firebase project',
            completed: true,
            completedAt: new Date(),
            completedBy: staffMembers[0]?.id
          },
          {
            id: 'subtask_2',
            title: 'Implement login component',
            completed: false
          }
        ]
      },
      {
        title: 'Design Staff Profile Interface',
        description: 'Create wireframes and mockups for the staff profile pages',
        status: 'pending',
        priority: 'medium',
        category: 'project',
        assignedTo: [staffMembers[2]?.id || ''],
        assignedBy: 'admin',
        createdBy: 'admin',
        dueDate: new Date('2024-02-20'),
        estimatedHours: 12,
        tags: ['design', 'ui', 'wireframes'],
        businessId: 'business_1',
        comments: [],
        subtasks: []
      }
    ];

    const taskPromises = sampleTasks.map(task => 
      this.staffService.createTask(task)
    );
    
    return forkJoin(taskPromises);
  }

  // Create sample calendar events
  createSampleCalendarEvents(staffMembers: StaffMember[]): Observable<CalendarEvent[]> {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);

    const sampleEvents: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        title: 'Morning Development Shift',
        description: 'Regular development work shift',
        start: new Date(today.setHours(9, 0, 0, 0)),
        end: new Date(today.setHours(17, 0, 0, 0)),
        type: 'shift',
        status: 'scheduled',
        assignedStaff: [staffMembers[0]?.id || ''],
        createdBy: 'admin',
        businessId: 'business_1',
        location: 'Office - Floor 2'
      },
      {
        title: 'Team Standup Meeting',
        description: 'Daily team standup and planning session',
        start: new Date(tomorrow.setHours(10, 0, 0, 0)),
        end: new Date(tomorrow.setHours(10, 30, 0, 0)),
        type: 'meeting',
        status: 'scheduled',
        assignedStaff: staffMembers.map(s => s.id || '').filter(Boolean),
        createdBy: 'admin',
        businessId: 'business_1',
        location: 'Conference Room A'
      },
      {
        title: 'Angular Training Workshop',
        description: 'Advanced Angular concepts and best practices',
        start: new Date(nextWeek.setHours(14, 0, 0, 0)),
        end: new Date(nextWeek.setHours(17, 0, 0, 0)),
        type: 'training',
        status: 'scheduled',
        assignedStaff: [staffMembers[0]?.id || ''],
        createdBy: 'admin',
        businessId: 'business_1',
        location: 'Training Room'
      }
    ];

    const eventPromises = sampleEvents.map(event => 
      this.calendarService.createEvent(event)
    );
    
    return forkJoin(eventPromises);
  }

  // Create sample time off requests
  createSampleTimeOffRequests(staffMembers: StaffMember[]): Observable<TimeOffRequest[]> {
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    
    const endDate = new Date(nextMonth);
    endDate.setDate(nextMonth.getDate() + 2);

    const sampleRequests: Omit<TimeOffRequest, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        staffId: staffMembers[0]?.id || '',
        type: 'vacation',
        startDate: nextMonth,
        endDate: endDate,
        totalDays: 3,
        totalHours: 24,
        reason: 'Family vacation',
        status: 'pending',
        requestedAt: new Date(),
        businessId: 'business_1'
      }
    ];

    const requestPromises = sampleRequests.map(request => 
      this.staffService.createTimeOffRequest(request)
    );
    
    return forkJoin(requestPromises);
  }

  // Full migration with all sample data
  migrateAllSampleData(): Observable<any> {
    return new Observable(observer => {
      this.migrateStaff().subscribe({
        next: (staffMembers) => {
          console.log('Staff migrated:', staffMembers.length);
          
          // Create related data
          forkJoin({
            goals: this.createSampleGoals(staffMembers),
            tasks: this.createSampleTasks(staffMembers),
            events: this.createSampleCalendarEvents(staffMembers),
            timeOffRequests: this.createSampleTimeOffRequests(staffMembers)
          }).subscribe({
            next: (results) => {
              console.log('Migration completed:', results);
              observer.next(results);
              observer.complete();
            },
            error: (error) => {
              console.error('Error creating related data:', error);
              observer.error(error);
            }
          });
        },
        error: (error) => {
          console.error('Error migrating staff:', error);
          observer.error(error);
        }
      });
    });
  }

  // Clear all data (use with caution!)
  clearAllData(): Observable<any> {
    console.warn('Clearing all data - this cannot be undone!');
    // Implementation would depend on your specific needs
    // This is a placeholder for a more comprehensive cleanup
    return from(Promise.resolve('Data clearing not implemented for safety'));
  }
}
