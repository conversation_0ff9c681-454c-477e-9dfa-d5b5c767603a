import { Injectable, inject, NgZone } from '@angular/core';
import { 
  Firestore, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  collection, 
  getDocs, 
  addDoc, 
  query, 
  onSnapshot, 
  QueryConstraint,
  DocumentReference,
  CollectionReference,
  DocumentSnapshot,
  QuerySnapshot
} from '@angular/fire/firestore';
import { Observable, from } from 'rxjs';

/**
 * Firebase Context Service
 * 
 * This service ensures all Firebase operations are executed within proper Angular injection context
 * to prevent the "Firebase API called outside injection context" warnings.
 */
@Injectable({
  providedIn: 'root'
})
export class FirebaseContextService {
  private firestore = inject(Firestore);
  private ngZone = inject(NgZone);

  /**
   * Execute a Firebase operation within proper injection context
   */
  private executeInContext<T>(operation: () => T): T {
    return this.ngZone.run(() => operation());
  }

  /**
   * Execute a Firebase operation outside Angular zone for performance
   */
  private executeOutsideAngular<T>(operation: () => T): T {
    return this.ngZone.runOutsideAngular(() => operation());
  }

  /**
   * Get a document with proper context
   */
  getDocument<T = any>(path: string): Observable<DocumentSnapshot<T>> {
    return this.executeInContext(() => {
      const docRef = doc(this.firestore, path) as DocumentReference<T>;
      return from(getDoc(docRef));
    });
  }

  /**
   * Set a document with proper context
   */
  setDocument<T = any>(path: string, data: T, options?: any): Observable<void> {
    return this.executeInContext(() => {
      const docRef = doc(this.firestore, path) as DocumentReference<T>;
      return from(setDoc(docRef, data, options));
    });
  }

  /**
   * Update a document with proper context
   */
  updateDocument(path: string, data: any): Observable<void> {
    return this.executeInContext(() => {
      const docRef = doc(this.firestore, path);
      return from(updateDoc(docRef, data));
    });
  }

  /**
   * Delete a document with proper context
   */
  deleteDocument(path: string): Observable<void> {
    return this.executeInContext(() => {
      const docRef = doc(this.firestore, path);
      return from(deleteDoc(docRef));
    });
  }

  /**
   * Add a document to a collection with proper context
   */
  addDocument<T = any>(collectionPath: string, data: T): Observable<DocumentReference<T>> {
    return this.executeInContext(() => {
      const collectionRef = collection(this.firestore, collectionPath) as CollectionReference<T>;
      return from(addDoc(collectionRef, data));
    });
  }

  /**
   * Get documents from a collection with proper context
   */
  getCollection<T = any>(collectionPath: string, ...constraints: QueryConstraint[]): Observable<QuerySnapshot<T>> {
    return this.executeInContext(() => {
      const collectionRef = collection(this.firestore, collectionPath) as CollectionReference<T>;
      const q = query(collectionRef, ...constraints);
      return from(getDocs(q));
    });
  }

  /**
   * Subscribe to document changes with proper context
   */
  subscribeToDocument<T = any>(path: string): Observable<DocumentSnapshot<T>> {
    return new Observable(observer => {
      const docRef = doc(this.firestore, path) as DocumentReference<T>;
      
      const unsubscribe = this.executeOutsideAngular(() => {
        return onSnapshot(docRef, 
          (snapshot) => {
            this.ngZone.run(() => {
              observer.next(snapshot);
            });
          },
          (error) => {
            this.ngZone.run(() => {
              observer.error(error);
            });
          }
        );
      });

      return () => unsubscribe();
    });
  }

  /**
   * Subscribe to collection changes with proper context
   */
  subscribeToCollection<T = any>(collectionPath: string, ...constraints: QueryConstraint[]): Observable<QuerySnapshot<T>> {
    return new Observable(observer => {
      const collectionRef = collection(this.firestore, collectionPath) as CollectionReference<T>;
      const q = query(collectionRef, ...constraints);
      
      const unsubscribe = this.executeOutsideAngular(() => {
        return onSnapshot(q,
          (snapshot) => {
            this.ngZone.run(() => {
              observer.next(snapshot);
            });
          },
          (error) => {
            this.ngZone.run(() => {
              observer.error(error);
            });
          }
        );
      });

      return () => unsubscribe();
    });
  }

  /**
   * Get Firestore instance (for direct access when needed)
   */
  getFirestore(): Firestore {
    return this.firestore;
  }

  /**
   * Execute any Firebase operation with proper context
   */
  executeFirebaseOperation<T>(operation: () => Promise<T>): Observable<T> {
    return this.executeInContext(() => from(operation()));
  }
}
