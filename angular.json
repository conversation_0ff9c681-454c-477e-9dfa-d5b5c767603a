{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"staffmanager-web-new": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/staffmanager-web-new", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": [], "server": "src/main.server.ts", "outputMode": "server", "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"port": 0}, "configurations": {"production": {"buildTarget": "staffmanager-web-new:build:production"}, "development": {"buildTarget": "staffmanager-web-new:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": []}}}}, "staff-hub": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "projects/staff-hub", "sourceRoot": "projects/staff-hub/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/staff-hub", "index": "projects/staff-hub/src/index.html", "browser": "projects/staff-hub/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/staff-hub/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/staff-hub/public"}], "styles": ["projects/staff-hub/src/styles.scss"], "scripts": [], "server": "projects/staff-hub/src/main.server.ts", "outputMode": "server", "ssr": {"entry": "projects/staff-hub/src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "staff-hub:build:production"}, "development": {"buildTarget": "staff-hub:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/staff-hub/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/staff-hub/public"}], "styles": ["projects/staff-hub/src/styles.scss"], "scripts": []}}}}, "time-hub": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "projects/time-hub", "sourceRoot": "projects/time-hub/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/time-hub", "index": "projects/time-hub/src/index.html", "browser": "projects/time-hub/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/time-hub/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/time-hub/public"}], "styles": ["projects/time-hub/src/styles.scss"], "scripts": [], "server": "projects/time-hub/src/main.server.ts", "outputMode": "server", "ssr": {"entry": "projects/time-hub/src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "time-hub:build:production"}, "development": {"buildTarget": "time-hub:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/time-hub/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/time-hub/public"}], "styles": ["projects/time-hub/src/styles.scss"], "scripts": []}}}}, "shared-hub": {"projectType": "library", "root": "projects/shared-hub", "sourceRoot": "projects/shared-hub/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/shared-hub/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/shared-hub/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/shared-hub/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/shared-hub/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}}, "cli": {"analytics": "38b9b409-bf45-4d7d-8d4d-350252059df9"}}