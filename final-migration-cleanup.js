#!/usr/bin/env node

/**
 * Final comprehensive cleanup for PrimeNG migration
 */

const fs = require('fs');
const path = require('path');

function finalCleanup(filePath) {
  console.log(`Final cleanup: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix critical import syntax errors
  const importFixes = [
    // Fix missing commas in imports arrays
    {
      from: /ButtonModule\s+TabViewModule/g,
      to: 'ButtonModule, TabViewModule'
    },
    {
      from: /TabViewModule\s+DropdownModule/g,
      to: 'TabViewModule, DropdownModule'
    },
    {
      from: /TableModule\s+DropdownModule/g,
      to: 'TableModule, DropdownModule'
    },
    {
      from: /TableModule\s+InputTextModule/g,
      to: 'TableModule, InputTextModule'
    },
    {
      from: /ToolbarModule,\s+ToolbarModule/g,
      to: 'ToolbarModule'
    },
    {
      from: /ButtonModule,\s+ButtonModule/g,
      to: 'ButtonModule'
    }
  ];

  importFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Remove all remaining Material imports and references
  const materialCleanup = [
    {
      from: /MatListModule,?\s*/g,
      to: ''
    },
    {
      from: /MatIconModule,?\s*/g,
      to: ''
    },
    {
      from: /MatButtonModule,?\s*/g,
      to: ''
    },
    {
      from: /MatDividerModule,?\s*/g,
      to: ''
    },
    {
      from: /MatSelectModule,?\s*/g,
      to: ''
    },
    {
      from: /MatOptionModule,?\s*/g,
      to: ''
    },
    {
      from: /MatBadgeModule,?\s*/g,
      to: ''
    },
    {
      from: /MatTooltipModule,?\s*/g,
      to: ''
    },
    {
      from: /MatNativeDateModule,?\s*/g,
      to: ''
    }
  ];

  materialCleanup.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix all Material Icons to PrimeIcons
  const iconFixes = [
    {
      from: /<mat-icon>calendar_today<\/mat-icon>/g,
      to: '<i class="pi pi-calendar"></i>'
    },
    {
      from: /<mat-icon>psychology<\/mat-icon>/g,
      to: '<i class="pi pi-brain"></i>'
    },
    {
      from: /<mat-icon>edit_calendar<\/mat-icon>/g,
      to: '<i class="pi pi-calendar-plus"></i>'
    },
    {
      from: /<mat-icon>auto_awesome<\/mat-icon>/g,
      to: '<i class="pi pi-sparkles"></i>'
    },
    {
      from: /<mat-icon>preview<\/mat-icon>/g,
      to: '<i class="pi pi-eye"></i>'
    },
    {
      from: /<mat-icon>check<\/mat-icon>/g,
      to: '<i class="pi pi-check"></i>'
    },
    {
      from: /<mat-icon>library_books<\/mat-icon>/g,
      to: '<i class="pi pi-book"></i>'
    },
    {
      from: /<mat-icon>business<\/mat-icon>/g,
      to: '<i class="pi pi-building"></i>'
    },
    {
      from: /<mat-icon[^>]*>[^<]*<\/mat-icon>/g,
      to: '<i class="pi pi-circle"></i>'
    }
  ];

  iconFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix Material components to PrimeNG equivalents
  const componentFixes = [
    {
      from: /<mat-spinner[^>]*><\/mat-spinner>/g,
      to: '<p-progressSpinner></p-progressSpinner>'
    },
    {
      from: /<mat-datepicker[^>]*><\/mat-datepicker>/g,
      to: ''
    },
    {
      from: /<mat-datepicker-toggle[^>]*><\/mat-datepicker-toggle>/g,
      to: ''
    },
    {
      from: /matInput/g,
      to: 'pInputText'
    },
    {
      from: /\[matDatepicker\]="[^"]*"/g,
      to: ''
    },
    {
      from: /mat-button/g,
      to: 'p-button'
    }
  ];

  componentFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix PrimeNG Card template structure
  const cardFixes = [
    // Replace invalid card elements with proper PrimeNG structure
    {
      from: /<p-card-header>/g,
      to: '<ng-template pTemplate="header">'
    },
    {
      from: /<\/p-card-header>/g,
      to: '</ng-template>'
    },
    {
      from: /<p-card-title>/g,
      to: '<h3>'
    },
    {
      from: /<\/p-card-title>/g,
      to: '</h3>'
    },
    {
      from: /<p-card-subtitle>/g,
      to: '<p>'
    },
    {
      from: /<\/p-card-subtitle>/g,
      to: '</p>'
    },
    {
      from: /<p-card-content>/g,
      to: '<ng-template pTemplate="content">'
    },
    {
      from: /<\/p-card-content>/g,
      to: '</ng-template>'
    },
    {
      from: /<p-card-actions>/g,
      to: '<ng-template pTemplate="footer">'
    },
    {
      from: /<\/p-card-actions>/g,
      to: '</ng-template>'
    }
  ];

  cardFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Fix PrimeNG dropdown options
  const dropdownFixes = [
    {
      from: /<p-option/g,
      to: '<option'
    },
    {
      from: /<\/p-option>/g,
      to: '</option>'
    }
  ];

  dropdownFixes.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  // Clean up broken template syntax
  const templateCleanup = [
    // Remove broken ng-template closings
    {
      from: /<\/ng-template>\s*<\/ng-template>\s*<\/ng-template>/g,
      to: '</ng-template>'
    },
    {
      from: /<\/ng-template>\s*<\/p-card>/g,
      to: '</ng-template></p-card>'
    },
    // Fix broken template syntax
    {
      from: />\s*<\/ng-template>/g,
      to: '</ng-template>'
    },
    // Remove duplicate commas
    {
      from: /,\s*,/g,
      to: ','
    },
    // Fix array syntax
    {
      from: /\[\s*,/g,
      to: '['
    },
    {
      from: /,\s*\]/g,
      to: ']'
    }
  ];

  templateCleanup.forEach(fix => {
    if (content.match(fix.from)) {
      content = content.replace(fix.from, fix.to);
      modified = true;
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Final cleanup: ${filePath}`);
    return true;
  }
  
  return false;
}

function findFiles(dir, extensions = ['.ts', '.html']) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
        traverse(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
function main() {
  console.log('🔧 Final comprehensive cleanup for PrimeNG migration...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const files = findFiles(srcDir);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (finalCleanup(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n✨ Final cleanup complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`✅ Files fixed: ${fixedCount}`);
}

if (require.main === module) {
  main();
}

module.exports = { finalCleanup };
