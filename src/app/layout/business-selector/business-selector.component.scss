@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

.business-selector {
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);

  &:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  &:focus {
    outline: 2px solid #5e72e4;
    outline-offset: 2px;
  }

  &.collapsed {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 !important;
    margin: 0 auto !important;
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
    border-radius: 8px;
    width: 56px !important;
    max-width: 56px !important;
    min-width: 56px !important;
    height: 56px !important;
    box-sizing: border-box !important;

    &:hover {
      background: rgba(94, 114, 228, 0.1) !important;
      box-shadow: none !important;
      transform: none !important;
    }
  }

  .business-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    gap: 12px;
  }

  .logo-group {
    display: flex;
    align-items: center;
  }

  .business-logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .business-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .business-icon-container {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #5e72e4 0%, #42a5f5 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(94, 114, 228, 0.3);
  }

  .business-icon {
    color: white;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  .business-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .business-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
    line-height: 1.2;
    letter-spacing: 0.01em;
  }

  .business-subtitle {
    font-size: 0.8rem;
    color: #8898aa;
    font-weight: 400;
  }

  .oneview-chip {
    align-self: flex-start;
    font-size: 0.7rem;
    height: 20px;
    font-weight: 600;

    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }

  .business-selector-collapsed {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 56px !important;
    height: 56px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;

    .collapsed-business-btn {
      width: 48px !important;
      height: 48px !important;
      background: transparent !important;
      border: none !important;
      border-radius: 8px !important;
      min-width: 48px !important;
      max-width: 48px !important;
      padding: 0 !important;
      margin: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      box-sizing: border-box !important;

      &:hover {
        background: rgba(94, 114, 228, 0.1) !important;
      }

      mat-icon {
        font-size: 24px !important;
        width: 24px !important;
        height: 24px !important;
        color: #5e72e4 !important;
      }
    }
  }
}
