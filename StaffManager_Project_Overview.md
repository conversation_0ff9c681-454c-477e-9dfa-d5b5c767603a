
# StaffManager Project Overview

## Introduction
StaffManager is a comprehensive staff management platform designed for modern organizations. It provides an integrated suite of tools to manage staff profiles, time management, task and checklist workflows, calendar scheduling, payroll preparation, goal tracking, and more. The system is built with Angular v19 and Angular Material, using Firebase as the backend for data and authentication, and features robust PWA support for use across devices.

## Core Features
- **Staff Profiles:** Advanced user profiles storing contact info, roles, permissions, time-off history, goal progress, payroll data, and more.
- **Authentication:** Secure login, registration, password recovery, and multi-role access via Firebase Auth and ngx-auth-firebaseui.
- **Calendar & Scheduling:** Integrated with FullCalendar for shift scheduling, appointments, meeting planning, leave management, and syncing tasks with the calendar.
- **Task & Checklist Management:** Configurable, assignable tasks and checklists with completion verification, group assignments, manager oversight, and AI-powered assistance via Gemini 2.5 Pro Flash.
- **Time Management:** Track time-off, sick/vacation days, attendance, and staff hours, including PWA modules for staff clock-in/out (TimeHub) and personal access (StaffHub).
- **Payroll Integration:** Collects timesheet and payroll data for upload to ADP and other services.
- **Goal Tracking:** Supports unit, dollar, and custom performance goals with reporting and dashboard widgets.
- **Dashboards & Widgets:** Customizable dashboards with draggable, resizable, and configurable widgets for analytics and daily operations.
- **Settings & Themes:** Per-user appearance settings, business selector, and extensive configuration options.

## Technology Stack
- **Frontend:** Angular v19, Angular Material, FullCalendar, ngx-auth-firebaseui, Angular PWA modules
- **Backend:** Firebase (Firestore, Auth)
- **AI Integration:** Gemini 2.5 Pro Flash for task/checklist assistance
- **Other:** ADP/Payroll integration, robust API/documentation, modular architecture

## PWAs
- **StaffHub:** For staff to view/manage their shifts, tasks, and checklists.
- **TimeHub:** For clock-in/out via secure PIN entry, suitable for shared devices.

## Documentation & Quality
- Expert-level UI/UX and database design principles are applied throughout.
- Codebase is well-documented and updated with each feature addition or change.
- Manager and staff flows are kept clear, intuitive, and modern.

## Future Development
- Enhanced analytics & reporting
- AI-powered scheduling
- Deeper payroll integrations
- Custom business logic modules

---
For further details, consult the API, COMPONENTS, and FEATURES documentation in the project repo.
