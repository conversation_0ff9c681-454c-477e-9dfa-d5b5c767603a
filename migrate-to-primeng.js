#!/usr/bin/env node

/**
 * StaffManager PrimeNG Migration Script
 * Automates the migration from Angular Material to PrimeNG components
 */

const fs = require('fs');
const path = require('path');

// Migration mappings
const IMPORT_MAPPINGS = {
  // Angular Material -> PrimeNG imports
  'MatCardModule': 'CardModule',
  'MatButtonModule': 'ButtonModule', 
  'MatIconModule': '', // Remove - using PrimeIcons
  'MatTableModule': 'TableModule',
  'MatInputModule': 'InputTextModule',
  'MatFormFieldModule': '', // Remove - PrimeNG uses different pattern
  'MatSelectModule': 'DropdownModule',
  'MatCheckboxModule': 'CheckboxModule',
  'MatChipsModule': 'ChipModule',
  'MatBadgeModule': 'BadgeModule',
  'MatMenuModule': 'MenuModule',
  'MatDialogModule': '', // Remove - using DynamicDialogModule
  'MatSnackBarModule': 'ToastModule',
  'MatProgressBarModule': 'ProgressBarModule',
  'MatDatepickerModule': 'CalendarModule',
  'MatToolbarModule': 'ToolbarModule',
  'MatDividerModule': 'DividerModule',
  'MatListModule': 'ListboxModule',
  'MatExpansionModule': 'AccordionModule',
  'MatTabsModule': 'TabViewModule',
  'MatPaginatorModule': 'PaginatorModule',
  'MatSortModule': '', // Remove - PrimeNG Table handles sorting
  'MatTooltipModule': 'TooltipModule',
  'MatProgressSpinnerModule': 'ProgressSpinnerModule',
  'MatSlideToggleModule': 'ToggleButtonModule',
  'MatAutocompleteModule': 'AutoCompleteModule'
};

const TEMPLATE_MAPPINGS = {
  // Template element mappings
  'mat-card': 'p-card',
  'mat-card-header': 'ng-template pTemplate="header"',
  'mat-card-title': 'ng-template pTemplate="title"', 
  'mat-card-subtitle': 'ng-template pTemplate="subtitle"',
  'mat-card-content': 'ng-template pTemplate="content"',
  'mat-card-actions': 'ng-template pTemplate="footer"',
  'mat-button': 'p-button',
  'mat-raised-button': 'p-button',
  'mat-stroked-button': 'p-button [outlined]="true"',
  'mat-flat-button': 'p-button',
  'mat-icon-button': 'p-button [text]="true"',
  'mat-fab': 'p-button [rounded]="true"',
  'mat-mini-fab': 'p-button [rounded]="true" size="small"',
  'mat-chip': 'p-chip',
  'mat-chip-set': 'div',
  'mat-menu': 'p-menu',
  'mat-toolbar': 'p-toolbar',
  'mat-divider': 'p-divider',
  'mat-progress-bar': 'p-progressBar',
  'mat-progress-spinner': 'p-progressSpinner',
  'mat-tab-group': 'p-tabView',
  'mat-tab': 'p-tabPanel',
  'mat-expansion-panel': 'p-accordionTab',
  'mat-expansion-panel-header': 'ng-template pTemplate="header"',
  'mat-form-field': 'div class="p-field"',
  'mat-label': 'label',
  'mat-select': 'p-dropdown',
  'mat-option': 'p-option',
  'mat-checkbox': 'p-checkbox',
  'mat-slide-toggle': 'p-toggleButton'
};

const ICON_MAPPINGS = {
  // Material Icons -> PrimeIcons
  'dashboard': 'pi-th-large',
  'people': 'pi-users',
  'person': 'pi-user',
  'person_add': 'pi-user-plus',
  'calendar_month': 'pi-calendar',
  'assignment_turned_in': 'pi-check-square',
  'settings': 'pi-cog',
  'business': 'pi-building',
  'visibility': 'pi-eye',
  'edit': 'pi-pencil',
  'delete': 'pi-trash',
  'add': 'pi-plus',
  'remove': 'pi-minus',
  'search': 'pi-search',
  'filter_list': 'pi-filter',
  'import_export': 'pi-download',
  'check_circle': 'pi-check-circle',
  'more_vert': 'pi-ellipsis-v',
  'chevron_left': 'pi-chevron-left',
  'chevron_right': 'pi-chevron-right',
  'logout': 'pi-sign-out',
  'swap_horiz': 'pi-refresh',
  'notifications': 'pi-bell',
  'chat': 'pi-comments',
  'menu': 'pi-bars',
  'close': 'pi-times',
  'save': 'pi-save',
  'cancel': 'pi-times',
  'warning': 'pi-exclamation-triangle',
  'error': 'pi-times-circle',
  'info': 'pi-info-circle',
  'success': 'pi-check-circle'
};

function migrateFile(filePath) {
  console.log(`Migrating: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // 1. Update imports
  Object.entries(IMPORT_MAPPINGS).forEach(([matImport, primeImport]) => {
    const matImportRegex = new RegExp(`import\\s*{[^}]*\\b${matImport}\\b[^}]*}\\s*from\\s*['"][^'"]*['"];?`, 'g');
    if (content.match(matImportRegex)) {
      if (primeImport) {
        // Replace with PrimeNG import
        const primeImportStatement = `import { ${primeImport} } from 'primeng/${primeImport.toLowerCase().replace('module', '')}';`;
        content = content.replace(matImportRegex, primeImportStatement);
      } else {
        // Remove the import
        content = content.replace(matImportRegex, '');
      }
      modified = true;
    }
  });

  // 2. Update component imports arrays
  Object.entries(IMPORT_MAPPINGS).forEach(([matImport, primeImport]) => {
    if (primeImport) {
      content = content.replace(new RegExp(`\\b${matImport}\\b`, 'g'), primeImport);
      modified = true;
    } else {
      // Remove from imports array
      content = content.replace(new RegExp(`,?\\s*${matImport}\\s*,?`, 'g'), '');
      modified = true;
    }
  });

  // 3. Update template elements
  Object.entries(TEMPLATE_MAPPINGS).forEach(([matElement, primeElement]) => {
    const regex = new RegExp(`<${matElement}\\b`, 'g');
    if (content.match(regex)) {
      content = content.replace(regex, `<${primeElement}`);
      // Also replace closing tags
      content = content.replace(new RegExp(`</${matElement}>`, 'g'), `</${primeElement.split(' ')[0]}>`);
      modified = true;
    }
  });

  // 4. Update Material Icons to PrimeIcons
  Object.entries(ICON_MAPPINGS).forEach(([matIcon, primeIcon]) => {
    const matIconRegex = new RegExp(`<mat-icon[^>]*>${matIcon}</mat-icon>`, 'g');
    if (content.match(matIconRegex)) {
      content = content.replace(matIconRegex, `<i class="pi ${primeIcon}"></i>`);
      modified = true;
    }
  });

  // 5. Update common attribute patterns
  content = content.replace(/mat-raised-button/g, 'p-button');
  content = content.replace(/mat-stroked-button/g, 'p-button [outlined]="true"');
  content = content.replace(/mat-flat-button/g, 'p-button');
  content = content.replace(/mat-icon-button/g, 'p-button [text]="true"');
  content = content.replace(/matTooltip=/g, 'pTooltip=');
  content = content.replace(/matTooltipPosition=/g, 'tooltipPosition=');

  // 6. Clean up duplicate imports and empty lines
  content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
  content = content.replace(/,\s*,/g, ',');
  content = content.replace(/\[\s*,/g, '[');
  content = content.replace(/,\s*\]/g, ']');

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Migrated: ${filePath}`);
    return true;
  }
  
  return false;
}

function findTSFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
        traverse(fullPath);
      } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
function main() {
  console.log('🚀 Starting StaffManager PrimeNG Migration...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const tsFiles = findTSFiles(srcDir);
  
  let migratedCount = 0;
  
  tsFiles.forEach(file => {
    if (migrateFile(file)) {
      migratedCount++;
    }
  });
  
  console.log(`\n✨ Migration complete!`);
  console.log(`📊 Files processed: ${tsFiles.length}`);
  console.log(`✅ Files migrated: ${migratedCount}`);
  console.log(`\n🔧 Next steps:`);
  console.log(`1. Run 'npm run build' to check for compilation errors`);
  console.log(`2. Update any remaining template files manually`);
  console.log(`3. Test the application thoroughly`);
}

if (require.main === module) {
  main();
}

module.exports = { migrateFile, IMPORT_MAPPINGS, TEMPLATE_MAPPINGS, ICON_MAPPINGS };
