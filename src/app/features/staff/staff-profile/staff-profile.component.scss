.staff-profile-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .profile-header {
    margin-bottom: 24px;

    .profile-header-content {
      display: flex;
      gap: 24px;
      align-items: flex-start;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .avatar-section {
        position: relative;
        flex-shrink: 0;

        .avatar {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;

          mat-icon {
            font-size: 80px;
            width: 80px;
            height: 80px;
            color: #9e9e9e;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        button {
          position: absolute;
          bottom: 0;
          right: 0;
          background: white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
      }

      .profile-info {
        flex: 1;

        h1 {
          margin: 0 0 8px 0;
          font-size: 2rem;
          font-weight: 500;
        }

        h2 {
          margin: 0 0 4px 0;
          font-size: 1.25rem;
          font-weight: 400;
          color: #1976d2;
        }

        .department {
          margin: 0 0 16px 0;
          color: #666;
          font-size: 1rem;
        }

        .status-chips {
          margin-bottom: 16px;

          mat-chip-set {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }

          .status-active { background-color: #4caf50; color: white; }
          .status-inactive { background-color: #f44336; color: white; }
          .status-on-leave { background-color: #ff9800; color: white; }
          .status-terminated { background-color: #9e9e9e; color: white; }
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;

            mat-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }
        }
      }

      .profile-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex-shrink: 0;

        @media (max-width: 768px) {
          flex-direction: row;
          justify-content: center;
        }
      }
    }
  }

  .profile-tabs {
    .tab-content {
      padding: 24px 0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h3 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 500;
      }
    }
  }

  // Overview Tab Styles
  .overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .stats-card {
      .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .stat-item {
          text-align: center;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;

          .stat-value {
            display: block;
            font-size: 2rem;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 0.875rem;
            color: #666;
          }
        }
      }
    }

    .activity-card {
      mat-list {
        max-height: 300px;
        overflow-y: auto;
      }
    }
  }

  // Goals Tab Styles
  .goals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;

    .goal-card {
      .goal-progress {
        margin: 16px 0;

        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 0.875rem;
          color: #666;
        }

        mat-progress-bar {
          height: 8px;
          border-radius: 4px;
        }
      }

      .goal-status {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .status-not-started { background-color: #9e9e9e; color: white; }
        .status-in-progress { background-color: #2196f3; color: white; }
        .status-completed { background-color: #4caf50; color: white; }
        .status-overdue { background-color: #f44336; color: white; }

        .priority-low { background-color: #4caf50; color: white; }
        .priority-medium { background-color: #ff9800; color: white; }
        .priority-high { background-color: #f44336; color: white; }
        .priority-critical { background-color: #9c27b0; color: white; }
      }
    }
  }

  // Tasks Tab Styles
  .tasks-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .task-card {
      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        h4 {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 500;
        }

        .task-meta {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }

      .task-details {
        display: flex;
        gap: 24px;
        margin-top: 12px;

        .task-info {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 0.875rem;
          color: #666;

          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
          }
        }
      }

      .status-pending { background-color: #ff9800; color: white; }
      .status-in-progress { background-color: #2196f3; color: white; }
      .status-completed { background-color: #4caf50; color: white; }
      .status-cancelled { background-color: #9e9e9e; color: white; }
      .status-on-hold { background-color: #795548; color: white; }

      .priority-low { background-color: #4caf50; color: white; }
      .priority-medium { background-color: #ff9800; color: white; }
      .priority-high { background-color: #f44336; color: white; }
      .priority-urgent { background-color: #9c27b0; color: white; }
    }
  }

  // Schedule Tab Styles
  .schedule-card {
    min-height: 400px;
  }

  // Time Management Tab Styles
  .time-management-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .time-off-card,
    .time-entries-card {
      mat-list {
        max-height: 400px;
        overflow-y: auto;
      }

      .status-pending { background-color: #ff9800; color: white; }
      .status-approved { background-color: #4caf50; color: white; }
      .status-denied { background-color: #f44336; color: white; }
      .status-cancelled { background-color: #9e9e9e; color: white; }
    }
  }
}

// Global chip styles
mat-chip {
  font-size: 0.75rem;
  min-height: 24px;
  padding: 0 8px;
  border-radius: 12px;
}

// Tab badge styling
.tab-badge {
  margin-left: 4px;
  font-size: 0.75rem;
  color: #666;
  font-weight: normal;
}

// Responsive adjustments
@media (max-width: 768px) {
  .staff-profile-container {
    padding: 16px;
  }
}
