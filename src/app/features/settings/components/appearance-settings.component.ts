import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Observable } from 'rxjs';

// Angular Material
import { CardModule } from 'primeng/card';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';

import { ToggleButtonModule } from 'primeng/togglebutton';
import { DropdownModule } from 'primeng/dropdown';
import { MatSliderModule } from '@angular/material/slider';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { DividerModule } from 'primeng/divider';

import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';

// Services
import { SettingsService } from '../../../core/services/settings.service';
import { StaffManagerThemeService } from '../../../core/theme/staffmanager-theme';

// Models
import { UserSettings, AppearanceSettings } from '../../../core/models/settings.model';

@Component({
  selector: 'app-appearance-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule,
    ToggleButtonModule,
    DropdownModule,
    MatSliderModule,
    MatButtonToggleModule,
    DividerModule,
    InputTextModule,
    ToastModule
  ],
  template: `
    <div class="appearance-settings">
      <p-card>
        <ng-template pTemplate="header">
          <i class="pi pi-circle"></i>
          <h3>
          <p>Content</p><ng-template pTemplate="content">
          <form [formGroup]="appearanceForm" (ngSubmit)="saveSettings()">

            <!-- Theme Settings -->
            <div class="settings-section">
              <h3>Theme</h3>
              <div class="theme-options">
                <p-button-toggle-group formControlName="theme" aria-label="Theme selection">
                  <p-button-toggle value="light">
                    <i class="pi pi-circle"></i>
                    Light
                  </p-button-toggle>
                  <p-button-toggle value="dark">
                    <i class="pi pi-circle"></i>
                    Dark
                  </p-button-toggle>
                  <p-button-toggle value="system">
                    <i class="pi pi-circle"></i>
                    System
                  </p-button-toggle>
                </p-button-toggle-group>
              </div>
              <p class="setting-description">
                Choose your preferred theme. System will automatically switch based on your device settings.
              </p>
            </div>

            <p-divider></p-divider>

            <!-- Color Scheme -->
            <div class="settings-section">
              <h3>Color Scheme</h3>
              <div class="color-options">
                <div class="color-group">
                  <label>Primary Color</label>
                  <div class="color-picker-row">
                    <input type="color" formControlName="primaryColor" class="color-input">
                    <span class="color-value">{{ appearanceForm.get('primaryColor')?.value }}</span>
                    <p-button text="true" type="button" (click)="resetColor('primary')" pTooltip="Reset to default">
                      <i class="pi pi-refresh"></i>
                    </p-button>
                  </div>

                <div class="color-group">
                  <label>Accent Color</label>
                  <div class="color-picker-row">
                    <input type="color" formControlName="accentColor" class="color-input">
                    <span class="color-value">{{ appearanceForm.get('accentColor')?.value }}</span>
                    <p-button text="true" type="button" (click)="resetColor('accent')" pTooltip="Reset to default">
                      <i class="pi pi-refresh"></i>
                    </p-button>
                  </div>

                <div class="color-group">
                  <label>Warning Color</label>
                  <div class="color-picker-row">
                    <input type="color" formControlName="warnColor" class="color-input">
                    <span class="color-value">{{ appearanceForm.get('warnColor')?.value }}</span>
                    <p-button text="true" type="button" (click)="resetColor('warn')" pTooltip="Reset to default">
                      <i class="pi pi-refresh"></i>
                    </p-button>
                  </div>
              </div>
            </div>

            <p-divider></p-divider>

            <!-- Typography -->
            <div class="settings-section">
              <h3>Typography</h3>
              <div class="typography-options">
                <div class="p-field" appearance="outline">
                  <label>Font Size</label>
                  <p-dropdown formControlName="fontSize" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Font Family</label>
                  <p-dropdown formControlName="fontFamily" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Line Height</label>
                  <p-dropdown formControlName="lineHeight" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>
            </div>

            <p-divider></p-divider>

            <!-- Layout Settings -->
            <div class="settings-section">
              <h3>Layout</h3>
              <div class="layout-options">
                <div class="p-field" appearance="outline">
                  <label>Density</label>
                  <p-dropdown formControlName="density" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Sidebar Behavior</label>
                  <p-dropdown formControlName="sidebarBehavior" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Default Sidebar State</label>
                  <p-dropdown formControlName="sidebarDefaultState" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Dashboard Columns</label>
                  <p-dropdown formControlName="dashboardColumns" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Widget Spacing</label>
                  <p-dropdown formControlName="widgetSpacing" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>
            </div>

            <p-divider></p-divider>

            <!-- Accessibility -->
            <div class="settings-section">
              <h3>Accessibility</h3>
              <div class="accessibility-options">
                <div class="toggle-option">
                  <p-toggleButton formControlName="highContrast">
                    High Contrast Mode
                  </p-toggleButton>
                  <p class="option-description">Increases contrast for better visibility</p>
                </div>

                <div class="toggle-option">
                  <p-toggleButton formControlName="reducedMotion">
                    Reduce Motion
                  </p-toggleButton>
                  <p class="option-description">Minimizes animations and transitions</p>
                </div>

                <div class="toggle-option">
                  <p-toggleButton formControlName="focusIndicators">
                    Enhanced Focus Indicators
                  </p-toggleButton>
                  <p class="option-description">Makes keyboard navigation more visible</p>
                </div>

                <div class="toggle-option">
                  <p-toggleButton formControlName="screenReaderOptimized">
                    Screen Reader Optimization
                  </p-toggleButton>
                  <p class="option-description">Optimizes interface for screen readers</p>
                </div>
            </div>

          </form><ng-template pTemplate="footer">
          <p-button severity="primary" (click)="saveSettings()" [disabled]="saving">
            <i class="pi pi-save"></i>
            Save Changes
          </p-button>
          <p-button (click)="resetToDefaults()">
            <i class="pi pi-refresh"></i>
            Reset to Defaults
          </p-button>
          <p-button (click)="previewChanges()">
            <i class="pi pi-eye"></i>
            Preview
          </p-button></ng-template></p-card>
    </div>
  `,
  styles: [`
    .appearance-settings {
      max-width: 800px;
    }

    .settings-section {
      margin: 24px 0;

      h3 {
        margin-bottom: 16px;
        color: #424242;
        font-weight: 500;
      }

      .setting-description {
        margin-top: 8px;
        color: #666;
        font-size: 0.875rem;
      }
    }

    .theme-options {
      p-button-toggle-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        p-button-toggle {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          padding: 16px;
          min-width: 120px;

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    .color-options {
      display: grid;
      gap: 16px;

      .color-group {
        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #424242;
        }

        .color-picker-row {
          display: flex;
          align-items: center;
          gap: 12px;

          .color-input {
            width: 50px;
            height: 40px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            background: none;

            &::-webkit-color-swatch-wrapper {
              padding: 0;
            }

            &::-webkit-color-swatch {
              border: none;
              border-radius: 6px;
            }
          }

          .color-value {
            font-family: monospace;
            font-size: 0.875rem;
            color: #666;
            min-width: 80px;
          }
        }
      }
    }

    .typography-options,
    .layout-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .accessibility-options {
      .toggle-option {
        margin-bottom: 16px;

        .option-description {
          margin: 4px 0 0 32px;
          color: #666;
          font-size: 0.875rem;
        }
      }
    }

    mat-card-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    // Responsive design
    @media (max-width: 768px) {
      .typography-options,
      .layout-options {
        grid-template-columns: 1fr;
      }

      .theme-options {
        p-button-toggle-group {
          p-button-toggle {
            min-width: 100px;
            padding: 12px;
          }
        }
      }

      mat-card-actions {
        flex-direction: column;

        button {
          width: 100%;
          justify-content: center;
        }
      }
    }
  `]
})
export class AppearanceSettingsComponent implements OnInit {
  private settingsService = inject(SettingsService);
  private themeService = inject(StaffManagerThemeService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);

  userSettings$!: Observable<UserSettings | null>;
  appearanceForm!: FormGroup;
  saving = false;

  ngOnInit(): void {
    this.userSettings$ = this.settingsService.userSettings$;
    this.initializeForm();

    // Load current settings
    this.userSettings$.subscribe(settings => {
      if (settings?.appearance) {
        this.populateForm(settings.appearance);
      }
    });

    // Watch for form changes and apply preview
    this.appearanceForm.valueChanges.subscribe(values => {
      this.applyPreview(values);
    });
  }

  private initializeForm(): void {
    this.appearanceForm = this.fb.group({
      theme: ['system'],
      primaryColor: ['#1976d2'],
      accentColor: ['#ff4081'],
      warnColor: ['#f44336'],
      fontSize: ['medium'],
      fontFamily: ['default'],
      lineHeight: ['normal'],
      density: ['comfortable'],
      sidebarBehavior: ['auto'],
      sidebarDefaultState: ['expanded'],
      dashboardColumns: [3],
      widgetSpacing: ['normal'],
      highContrast: [false],
      reducedMotion: [false],
      focusIndicators: [true],
      screenReaderOptimized: [false]
    });
  }

  private populateForm(appearance: AppearanceSettings): void {
    this.appearanceForm.patchValue({
      theme: appearance.theme,
      primaryColor: appearance.colorScheme.primary,
      accentColor: appearance.colorScheme.accent,
      warnColor: appearance.colorScheme.warn,
      fontSize: appearance.typography.fontSize,
      fontFamily: appearance.typography.fontFamily,
      lineHeight: appearance.typography.lineHeight,
      density: appearance.layout.density,
      sidebarBehavior: appearance.layout.sidebarBehavior,
      sidebarDefaultState: appearance.layout.sidebarDefaultState,
      dashboardColumns: appearance.layout.dashboardColumns,
      widgetSpacing: appearance.layout.widgetSpacing,
      highContrast: appearance.accessibility.highContrast,
      reducedMotion: appearance.accessibility.reducedMotion,
      focusIndicators: appearance.accessibility.focusIndicators,
      screenReaderOptimized: appearance.accessibility.screenReaderOptimized
    });
  }

  private applyPreview(values: any): void {
    // Apply theme changes immediately for preview
    if (values.theme) {
      switch (values.theme) {
        case 'light':
          this.themeService.setTheme(false);
          break;
        case 'dark':
          this.themeService.setTheme(true);
          break;
        case 'system':
          // Use system preference
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          this.themeService.setTheme(prefersDark);
          break;
      }
    }

    // Apply color scheme changes (theme service may not support this yet)
    // if (values.primaryColor || values.accentColor || values.warnColor) {
    //   this.themeService.updateColorScheme({
    //     primary: values.primaryColor,
    //     accent: values.accentColor,
    //     warn: values.warnColor
    //   });
    // }

    // Apply layout changes to document root
    const root = document.documentElement;
    if (values.density) {
      root.setAttribute('data-density', values.density);
    }
    if (values.widgetSpacing) {
      root.setAttribute('data-widget-spacing', values.widgetSpacing);
    }

    // Apply accessibility settings
    root.classList.toggle('high-contrast', values.highContrast);
    root.classList.toggle('reduced-motion', values.reducedMotion);
    root.classList.toggle('enhanced-focus', values.focusIndicators);
  }

  async saveSettings(): Promise<void> {
    if (this.appearanceForm.valid) {
      this.saving = true;
      try {
        const currentSettings = await this.userSettings$.pipe().toPromise();
        if (currentSettings) {
          const formValue = this.appearanceForm.value;

          const appearanceSettings: AppearanceSettings = {
            theme: formValue.theme,
            colorScheme: {
              primary: formValue.primaryColor,
              accent: formValue.accentColor,
              warn: formValue.warnColor
            },
            typography: {
              fontSize: formValue.fontSize,
              fontFamily: formValue.fontFamily,
              lineHeight: formValue.lineHeight
            },
            layout: {
              density: formValue.density,
              sidebarBehavior: formValue.sidebarBehavior,
              sidebarDefaultState: formValue.sidebarDefaultState,
              dashboardColumns: formValue.dashboardColumns,
              widgetSpacing: formValue.widgetSpacing
            },
            accessibility: {
              highContrast: formValue.highContrast,
              reducedMotion: formValue.reducedMotion,
              focusIndicators: formValue.focusIndicators,
              screenReaderOptimized: formValue.screenReaderOptimized
            }
          };

          await this.settingsService.updateSettings(currentSettings.userId, {
            appearance: appearanceSettings
          });

          this.messageService.add({ severity: 'info', summary: 'Appearance settings saved successfully' });
        }
      } catch (error) {
        this.messageService.add({ severity: 'info', summary: 'Error saving appearance settings' });
      } finally {
        this.saving = false;
      }
    }
  }

  resetColor(colorType: 'primary' | 'accent' | 'warn'): void {
    const defaults = {
      primary: '#1976d2',
      accent: '#ff4081',
      warn: '#f44336'
    };

    this.appearanceForm.patchValue({
      [`${colorType}Color`]: defaults[colorType]
    });
  }

  resetToDefaults(): void {
    this.initializeForm();
    this.messageService.add({ severity: 'info', summary: 'Settings reset to defaults' });
  }

  previewChanges(): void {
    this.applyPreview(this.appearanceForm.value);
    this.messageService.add({ severity: 'info', summary: 'Preview applied - save to make permanent' });
  }
}
