import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { ServerModule } from '@angular/platform-server';
import { RootComponent } from './root.component';
import { RouterModule } from '@angular/router';

/**
 * Angular Universal SSR entry for standalone app.
 * This config is referenced in main.server.ts for bootstrapApplication().
 */
@NgModule({
  declarations: [RootComponent],
  imports: [BrowserModule.withServerTransition({ appId: 'serverApp' }), RouterModule.forRoot([])],
  exports: [RouterModule],
  bootstrap: [RootComponent],
})
export class AppModule {}

@NgModule({
  imports: [AppModule, ServerModule, RouterModule],
  bootstrap: [RootComponent],
})
export class AppServerModule {} 