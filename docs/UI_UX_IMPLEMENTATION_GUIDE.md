# StaffManager UI/UX Implementation Guide

## 🚀 **Quick Start**

This guide helps developers implement the new modern UI/UX system across StaffManager components.

## 📦 **Core Files Structure**

```
src/app/core/
├── animations/
│   └── staffmanager-animations.ts    # Central animation library
├── services/
│   └── icon.service.ts               # Modern icon management
└── theme/
    └── staffmanager-theme.ts         # Enhanced theme system

src/styles.scss                       # Global design tokens & utilities
```

## 🎨 **Using Design Tokens**

### **Spacing**
```scss
// Use semantic spacing variables
.my-component {
  padding: var(--sm-spacing-lg);        // 24px
  margin: var(--sm-spacing-md);         // 16px
  gap: var(--sm-spacing-sm);            // 8px
}
```

### **Colors**
```scss
// Use semantic color variables
.my-component {
  background: var(--sm-background-paper);
  color: var(--sm-text-primary);
  border: 1px solid var(--sm-border-light);
}
```

### **Shadows**
```scss
// Use the shadow system
.my-card {
  box-shadow: var(--sm-shadow-md);
  
  &:hover {
    box-shadow: var(--sm-shadow-lg);
  }
}
```

## ✨ **Adding Animations**

### **1. Import Animations**
```typescript
import { fadeInAnimation, cardHoverAnimation, staggerAnimation } from '../core/animations/staffmanager-animations';

@Component({
  selector: 'app-my-component',
  animations: [fadeInAnimation, cardHoverAnimation, staggerAnimation],
  // ...
})
```

### **2. Apply in Template**
```html
<!-- Fade in animation -->
<div [@fadeIn]>Content</div>

<!-- Card hover animation -->
<mat-card [@cardHover]="hoverState"
          (mouseenter)="hoverState = 'hovered'"
          (mouseleave)="hoverState = 'default'">
  Card content
</mat-card>

<!-- Stagger animation for lists -->
<div [@stagger]="items.length">
  <div *ngFor="let item of items">{{ item }}</div>
</div>
```

### **3. Component Properties**
```typescript
export class MyComponent {
  hoverState: 'default' | 'hovered' = 'default';
  items = ['item1', 'item2', 'item3'];
}
```

## 🎯 **Using Modern Icons**

### **1. Inject Icon Service**
```typescript
import { IconService } from '../core/services/icon.service';

@Component({
  // ...
})
export class MyComponent {
  constructor(private iconService: IconService) {}
  
  getIcon(semanticName: string) {
    return this.iconService.getIconName(semanticName);
  }
}
```

### **2. Use in Template**
```html
<!-- Semantic icon usage -->
<mat-icon>{{ getIcon('dashboard') }}</mat-icon>
<mat-icon>{{ getIcon('staff') }}</mat-icon>
<mat-icon>{{ getIcon('settings') }}</mat-icon>
```

## 🎨 **Utility Classes**

### **Spacing**
```html
<!-- Padding -->
<div class="p-xs">4px padding</div>
<div class="p-sm">8px padding</div>
<div class="p-md">16px padding</div>
<div class="p-lg">24px padding</div>

<!-- Margin -->
<div class="m-xs">4px margin</div>
<div class="m-sm">8px margin</div>
<div class="m-md">16px margin</div>
<div class="m-lg">24px margin</div>
```

### **Flexbox**
```html
<div class="flex items-center justify-between">
  <span>Left content</span>
  <span>Right content</span>
</div>

<div class="flex flex-col gap-md">
  <div>Item 1</div>
  <div>Item 2</div>
</div>
```

### **Shadows & Effects**
```html
<div class="shadow-sm">Light shadow</div>
<div class="shadow-md">Medium shadow</div>
<div class="shadow-lg">Large shadow</div>
<div class="rounded-lg">Large border radius</div>
<div class="transition-normal">Smooth transitions</div>
```

## 🏗️ **Creating Modern Components**

### **1. Component Template Structure**
```html
<mat-card class="modern-card shadow-md transition-normal"
          [@fadeIn]
          [@cardHover]="hoverState"
          (mouseenter)="hoverState = 'hovered'"
          (mouseleave)="hoverState = 'default'">
  
  <!-- Header with icon -->
  <div class="card-header flex items-center gap-sm">
    <mat-icon>{{ getIcon('dashboard') }}</mat-icon>
    <h3>Card Title</h3>
  </div>
  
  <!-- Content -->
  <div class="card-content p-lg">
    <p>Card content goes here</p>
  </div>
  
  <!-- Actions -->
  <div class="card-actions flex justify-end gap-sm p-md">
    <button mat-button>Cancel</button>
    <button mat-raised-button color="primary">Save</button>
  </div>
</mat-card>
```

### **2. Component Styles**
```scss
.modern-card {
  background: var(--sm-background-paper);
  border: 1px solid var(--sm-border-light);
  border-radius: var(--sm-border-radius-lg);
  overflow: hidden;
  
  &:hover {
    border-color: var(--sm-border-medium);
    transform: translateY(-2px);
  }
  
  .card-header {
    background: var(--sm-background-surface);
    border-bottom: 1px solid var(--sm-border-light);
    
    h3 {
      margin: 0;
      color: var(--sm-text-primary);
      font-weight: 600;
    }
    
    mat-icon {
      color: var(--sm-primary-main);
    }
  }
  
  .card-content {
    color: var(--sm-text-secondary);
    line-height: 1.6;
  }
  
  .card-actions {
    background: var(--sm-background-surface);
    border-top: 1px solid var(--sm-border-light);
  }
}

// Dark theme support
.dark-theme .modern-card {
  background: var(--sm-background-paper);
  border-color: var(--sm-border-light);
  
  .card-header,
  .card-actions {
    background: var(--sm-background-surface);
    border-color: var(--sm-border-light);
  }
}
```

### **3. Component TypeScript**
```typescript
import { Component } from '@angular/core';
import { fadeInAnimation, cardHoverAnimation } from '../core/animations/staffmanager-animations';
import { IconService } from '../core/services/icon.service';

@Component({
  selector: 'app-modern-card',
  templateUrl: './modern-card.component.html',
  styleUrls: ['./modern-card.component.scss'],
  animations: [fadeInAnimation, cardHoverAnimation]
})
export class ModernCardComponent {
  hoverState: 'default' | 'hovered' = 'default';
  
  constructor(private iconService: IconService) {}
  
  getIcon(semanticName: string): string {
    return this.iconService.getIconName(semanticName);
  }
}
```

## 📱 **Responsive Design**

### **Breakpoint Usage**
```scss
.my-component {
  // Desktop first approach
  grid-template-columns: repeat(4, 1fr);
  
  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (max-width: 900px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
}
```

### **Touch-Friendly Elements**
```scss
.touch-button {
  min-width: 44px;
  min-height: 44px;
  padding: var(--sm-spacing-sm) var(--sm-spacing-md);
  border-radius: var(--sm-border-radius-md);
  transition: all var(--sm-transition-fast);
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--sm-shadow-md);
  }
  
  &:active {
    transform: translateY(0) scale(0.95);
  }
}
```

## 🌙 **Dark Theme Implementation**

### **Component Dark Theme Support**
```scss
.my-component {
  background: var(--sm-background-paper);
  color: var(--sm-text-primary);
  
  .sub-element {
    border: 1px solid var(--sm-border-light);
  }
}

// Dark theme automatically handled by CSS variables
// No additional dark theme styles needed if using design tokens
```

## ✅ **Best Practices**

1. **Always use design tokens** instead of hardcoded values
2. **Import animations** at the component level, not globally
3. **Use semantic icon names** through the IconService
4. **Apply utility classes** for common styling patterns
5. **Test in both light and dark themes**
6. **Ensure 44px minimum touch targets** for mobile
7. **Use transition classes** for smooth interactions
8. **Follow the established animation patterns**

## 🔧 **Common Patterns**

### **Loading States**
```html
<div class="loading-container" [@fadeIn]>
  <mat-spinner diameter="40"></mat-spinner>
  <p>Loading...</p>
</div>
```

### **Error States**
```html
<div class="error-state" [@shakeAnimation]="errorState">
  <mat-icon color="warn">{{ getIcon('error') }}</mat-icon>
  <p>Something went wrong</p>
</div>
```

### **Success States**
```html
<div class="success-state" [@bounceAnimation]="successState">
  <mat-icon color="primary">{{ getIcon('success') }}</mat-icon>
  <p>Operation completed successfully</p>
</div>
```

This implementation guide ensures consistent application of the modern UI/UX system across all StaffManager components.
