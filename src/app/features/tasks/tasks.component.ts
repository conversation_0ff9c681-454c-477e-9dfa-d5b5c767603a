import { Component, OnInit, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, combineLatest, map, startWith } from 'rxjs';

// Angular Material Imports
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ChipModule } from 'primeng/chip';
import { BadgeModule } from 'primeng/badge';
import { MenuModule } from 'primeng/menu';

import { ToastModule } from 'primeng/toast';
import { ProgressBarModule } from 'primeng/progressbar';
import { CheckboxModule } from 'primeng/checkbox';
import { CalendarModule } from 'primeng/calendar';


import { ToolbarModule } from 'primeng/toolbar';
import { DividerModule } from 'primeng/divider';
import { ListboxModule } from 'primeng/listbox';
import { AccordionModule } from 'primeng/accordion';

// Services
import { TaskManagementService } from './services/task-management.service';
import { AITaskAssistantService } from './services/ai-task-assistant.service';
import { AuthService } from '../../core/auth/auth.service';
import { StaffFirestoreService } from '../staff/services/staff-firestore.service';

// Models
import { Task, Checklist, TaskFilter, TaskSortOptions } from './models/task.model';
import { StaffMember } from '../staff/models/staff.model';

// Components
import { TaskDialogComponent, TaskDialogData } from './components/task-dialog.component';
import { ChecklistDialogComponent, ChecklistDialogData } from './components/checklist-dialog.component';

@Component({
  selector: 'app-tasks',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule, TabViewModule,
    TableModule,
    PaginatorModule
    InputTextModule, DropdownModule,
    ChipModule,
    BadgeModule,
    MenuModule
    ToastModule,
    ProgressBarModule,
    CheckboxModule,
    CalendarModule,
    ToolbarModule,
    DividerModule,
    ListboxModule,
    AccordionModule
  ],
  templateUrl: './tasks.component.html',
  styleUrls: ['./tasks.component.scss']
})
export class TasksComponent implements OnInit {
  // Injected services
  private taskService = inject(TaskManagementService);
  private aiService = inject(AITaskAssistantService);
  private authService = inject(AuthService);
  private staffService = inject(StaffFirestoreService);
  private dialog = inject(MatDialog);
  private snackBar = inject(MessageService);
  private router = inject(Router);

  // Signals for reactive state management
  selectedTab = signal(0);
  isLoading = signal(false);
  searchText = signal('');
  selectedFilters = signal<TaskFilter>({});
  sortOptions = signal<TaskSortOptions>({ field: 'createdAt', direction: 'desc' });

  // Data observables
  tasks$!: Observable<Task[]>;
  checklists$!: Observable<Checklist[]>;
  staff$!: Observable<StaffMember[]>;
  currentUser$ = this.authService.userProfile$;

  // Computed values
  filteredTasks$ = computed(() => {
    // This will be implemented with proper filtering logic
    return this.tasks$;
  });

  // Table configuration
  taskColumns = ['title', 'status', 'priority', 'assignedTo', 'dueDate', 'progress', 'actions'];
  checklistColumns = ['title', 'status', 'priority', 'assignedTo', 'dueDate', 'completion', 'actions'];

  // Filter options
  statusOptions = [
    { value: 'pending', label: 'Pending', color: 'warn' },
    { value: 'in-progress', label: 'In Progress', color: 'primary' },
    { value: 'completed', label: 'Completed', color: 'accent' },
    { value: 'on-hold', label: 'On Hold', color: 'basic' },
    { value: 'cancelled', label: 'Cancelled', color: 'basic' },
    { value: 'overdue', label: 'Overdue', color: 'warn' }
  ];

  priorityOptions = [
    { value: 'low', label: 'Low', color: 'basic' },
    { value: 'medium', label: 'Medium', color: 'primary' },
    { value: 'high', label: 'High', color: 'accent' },
    { value: 'urgent', label: 'Urgent', color: 'warn' },
    { value: 'critical', label: 'Critical', color: 'warn' }
  ];

  categoryOptions = [
    { value: 'administrative', label: 'Administrative' },
    { value: 'customer-service', label: 'Customer Service' },
    { value: 'training', label: 'Training' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'project', label: 'Project' },
    { value: 'safety', label: 'Safety' },
    { value: 'compliance', label: 'Compliance' },
    { value: 'custom', label: 'Custom' }
  ];

  ngOnInit(): void {
    this.initializeData();
  }

  private initializeData(): void {
    this.isLoading.set(true);

    // Get current user's business context
    this.currentUser$.subscribe(user => {
      if (user?.primaryBusinessId) {
        this.loadTasksAndChecklists(user.primaryBusinessId);
        this.loadStaff(user.primaryBusinessId);
      }
    });
  }

  private loadTasksAndChecklists(businessId: string): void {
    const filter: TaskFilter = {
      businessId,
      ...this.selectedFilters()
    };

    this.tasks$ = this.taskService.getTasks(filter, this.sortOptions());
    this.checklists$ = this.taskService.getChecklists(filter, this.sortOptions());

    // Subscribe to data loading completion
    combineLatest([this.tasks$, this.checklists$]).subscribe(() => {
      this.isLoading.set(false);
    });
  }

  private loadStaff(businessId: string): void {
    this.staff$ = this.staffService.getActiveStaff(businessId);
  }

  // ==================== TASK ACTIONS ====================

  createTask(): void {
    // Open task creation dialog
    this.openTaskDialog();
  }

  createChecklist(): void {
    // Open checklist creation dialog
    this.openChecklistDialog();
  }

  editTask(task: Task): void {
    this.openTaskDialog(task);
  }

  editChecklist(checklist: Checklist): void {
    this.openChecklistDialog(checklist);
  }

  deleteTask(task: Task): void {
    if (confirm(`Are you sure you want to delete "${task.title}"?`)) {
      this.taskService.deleteTask(task.id).subscribe({
        next: () => {
          this.snackBar.open('Task deleted successfully', 'Close', { duration: 3000 });
          this.refreshData();
        },
        error: (error) => {
          this.snackBar.open('Error deleting task', 'Close', { duration: 3000 });
          console.error('Delete task error:', error);
        }
      });
    }
  }

  deleteChecklist(checklist: Checklist): void {
    if (confirm(`Are you sure you want to delete "${checklist.title}"?`)) {
      this.taskService.deleteChecklist(checklist.id).subscribe({
        next: () => {
          this.snackBar.open('Checklist deleted successfully', 'Close', { duration: 3000 });
          this.refreshData();
        },
        error: (error) => {
          this.snackBar.open('Error deleting checklist', 'Close', { duration: 3000 });
          console.error('Delete checklist error:', error);
        }
      });
    }
  }

  // ==================== AI ASSISTANCE ====================

  async getAITaskSuggestions(): Promise<void> {
    this.isLoading.set(true);

    try {
      const user = await this.currentUser$.pipe(map(u => u)).toPromise();
      if (user?.staffId && user?.primaryBusinessId) {
        const suggestions = await this.aiService.generateTaskSuggestions(
          user.staffId,
          user.primaryBusinessId,
          'General task suggestions for productivity improvement'
        );

        // Show AI suggestions dialog
        this.showAISuggestionsDialog(suggestions);
      }
    } catch (error) {
      this.snackBar.open('Error getting AI suggestions', 'Close', { duration: 3000 });
      console.error('AI suggestions error:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  async analyzeProductivity(): Promise<void> {
    this.isLoading.set(true);

    try {
      const user = await this.currentUser$.pipe(map(u => u)).toPromise();
      if (user?.staffId) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30); // Last 30 days

        const analysis = await this.aiService.analyzeStaffProductivity(user.staffId, {
          start: startDate,
          end: endDate
        });

        // Show productivity analysis dialog
        this.showProductivityAnalysisDialog(analysis);
      }
    } catch (error) {
      this.snackBar.open('Error analyzing productivity', 'Close', { duration: 3000 });
      console.error('Productivity analysis error:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  // ==================== FILTERING AND SORTING ====================

  applyFilters(filters: TaskFilter): void {
    this.selectedFilters.set(filters);
    this.refreshData();
  }

  applySorting(sort: TaskSortOptions): void {
    this.sortOptions.set(sort);
    this.refreshData();
  }

  clearFilters(): void {
    this.selectedFilters.set({});
    this.searchText.set('');
    this.refreshData();
  }

  // ==================== UTILITY METHODS ====================

  private refreshData(): void {
    this.currentUser$.subscribe(user => {
      if (user?.primaryBusinessId) {
        this.loadTasksAndChecklists(user.primaryBusinessId);
      }
    });
  }

  private openTaskDialog(task?: Task): void {
    this.currentUser$.subscribe(user => {
      if (!user?.primaryBusinessId) return;

      const dialogData: TaskDialogData = {
        task,
        businessId: user.primaryBusinessId,
        mode: task ? 'edit' : 'create'
      };

      const dialogRef = this.dialog.open(TaskDialogComponent, {
        width: '800px',
        maxWidth: '90vw',
        maxHeight: '90vh',
        data: dialogData,
        disableClose: true
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.refreshData();
        }
      });
    });
  }

  private openChecklistDialog(checklist?: Checklist): void {
    this.currentUser$.subscribe(user => {
      if (!user?.primaryBusinessId) return;

      const dialogData: ChecklistDialogData = {
        checklist,
        businessId: user.primaryBusinessId,
        mode: checklist ? 'edit' : 'create'
      };

      const dialogRef = this.dialog.open(ChecklistDialogComponent, {
        width: '900px',
        maxWidth: '95vw',
        maxHeight: '90vh',
        data: dialogData,
        disableClose: true
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.refreshData();
        }
      });
    });
  }

  private showAISuggestionsDialog(suggestions: any[]): void {
    // Implementation for AI suggestions dialog will be added
    console.log('AI suggestions:', suggestions);
  }

  private showProductivityAnalysisDialog(analysis: any): void {
    // Implementation for productivity analysis dialog will be added
    console.log('Productivity analysis:', analysis);
  }

  getStatusColor(status: string): string {
    const statusOption = this.statusOptions.find(option => option.value === status);
    return statusOption?.color || 'basic';
  }

  getPriorityColor(priority: string): string {
    const priorityOption = this.priorityOptions.find(option => option.value === priority);
    return priorityOption?.color || 'basic';
  }

  formatStaffNames(staffIds: string[]): Observable<string> {
    return this.staff$.pipe(
      map(staff => {
        const names = staffIds.map(id => {
          const member = staff.find(s => s.id === id);
          return member ? `${member.firstName} ${member.lastName}` : 'Unknown';
        });
        return names.join(', ');
      })
    );
  }

  // Helper methods for template
  getTasksByStatus(tasks: Task[], status: string): Task[] {
    return tasks.filter(task => task.status === status);
  }

  getChecklistsByStatus(checklists: Checklist[], status: string): Checklist[] {
    return checklists.filter(checklist => checklist.status === status);
  }

  isTaskOverdue(task: Task): boolean {
    return !!(task.dueDate && task.dueDate < new Date() && task.status !== 'completed');
  }

  isChecklistOverdue(checklist: Checklist): boolean {
    return !!(checklist.dueDate && checklist.dueDate < new Date() && checklist.status !== 'completed');
  }

  getTasksDataSource(): Task[] {
    return [];
  }

  getChecklistsDataSource(): Checklist[] {
    return [];
  }
}
