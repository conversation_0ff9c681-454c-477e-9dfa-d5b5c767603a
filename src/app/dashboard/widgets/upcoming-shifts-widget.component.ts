import { Component, Input } from '@angular/core';
import { CardModule } from 'primeng/card';
import { CommonModule } from '@angular/common';

interface Shift {
  id: string;
  startTime: string;
  endTime: string;
  staffName: string;
}

@Component({
  selector: 'app-upcoming-shifts-widget',
  standalone: true,
  imports: [CardModule, CommonModule],
  template: `
    <p-card class="number-widget">
      <div class="number-widget-content">
        <div class="number-widget-title">{{ title }}</div>
        <div class="number-widget-value">{{ value }}</div>
        <div class="number-widget-description">{{ description }}</div>
        <div *ngIf="shifts?.length" class="number-widget-shifts">
          <div class="number-widget-shifts-title">Today's Schedule:</div>
          <ul>
            <li *ngFor="let shift of shifts">
              {{ shift.startTime }} - {{ shift.endTime }}: {{ shift.staffName }}
            </li>
          </ul>
        </div>
      </div>
    </p-card>
  `,
  styleUrls: ['./number-widget.component.scss']
})
export class UpcomingShiftsWidgetComponent {
  @Input() title = 'Upcoming Shifts';
  @Input() value: number | string = '';
  @Input() description = '';
  @Input() shifts: Shift[] = [];
} 