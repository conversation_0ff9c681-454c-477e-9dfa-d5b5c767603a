import { Component, EventEmitter, Output, Input } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { BadgeModule } from 'primeng/badge';
import { ToolbarModule } from 'primeng/toolbar';
import { DividerModule } from 'primeng/divider';
import { MenuItem } from 'primeng/api';
import { fadeInAnimation, pulseAnimation, buttonPressAnimation } from '../core/animations/staffmanager-animations';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, ButtonModule, MenuModule, BadgeModule, ToolbarModule, DividerModule],
  providers: [DatePipe],
  animations: [fadeInAnimation, pulseAnimation, buttonPressAnimation],
  template: `
    <p-toolbar class="app-header" [@fadeIn]
               styleClass="custom-toolbar"
               [ngStyle]="{
                 'background': 'linear-gradient(135deg, #1976d2 0%, #42a5f5 50%, #7c3aed 100%)',
                 'color': 'white',
                 'box-shadow': '0 4px 12px rgba(0,0,0,0.15)',
                 'height': '64px',
                 'position': 'relative',
                 'z-index': '1000',
                 'border': 'none',
                 'border-radius': '0'
               }">
      <ng-template pTemplate="start">
        <p-button *ngIf="showMenuButton"
                  (onClick)="menuToggle.emit()"
                  icon="pi pi-bars"
                  [text]="true"
                  [rounded]="true"
                  aria-label="Open sidebar"
                  styleClass="p-button-text p-button-plain"
                  [ngStyle]="{'color': 'white', 'margin-right': '16px', 'width': '44px', 'height': '44px'}"
                  [@buttonPress]>
        </p-button>

        <div [@fadeIn] style="display: flex; align-items: center; margin-left: 16px; padding: 8px 16px;
                             border-radius: 20px; background: rgba(255,255,255,0.2);
                             backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.3);
                             transition: all 0.3s ease; cursor: pointer;">
          <span style="font-size: 0.9rem; font-weight: 500; margin-right: 8px; opacity: 0.9;">{{ today | date:'MMMM d, yyyy' }}</span>
          <span style="font-size: 0.9rem; font-weight: 600; color: rgba(255,255,255,0.95);">{{ now | date:'h:mm a' }}</span>
        </div></ng-template>

      <ng-template pTemplate="center">
        <span [@fadeIn] style="font-weight: 700; font-size: 1.5rem; letter-spacing: -0.5px;
                              text-shadow: 0 2px 4px rgba(0,0,0,0.3);">StaffManager</span></ng-template>

      <ng-template pTemplate="end">

        <p-button (onClick)="notificationsMenu.toggle($event)"
                  icon="pi pi-bell"
                  [text]="true"
                  [rounded]="true"
                  aria-label="Notifications"
                  styleClass="p-button-text p-button-plain"
                  [ngStyle]="{'color': 'white', 'margin': '0 4px', 'width': '44px', 'height': '44px'}"
                  [@buttonPress]
                  [class.pulse]="hasNewNotifications">
          <p-badge *ngIf="notificationCount > 0"
                   [value]="notificationCount.toString()"
                   severity="danger"
                   [ngStyle]="{'position': 'absolute', 'top': '8px', 'right': '8px'}">
          </p-badge>
        </p-button>

        <p-menu #notificationsMenu [popup]="true" [model]="notificationMenuItems"></p-menu>

        <p-button (onClick)="chatMenu.toggle($event)"
                  icon="pi pi-comments"
                  [text]="true"
                  [rounded]="true"
                  aria-label="Chat"
                  styleClass="p-button-text p-button-plain"
                  [ngStyle]="{'color': 'white', 'margin': '0 4px', 'width': '44px', 'height': '44px'}">
          <p-badge value="2"
                   severity="danger"
                   [ngStyle]="{'position': 'absolute', 'top': '8px', 'right': '8px'}">
          </p-badge>
        </p-button>

        <p-menu #chatMenu [popup]="true" [model]="chatMenuItems"></p-menu</ng-template>
    </p-toolbar>
  `,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  @Input() showMenuButton = false;
  @Output() menuToggle = new EventEmitter<void>();

  today = new Date();
  now = new Date();

  notifications = [
    'New staff member added',
    'Meeting scheduled for tomorrow',
    'Payroll exported to ADP'
  ];

  chats = [
    '2 unread messages',
    'Support: New reply'
  ];

  notificationMenuItems: MenuItem[] = [];
  chatMenuItems: MenuItem[] = [];

  // Computed properties for enhanced functionality
  get notificationCount(): number {
    return this.notifications.length;
  }

  get hasNewNotifications(): boolean {
    return this.notificationCount > 0;
  }

  get chatCount(): number {
    return this.chats.length;
  }

  constructor() {
    // Update time every minute
    setInterval(() => {
      this.now = new Date();
    }, 60000);

    // Initialize menu items
    this.initializeMenuItems();
  }

  private initializeMenuItems(): void {
    this.notificationMenuItems = this.notifications.map(notification => ({
      label: notification,
      icon: 'pi pi-bell',
      command: () => {
        // Handle notification click
        console.log('Notification clicked:', notification);
      }
    }));

    this.notificationMenuItems.push(
      { separator: true },
      {
        label: 'View all notifications',
        icon: 'pi pi-eye',
        command: () => {
          // Handle view all notifications
          console.log('View all notifications');
        }
      }
    );

    this.chatMenuItems = this.chats.map(chat => ({
      label: chat,
      icon: 'pi pi-comments',
      command: () => {
        // Handle chat click
        console.log('Chat clicked:', chat);
      }
    }));

    this.chatMenuItems.push(
      { separator: true },
      {
        label: 'Open chat',
        icon: 'pi pi-external-link',
        command: () => {
          // Handle open chat
          console.log('Open chat');
        }
      }
    );
  }
}