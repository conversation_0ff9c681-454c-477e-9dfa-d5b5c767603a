import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Observable, interval } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';

// Angular Material
import { CardModule } from 'primeng/card';

import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { DividerModule } from 'primeng/divider';

// Services
import { BusinessProfileService } from '../../core/services/business-profile.service';

// Models
import { BusinessProfile, BusinessStatus } from '../../core/models/business.model';

@Component({
  selector: 'app-business-status-widget',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    CardModule
    ButtonModule,
    ChipModule,
    DividerModule
  ],
  template: `
    <p-card class="business-status-widget">
      <ng-template pTemplate="header">
        <i class="pi pi-circle"></i>
        <h3>
        <p>Content</p></ng-template>

      <p-card-content *ngIf="businessStatus$ | async as status">
        <div class="status-main">
          <div class="status-indicator">
            <p-chip [class]="statusClass">
              <i class="pi pi-circle"></i>
              {{ status.isCurrentlyOpen ? 'OPEN' : 'CLOSED' }}
            </p-chip>
          </div>

          <div class="next-change" *ngIf="status.nextStatusChange">
            <span class="label">
              {{ status.isCurrentlyOpen ? 'Closes' : 'Opens' }} in:
            </span>
            <span class="time">{{ getTimeUntil(status.nextStatusChange) }}</span>
          </div>
        </div>

        <p-divider></p-divider>

        <div class="status-details">
          <div class="detail-item" *ngIf="status.currentShift">
            <i class="pi pi-clock"></i>
            <span>Current Shift: {{ status.currentShift }}</span>
          </div>

          <div class="detail-item" *ngIf="status.staffOnDuty > 0">
            <i class="pi pi-users"></i>
            <span>{{ status.staffOnDuty }} staff on duty</span>
          </div>

          <div class="detail-item" *ngIf="status.estimatedCapacity > 0">
            <i class="pi pi-building"></i>
            <span>Capacity: {{ status.estimatedCapacity }}</span>
          </div>
        </div>

        <div class="today-hours" *ngIf="todayHours$ | async as hours">
          <h4>Today's Hours</h4>
          <div class="hours-display" *ngIf="hours.isOpen; else closedToday">
            <i class="pi pi-circle"></i>
            <span>{{ hours.openTime }} - {{ hours.closeTime }}</span>
          </div>
          <ng-template #closedToday>
            <div class="hours-display closed">
              <i class="pi pi-circle"></i>
              <span>Closed Today</span>
            </div></ng-template>
        </div></ng-template>

      <ng-template pTemplate="footer">
        <button p-button routerLink="/business/profile">
          <i class="pi pi-cog"></i>
          Manage Hours
        </p-button></ng-template></p-card>
  `,
  styles: [`
    .business-status-widget {
      height: 100%;
      display: flex;
      flex-direction: column;

      mat-card-content {
        flex: 1;
        padding: 16px;
      }
    }

    .status-main {
      text-align: center;
      margin-bottom: 16px;

      .status-indicator {
        margin-bottom: 12px;

        mat-chip {
          font-size: 16px;
          font-weight: 600;
          padding: 8px 16px;

          &.open {
            background-color: #4caf50;
            color: white;
          }

          &.closed {
            background-color: #f44336;
            color: white;
          }

          mat-icon {
            margin-right: 8px;
          }
        }
      }

      .next-change {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .label {
          font-size: 14px;
          color: #666;
        }

        .time {
          font-size: 18px;
          font-weight: 500;
          color: #1976d2;
        }
      }
    }

    .status-details {
      margin: 16px 0;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 14px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          color: #666;
        }
      }
    }

    .today-hours {
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 500;
        color: #424242;
      }

      .hours-display {
        display: flex;
        align-items: center;
        gap: 8px;

        &.closed {
          color: #f44336;
        }

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }

    .open {
      color: #4caf50;
    }

    .closed {
      color: #f44336;
    }

    mat-card-avatar {
      &.open {
        background-color: #4caf50;
        color: white;
      }

      &.closed {
        background-color: #f44336;
        color: white;
      }
    }
  `]
})
export class BusinessStatusWidgetComponent implements OnInit {
  private businessProfileService = inject(BusinessProfileService);

  businessStatus$!: Observable<BusinessStatus | null>;
  todayHours$!: Observable<any>;
  currentTime = new Date();

  ngOnInit(): void {
    // Update current time every minute
    interval(60000).pipe(
      startWith(0)
    ).subscribe(() => {
      this.currentTime = new Date();
    });

    // Get business status
    this.businessStatus$ = this.businessProfileService.selectedBusinessProfile$.pipe(
      map(profile => {
        if (!profile) return null;
        return this.businessProfileService.getCurrentBusinessStatus(profile);
      })
    );

    // Get today's hours
    this.todayHours$ = this.businessProfileService.selectedBusinessProfile$.pipe(
      map(profile => {
        if (!profile) return null;

        const now = new Date();
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        const today = dayNames[now.getDay()];

        return profile.hoursOfOperation[today as keyof typeof profile.hoursOfOperation];
      })
    );
  }

  get statusIcon(): string {
    // This will be updated by the async pipe
    return 'store';
  }

  get statusClass(): string {
    // This will be updated by the async pipe
    return 'open';
  }

  getTimeUntil(targetDate: Date): string {
    const now = new Date();
    const diff = targetDate.getTime() - now.getTime();

    if (diff <= 0) {
      return 'Now';
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }
}
