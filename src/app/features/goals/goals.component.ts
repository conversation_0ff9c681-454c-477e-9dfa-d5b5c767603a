import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Observable, combineLatest, map } from 'rxjs';

// Angular Material
import { TabViewModule } from 'primeng/tabview';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ToolbarModule } from 'primeng/toolbar';
import { ChipModule } from 'primeng/chip';
import { ProgressBarModule } from 'primeng/progressbar';
import { BadgeModule } from 'primeng/badge';
import { MenuModule } from 'primeng/menu';
import { DividerModule } from 'primeng/divider';
import { ListboxModule } from 'primeng/listbox';
import { DropdownModule } from 'primeng/dropdown';

import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';

import { MatDialog } from '@angular/material/dialog';

// Services
import { StaffFirestoreService } from '../staff/services/staff-firestore.service';
import { AuthService } from '../../core/auth/auth.service';
import { BusinessProfileService } from '../../core/services/business-profile.service';
import { GoalsService } from './services/goals.service';

// Models
import { StaffGoalExtended, StaffMember } from '../staff/models/staff.model';

// Components
import { GoalCardComponent } from './components/goal-card.component';

@Component({
  selector: 'app-goals',
  standalone: true,
  imports: [
    CommonModule,
    TabViewModule,
    CardModule,
    ButtonModule
    ToolbarModule,
    ChipModule,
    ProgressBarModule,
    BadgeModule,
    MenuModule,
    DividerModule,
    ListboxModule,
    DropdownModule
    InputTextModule,
    CalendarModule,
    GoalCardComponent
  ],
  template: `
    <div class="goals-container">
      <!-- Header Section -->
      <div class="goals-header">
        <div class="header-content">
          <div class="title-section">
            <h1>
              <i class="pi pi-circle"></i>
              Goals Management
            </h1>
            <p class="subtitle">Track progress, set objectives, and achieve success across your organization</p>
          </div>

          <div class="header-actions">
            <p-button color="primary" (click)="createGoal()">
              <i class="pi pi-plus"></i>
              Create Goal
            </p-button>
            <p-button [outlined]="true" color="primary" (click)="getAIGoalSuggestions()">
              <i class="pi pi-brain"></i>
              AI Suggestions
            </p-button>
          </div>
        </div>

        <!-- Filters and Stats -->
        <div class="goals-filters">
          <div class="p-field" appearance="outline" class="filter-field">
            <label>Filter by Status</label>
            <p-dropdown [(value)]="selectedStatus" (selectionChange)="onFilterChange()">
              <option value="">All Statuses</option>
              <option value="not-started">Not Started</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="overdue">Overdue</option>
            </p-dropdown>
          </div>

          <div class="p-field" appearance="outline" class="filter-field">
            <label>Filter by Type</label>
            <p-dropdown [(value)]="selectedType" (selectionChange)="onFilterChange()">
              <option value="">All Types</option>
              <option value="individual">Individual</option>
              <option value="team">Team</option>
              <option value="company">Company</option>
            </p-dropdown>
          </div>

          <div class="p-field" appearance="outline" class="filter-field">
            <label>Filter by Category</label>
            <p-dropdown [(value)]="selectedCategory" (selectionChange)="onFilterChange()">
              <option value="">All Categories</option>
              <option value="sales">Sales</option>
              <option value="performance">Performance</option>
              <option value="training">Training</option>
              <option value="attendance">Attendance</option>
              <option value="custom">Custom</option>
            </p-dropdown>
          </div>
        </div>

        <!-- Goal Statistics -->
        <div class="goals-stats">
          <p-card class="stat-card">
            <ng-template pTemplate="content">
              <div class="stat-value">{{ (totalGoals$ | async) || 0 }}</div>
              <div class="stat-label">Total Goals</div></ng-template></p-card>
          <p-card class="stat-card">
            <ng-template pTemplate="content">
              <div class="stat-value">{{ (activeGoals$ | async) || 0 }}</div>
              <div class="stat-label">Active Goals</div></ng-template></p-card>
          <p-card class="stat-card">
            <ng-template pTemplate="content">
              <div class="stat-value">{{ (completedGoals$ | async) || 0 }}</div>
              <div class="stat-label">Completed</div></ng-template></p-card>
          <p-card class="stat-card">
            <ng-template pTemplate="content">
              <div class="stat-value">{{ (overdueGoals$ | async) || 0 }}</div>
              <div class="stat-label">Overdue</div></ng-template></p-card>
        </div>
      </div>

      <!-- Goals Content -->
      <div class="goals-content">
        <p-tabView class="goals-tabs" animationDuration="300ms">
          <!-- All Goals Tab -->
          <p-tabPanel>
            <ng-template mat-tab-label>
              All Goals
              <span class="tab-badge" *ngIf="(filteredGoals$ | async)?.length as count">
                ({{ count }})
              </span></ng-template>
            <div class="tab-content">
              <div class="goals-grid">
                <app-goal-card
                  *ngFor="let goal of filteredGoals$ | async; trackBy: trackByGoalId"
                  [goal]="goal"
                  [canEdit]="canEditGoal(goal)"
                  (edit)="editGoal($event)"
                  (delete)="deleteGoal($event)"
                  (viewDetails)="viewGoalDetails($event)">
                </app-goal-card>
              </div>

              <div class="empty-state" *ngIf="(filteredGoals$ | async)?.length === 0">
                <i class="pi pi-circle"></i>
                <h3>No goals found</h3>
                <p>Create your first goal to start tracking progress and achieving objectives.</p>
                <p-button color="primary" (click)="createGoal()">
                  <i class="pi pi-plus"></i>
                  Create Goal
                </p-button>
              </div>
            </div>
          </p-tabPanel>

          <!-- My Goals Tab -->
          <p-tabPanel>
            <ng-template mat-tab-label>
              My Goals
              <span class="tab-badge" *ngIf="(myGoals$ | async)?.length as count">
                ({{ count }})
              </span></ng-template>
            <div class="tab-content">
              <div class="goals-grid">
                <app-goal-card
                  *ngFor="let goal of myGoals$ | async; trackBy: trackByGoalId"
                  [goal]="goal"
                  [canEdit]="canEditGoal(goal)"
                  (edit)="editGoal($event)"
                  (delete)="deleteGoal($event)"
                  (viewDetails)="viewGoalDetails($event)">
                </app-goal-card>
              </div>
            </div>
          </p-tabPanel>

          <!-- Team Goals Tab -->
          <p-tabPanel>
            <ng-template mat-tab-label>
              Team Goals
              <span class="tab-badge" *ngIf="(teamGoals$ | async)?.length as count">
                ({{ count }})
              </span></ng-template>
            <div class="tab-content">
              <div class="goals-grid">
                <app-goal-card
                  *ngFor="let goal of teamGoals$ | async; trackBy: trackByGoalId"
                  [goal]="goal"
                  [canEdit]="canEditGoal(goal)"
                  (edit)="editGoal($event)"
                  (delete)="deleteGoal($event)"
                  (viewDetails)="viewGoalDetails($event)">
                </app-goal-card>
              </div>
            </div>
          </p-tabPanel>

          <!-- Company Goals Tab -->
          <p-tabPanel>
            <ng-template mat-tab-label>
              Company Goals
              <span class="tab-badge" *ngIf="(companyGoals$ | async)?.length as count">
                ({{ count }})
              </span></ng-template>
            <div class="tab-content">
              <div class="goals-grid">
                <app-goal-card
                  *ngFor="let goal of companyGoals$ | async; trackBy: trackByGoalId"
                  [goal]="goal"
                  [canEdit]="canEditGoal(goal)"
                  (edit)="editGoal($event)"
                  (delete)="deleteGoal($event)"
                  (viewDetails)="viewGoalDetails($event)">
                </app-goal-card>
              </div>
            </div>
          </p-tabPanel>
        </p-tabView>
      </div>
    </div>
  `,
  styles: [`
    .goals-container {
      padding: 24px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .goals-header {
      margin-bottom: 32px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
    }

    .title-section h1 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 2rem;
      font-weight: 500;
    }

    .subtitle {
      color: var(--mdc-theme-text-secondary-on-background);
      margin: 0;
      font-size: 1.1rem;
    }

    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .goals-filters {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      flex-wrap: wrap;
    }

    .filter-field {
      min-width: 200px;
    }

    .goals-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .stat-card {
      text-align: center;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 600;
      color: var(--mdc-theme-primary);
    }

    .stat-label {
      color: var(--mdc-theme-text-secondary-on-background);
      font-size: 0.875rem;
      margin-top: 4px;
    }

    .goals-content {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .goals-tabs {
      min-height: 500px;
    }

    .tab-content {
      padding: 24px;
    }

    .tab-badge {
      margin-left: 8px;
      font-size: 0.75rem;
      opacity: 0.7;
    }

    .goals-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 24px;
    }

    .empty-state {
      text-align: center;
      padding: 64px 24px;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .empty-state mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state h3 {
      margin: 0 0 8px 0;
      font-weight: 500;
    }

    .empty-state p {
      margin: 0 0 24px 0;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    @media (max-width: 768px) {
      .goals-container {
        padding: 16px;
      }

      .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .goals-filters {
        flex-direction: column;
      }

      .filter-field {
        min-width: unset;
      }

      .goals-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class GoalsComponent implements OnInit {
  private router = inject(Router);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private businessService = inject(BusinessProfileService);
  private goalsService = inject(GoalsService);
  private dialog = inject(MatDialog);

  // Filter properties
  selectedStatus = '';
  selectedType = '';
  selectedCategory = '';

  // Data observables
  allGoals$!: Observable<StaffGoalExtended[]>;
  filteredGoals$!: Observable<StaffGoalExtended[]>;
  myGoals$!: Observable<StaffGoalExtended[]>;
  teamGoals$!: Observable<StaffGoalExtended[]>;
  companyGoals$!: Observable<StaffGoalExtended[]>;

  // Statistics observables
  totalGoals$!: Observable<number>;
  activeGoals$!: Observable<number>;
  completedGoals$!: Observable<number>;
  overdueGoals$!: Observable<number>;

  currentUser$ = this.authService.user$;

  ngOnInit(): void {
    this.initializeData();
  }

  private initializeData(): void {
    // Get all goals using the goals service
    this.allGoals$ = this.goalsService.getGoals();

    // Initialize filtered goals
    this.updateFilteredGoals();

    // Initialize goal type observables
    this.initializeGoalTypes();

    // Initialize statistics
    this.initializeStatistics();
  }

  private updateFilteredGoals(): void {
    this.filteredGoals$ = this.allGoals$.pipe(
      map(goals => this.applyFilters(goals))
    );
  }

  private initializeGoalTypes(): void {
    this.currentUser$.subscribe(user => {
      if (user) {
        this.myGoals$ = this.allGoals$.pipe(
          map(goals => goals.filter(goal => goal.assignedTo.includes(user.uid)))
        );
      }
    });

    this.teamGoals$ = this.allGoals$.pipe(
      map(goals => goals.filter(goal => goal.type === 'team'))
    );

    this.companyGoals$ = this.allGoals$.pipe(
      map(goals => goals.filter(goal => goal.type === 'company'))
    );
  }

  private initializeStatistics(): void {
    this.totalGoals$ = this.allGoals$.pipe(
      map(goals => goals.length)
    );

    this.activeGoals$ = this.allGoals$.pipe(
      map(goals => goals.filter(goal => goal.status === 'in-progress').length)
    );

    this.completedGoals$ = this.allGoals$.pipe(
      map(goals => goals.filter(goal => goal.status === 'completed').length)
    );

    this.overdueGoals$ = this.allGoals$.pipe(
      map(goals => goals.filter(goal => goal.status === 'overdue').length)
    );
  }

  private applyFilters(goals: StaffGoalExtended[]): StaffGoalExtended[] {
    return goals.filter(goal => {
      if (this.selectedStatus && goal.status !== this.selectedStatus) return false;
      if (this.selectedType && goal.type !== this.selectedType) return false;
      if (this.selectedCategory && goal.category !== this.selectedCategory) return false;
      return true;
    });
  }

  onFilterChange(): void {
    this.updateFilteredGoals();
  }

  createGoal(): void {
    this.router.navigate(['/goals/create']);
  }

  editGoal(goal: StaffGoalExtended): void {
    this.router.navigate(['/goals/edit', goal.id]);
  }

  deleteGoal(goal: StaffGoalExtended): void {
    if (confirm(`Are you sure you want to delete the goal "${goal.title}"?`)) {
      this.staffService.deleteGoal(goal.id).subscribe({
        next: () => {
          console.log('Goal deleted successfully');
        },
        error: (error) => {
          console.error('Error deleting goal:', error);
        }
      });
    }
  }

  viewGoalDetails(goal: StaffGoalExtended): void {
    this.router.navigate(['/goals/details', goal.id]);
  }

  canEditGoal(goal: StaffGoalExtended): boolean {
    // Implement permission logic here
    return true; // For now, allow all edits
  }

  getAIGoalSuggestions(): void {
    // Implement AI goal suggestions
    console.log('Getting AI goal suggestions...');
  }

  trackByGoalId(index: number, goal: StaffGoalExtended): string {
    return goal.id;
  }
}
