import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class WidgetDataService {
  getStaffOnShift(): Observable<number> {
    return of(5); // Mocked value
  }

  getUpcomingShifts(): Observable<any[]> {
    return of([
      { id: '1', time: '08:00 - 12:00', name: '<PERSON>' },
      { id: '2', time: '12:00 - 16:00', name: '<PERSON>' },
      { id: '3', time: '16:00 - 20:00', name: '<PERSON>' }
    ]);
  }

  getNotifications(): Observable<number> {
    return of(2); // Mocked value
  }

  getMessages(): Observable<number> {
    return of(1); // Mocked value
  }
}