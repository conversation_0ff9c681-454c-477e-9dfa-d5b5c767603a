// Notification Models for StaffHub and TimeHub
export interface Notification {
  id: string;
  recipientId: string; // Staff ID
  businessId: string;
  
  // Notification content
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success' | 'reminder';
  category: 'schedule' | 'task' | 'timesheet' | 'system' | 'announcement' | 'approval' | 'reminder';
  
  // Priority and urgency
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isUrgent: boolean;
  
  // Delivery channels
  channels: NotificationChannel[];
  
  // Status and tracking
  status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed';
  isRead: boolean;
  readAt?: Date;
  
  // Action and interaction
  actionRequired: boolean;
  actionUrl?: string;
  actionText?: string;
  expiresAt?: Date;
  
  // Related entities
  relatedEntityType?: 'task' | 'shift' | 'timesheet' | 'request' | 'announcement';
  relatedEntityId?: string;
  
  // Metadata
  createdAt: Date;
  sentAt?: Date;
  deliveredAt?: Date;
  createdBy?: string; // System or user ID
  
  // Rich content
  imageUrl?: string;
  attachments?: NotificationAttachment[];
  customData?: any;
}

export interface NotificationChannel {
  type: 'push' | 'email' | 'sms' | 'in-app';
  status: 'pending' | 'sent' | 'delivered' | 'failed';
  sentAt?: Date;
  deliveredAt?: Date;
  errorMessage?: string;
  retryCount: number;
  maxRetries: number;
}

export interface NotificationAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
}

export interface NotificationPreferences {
  staffId: string;
  
  // Channel preferences
  channels: {
    push: {
      enabled: boolean;
      deviceTokens: string[];
      quietHours?: {
        start: string; // HH:mm
        end: string; // HH:mm
      };
    };
    email: {
      enabled: boolean;
      emailAddress: string;
      frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
    };
    sms: {
      enabled: boolean;
      phoneNumber?: string;
      urgentOnly: boolean;
    };
    inApp: {
      enabled: boolean;
      showBadges: boolean;
      playSound: boolean;
    };
  };
  
  // Category preferences
  categories: {
    [category: string]: {
      enabled: boolean;
      channels: ('push' | 'email' | 'sms' | 'in-app')[];
      priority: 'low' | 'medium' | 'high' | 'urgent';
    };
  };
  
  // General settings
  doNotDisturb: {
    enabled: boolean;
    start?: string; // HH:mm
    end?: string; // HH:mm
    daysOfWeek?: number[]; // 0-6
  };
  
  // Metadata
  updatedAt: Date;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  description?: string;
  businessId: string;
  
  // Template content
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success' | 'reminder';
  category: 'schedule' | 'task' | 'timesheet' | 'system' | 'announcement' | 'approval' | 'reminder';
  
  // Template variables
  variables: NotificationVariable[];
  
  // Default settings
  defaultChannels: ('push' | 'email' | 'sms' | 'in-app')[];
  defaultPriority: 'low' | 'medium' | 'high' | 'urgent';
  
  // Triggers
  triggers: NotificationTrigger[];
  
  // Status
  isActive: boolean;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface NotificationVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  description: string;
  isRequired: boolean;
  defaultValue?: any;
}

export interface NotificationTrigger {
  id: string;
  type: 'schedule' | 'event' | 'condition' | 'manual';
  
  // Schedule trigger
  schedule?: {
    type: 'once' | 'recurring';
    date?: Date;
    time?: string; // HH:mm
    recurrence?: {
      pattern: 'daily' | 'weekly' | 'monthly';
      interval: number;
      daysOfWeek?: number[];
    };
  };
  
  // Event trigger
  event?: {
    entityType: 'task' | 'shift' | 'timesheet' | 'request';
    eventType: 'created' | 'updated' | 'completed' | 'overdue' | 'approved' | 'rejected';
    conditions?: NotificationCondition[];
  };
  
  // Condition trigger
  conditions?: NotificationCondition[];
  
  // Status
  isActive: boolean;
}

export interface NotificationCondition {
  field: string;
  operator: 'equals' | 'not-equals' | 'greater-than' | 'less-than' | 'contains' | 'in' | 'not-in';
  value: any;
  logicalOperator?: 'and' | 'or';
}

// Real-time notification events
export interface NotificationEvent {
  type: 'new-notification' | 'notification-read' | 'notification-deleted' | 'preferences-updated';
  staffId: string;
  businessId: string;
  data: any;
  timestamp: Date;
}

// Notification analytics
export interface NotificationAnalytics {
  businessId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  
  // Overall metrics
  totalSent: number;
  totalDelivered: number;
  totalRead: number;
  deliveryRate: number; // percentage
  readRate: number; // percentage
  
  // By channel
  channelStats: {
    [channel: string]: {
      sent: number;
      delivered: number;
      failed: number;
      deliveryRate: number;
    };
  };
  
  // By category
  categoryStats: {
    [category: string]: {
      sent: number;
      read: number;
      readRate: number;
    };
  };
  
  // Performance metrics
  averageDeliveryTime: number; // seconds
  averageReadTime: number; // seconds
  failureReasons: { [reason: string]: number };
}
