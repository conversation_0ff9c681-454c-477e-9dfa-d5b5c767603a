# Contributing to StaffManager

## 🎯 Welcome Contributors!

Thank you for your interest in contributing to StaffManager! This guide will help you get started with contributing to our advanced staff management system built with Angular 19 and Firebase.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)
- [Documentation](#documentation)

## 🤝 Code of Conduct

### Our Pledge

We are committed to making participation in this project a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

### Our Standards

**Positive behavior includes:**
- Using welcoming and inclusive language
- Being respectful of differing viewpoints and experiences
- Gracefully accepting constructive criticism
- Focusing on what is best for the community
- Showing empathy towards other community members

**Unacceptable behavior includes:**
- The use of sexualized language or imagery
- Trolling, insulting/derogatory comments, and personal or political attacks
- Public or private harassment
- Publishing others' private information without explicit permission

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and npm
- Angular CLI 19+
- Git
- Firebase CLI (for backend development)
- Code editor (VS Code recommended)

### Development Setup

1. **Fork and Clone**
```bash
git clone https://github.com/your-username/staffmanager-web-new.git
cd staffmanager-web-new
```

2. **Install Dependencies**
```bash
npm install
```

3. **Environment Setup**
```bash
# Copy environment template
cp src/environments/environment.example.ts src/environments/environment.ts

# Configure your Firebase project settings
# Edit src/environments/environment.ts with your Firebase config
```

4. **Start Development Server**
```bash
npm start
```

5. **Verify Setup**
- Open http://localhost:4200 (or the port shown in terminal)
- Ensure the application loads without errors
- Test basic functionality (login, navigation)

## 🔄 Development Workflow

### Branch Strategy

We use a feature branch workflow:

```
main (production-ready code)
├── develop (integration branch)
├── feature/feature-name
├── bugfix/bug-description
├── hotfix/critical-fix
└── docs/documentation-update
```

### Creating a Feature Branch

```bash
# Update your local main branch
git checkout main
git pull origin main

# Create and switch to feature branch
git checkout -b feature/your-feature-name

# Make your changes and commit
git add .
git commit -m "feat: add new feature description"

# Push to your fork
git push origin feature/your-feature-name
```

### Commit Message Convention

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: Code change that neither fixes a bug nor adds a feature
- `perf`: Performance improvement
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

**Examples:**
```bash
feat(staff): add availability calendar component
fix(auth): resolve login redirect issue
docs(api): update service documentation
style(dashboard): improve widget spacing
refactor(time): optimize time tracking service
test(staff): add unit tests for staff service
chore(deps): update Angular to v19.2.13
```

## 📝 Coding Standards

### TypeScript Guidelines

1. **Strict Type Checking**
```typescript
// ✅ Good - Explicit typing
interface StaffMember {
  id: string;
  name: string;
  role: 'manager' | 'staff' | 'admin';
}

// ❌ Avoid - Any types
const staff: any = { /* ... */ };
```

2. **Interface Definitions**
```typescript
// ✅ Good - Clear interface structure
interface CreateStaffRequest {
  personalInfo: PersonalInfo;
  employmentInfo: EmploymentInfo;
  availability: WeeklyAvailability;
}

// ❌ Avoid - Inline object types
function createStaff(data: { name: string; role: string; /* ... */ }) {
  // ...
}
```

3. **Service Patterns**
```typescript
// ✅ Good - Injectable service with proper typing
@Injectable({ providedIn: 'root' })
export class StaffService {
  private staffSubject = new BehaviorSubject<Staff[]>([]);
  public staff$ = this.staffSubject.asObservable();

  async createStaff(staff: CreateStaffRequest): Promise<string> {
    // Implementation
  }
}
```

### Angular Component Guidelines

1. **Component Structure**
```typescript
@Component({
  selector: 'app-staff-list',
  standalone: true,
  imports: [CommonModule, MatTableModule, MatButtonModule],
  template: `
    <!-- Template content -->
  `,
  styles: [`
    /* Component-specific styles */
  `]
})
export class StaffListComponent implements OnInit, OnDestroy {
  // Public properties first
  @Input() businessId: string | null = null;
  @Output() staffSelected = new EventEmitter<Staff>();

  // Private properties
  private destroy$ = new Subject<void>();

  // Lifecycle hooks
  ngOnInit(): void {
    // Initialization logic
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Public methods
  onStaffClick(staff: Staff): void {
    this.staffSelected.emit(staff);
  }

  // Private methods
  private loadStaff(): void {
    // Implementation
  }
}
```

2. **Template Guidelines**
```html
<!-- ✅ Good - Semantic HTML with proper accessibility -->
<mat-table [dataSource]="staff" class="staff-table" role="table">
  <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
  <mat-row 
    *matRowDef="let row; columns: displayedColumns;"
    (click)="onStaffClick(row)"
    [attr.aria-label]="'Staff member ' + row.name"
    tabindex="0">
  </mat-row>
</mat-table>

<!-- ❌ Avoid - Non-semantic markup -->
<div class="table">
  <div class="row" (click)="onStaffClick(row)">
    <!-- ... -->
  </div>
</div>
```

### SCSS/CSS Guidelines

1. **BEM Methodology**
```scss
// ✅ Good - BEM naming convention
.staff-list {
  &__header {
    display: flex;
    justify-content: space-between;
    
    &--compact {
      padding: 8px;
    }
  }
  
  &__item {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
}
```

2. **Responsive Design**
```scss
// ✅ Good - Mobile-first approach
.dashboard-widget {
  padding: 16px;
  
  @media (min-width: 768px) {
    padding: 24px;
  }
  
  @media (min-width: 1024px) {
    padding: 32px;
  }
}
```

## 🧪 Testing Guidelines

### Unit Testing

1. **Component Testing**
```typescript
describe('StaffListComponent', () => {
  let component: StaffListComponent;
  let fixture: ComponentFixture<StaffListComponent>;
  let staffService: jasmine.SpyObj<StaffService>;

  beforeEach(async () => {
    const staffServiceSpy = jasmine.createSpyObj('StaffService', ['getStaff']);

    await TestBed.configureTestingModule({
      imports: [StaffListComponent],
      providers: [
        { provide: StaffService, useValue: staffServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(StaffListComponent);
    component = fixture.componentInstance;
    staffService = TestBed.inject(StaffService) as jasmine.SpyObj<StaffService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit staffSelected when staff is clicked', () => {
    const mockStaff: Staff = { id: '1', name: 'John Doe' } as Staff;
    spyOn(component.staffSelected, 'emit');

    component.onStaffClick(mockStaff);

    expect(component.staffSelected.emit).toHaveBeenCalledWith(mockStaff);
  });
});
```

2. **Service Testing**
```typescript
describe('StaffService', () => {
  let service: StaffService;
  let firestore: jasmine.SpyObj<Firestore>;

  beforeEach(() => {
    const firestoreSpy = jasmine.createSpyObj('Firestore', ['collection']);

    TestBed.configureTestingModule({
      providers: [
        { provide: Firestore, useValue: firestoreSpy }
      ]
    });

    service = TestBed.inject(StaffService);
    firestore = TestBed.inject(Firestore) as jasmine.SpyObj<Firestore>;
  });

  it('should create staff member', async () => {
    const mockStaff: CreateStaffRequest = {
      name: 'John Doe',
      role: 'staff'
    } as CreateStaffRequest;

    const result = await service.createStaff(mockStaff);

    expect(result).toBeDefined();
    expect(typeof result).toBe('string');
  });
});
```

### E2E Testing

```typescript
// e2e/staff-management.e2e-spec.ts
describe('Staff Management', () => {
  beforeEach(() => {
    cy.visit('/staff');
    cy.login('<EMAIL>', 'password');
  });

  it('should display staff list', () => {
    cy.get('[data-cy=staff-list]').should('be.visible');
    cy.get('[data-cy=staff-item]').should('have.length.greaterThan', 0);
  });

  it('should create new staff member', () => {
    cy.get('[data-cy=add-staff-button]').click();
    cy.get('[data-cy=staff-name-input]').type('John Doe');
    cy.get('[data-cy=staff-role-select]').select('staff');
    cy.get('[data-cy=save-button]').click();
    
    cy.get('[data-cy=success-message]').should('contain', 'Staff member created');
  });
});
```

### Test Coverage Requirements

- **Minimum Coverage**: 80% for all new code
- **Critical Paths**: 95% coverage required
- **Components**: Test all public methods and event emissions
- **Services**: Test all CRUD operations and error handling

## 🔍 Pull Request Process

### Before Submitting

1. **Code Quality Checks**
```bash
# Run linting
npm run lint

# Run tests
npm test

# Run e2e tests
npm run e2e

# Build for production
npm run build:prod
```

2. **Self Review Checklist**
- [ ] Code follows project conventions
- [ ] All tests pass
- [ ] Documentation updated
- [ ] No console.log statements
- [ ] Accessibility considerations addressed
- [ ] Performance impact considered

### PR Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] E2E tests added/updated
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
```

### Review Process

1. **Automated Checks**: All CI checks must pass
2. **Code Review**: At least one maintainer approval required
3. **Testing**: Manual testing for UI changes
4. **Documentation**: Verify documentation updates

## 🐛 Issue Reporting

### Bug Reports

Use the bug report template:

```markdown
**Bug Description**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected Behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment:**
- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Additional Context**
Add any other context about the problem here.
```

### Feature Requests

```markdown
**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is.

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Additional context**
Add any other context or screenshots about the feature request here.
```

## 📚 Documentation

### Code Documentation

1. **JSDoc Comments**
```typescript
/**
 * Creates a new staff member in the system
 * @param staff - The staff member data to create
 * @returns Promise resolving to the created staff member ID
 * @throws {Error} When staff creation fails
 */
async createStaff(staff: CreateStaffRequest): Promise<string> {
  // Implementation
}
```

2. **README Updates**
- Update feature lists when adding new functionality
- Include setup instructions for new dependencies
- Add usage examples for new components

3. **API Documentation**
- Document all service methods
- Include request/response examples
- Update interface definitions

### Component Documentation

```typescript
/**
 * StaffListComponent displays a list of staff members with filtering and actions
 * 
 * @example
 * ```html
 * <app-staff-list 
 *   [businessId]="currentBusinessId"
 *   [showActions]="canManageStaff"
 *   (staffSelected)="onStaffSelected($event)">
 * </app-staff-list>
 * ```
 */
@Component({
  selector: 'app-staff-list',
  // ...
})
export class StaffListComponent {
  /**
   * The business ID to filter staff members
   */
  @Input() businessId: string | null = null;
  
  /**
   * Whether to show action buttons for staff management
   */
  @Input() showActions: boolean = true;
  
  /**
   * Emitted when a staff member is selected
   */
  @Output() staffSelected = new EventEmitter<Staff>();
}
```

## 🎉 Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes for significant contributions
- GitHub contributor graphs
- Special mentions for major features

## 📞 Getting Help

- **Discord**: Join our development Discord server
- **GitHub Discussions**: For general questions and discussions
- **Issues**: For bug reports and feature requests
- **Email**: <EMAIL> for private inquiries

## 📄 License

By contributing to StaffManager, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to StaffManager! Your efforts help make staff management better for businesses everywhere. 🚀
