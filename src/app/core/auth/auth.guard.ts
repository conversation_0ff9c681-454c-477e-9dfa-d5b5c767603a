import { Injectable, inject } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(): Observable<boolean> {
    return this.authService.user$.pipe(
      take(1),
      map(user => {
        console.log('🔒 AuthGuard - Checking authentication:', {
          user: user,
          isAuthenticated: !!user,
          uid: user?.uid,
          email: user?.email
        });

        if (user) {
          console.log('✅ AuthGuard - User authenticated, allowing access');
          return true;
        } else {
          console.log('❌ AuthGuard - No user found, redirecting to login');
          this.router.navigate(['/auth/login']);
          return false;
        }
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(): Observable<boolean> {
    return this.authService.userProfile$.pipe(
      take(1),
      map(profile => {
        if (profile?.role === 'admin') {
          return true;
        } else {
          this.router.navigate(['/dashboard']);
          return false;
        }
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class ManagerGuard implements CanActivate {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(): Observable<boolean> {
    return this.authService.isAdminOrManager().pipe(
      take(1),
      map(isAuthorized => {
        if (isAuthorized) {
          return true;
        } else {
          this.router.navigate(['/dashboard']);
          return false;
        }
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class StaffEditGuard implements CanActivate {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(route: any): Observable<boolean> {
    const staffIdFromRoute = route.paramMap.get('id');

    return this.authService.userProfile$.pipe(
      take(1),
      map(profile => {
        console.log('🔒 StaffEditGuard - Checking access:', {
          staffIdFromRoute,
          userProfile: profile,
          userStaffId: profile?.staffId,
          userRole: profile?.role
        });

        if (!profile) {
          console.log('❌ No user profile found, redirecting to login');
          this.router.navigate(['/auth/login']);
          return false;
        }

        // Allow if user is admin/manager OR if they're editing their own profile
        const isAdminOrManager = profile.role === 'admin' || profile.role === 'manager';
        const isOwnProfile = profile.staffId === staffIdFromRoute;

        // Special case: if user doesn't have staffId yet but is trying to access a profile,
        // check if this might be their own profile by checking if they just created it
        const mightBeOwnProfile = !profile.staffId && staffIdFromRoute;

        console.log('🔒 Access check results:', {
          isAdminOrManager,
          isOwnProfile,
          mightBeOwnProfile,
          allowAccess: isAdminOrManager || isOwnProfile || mightBeOwnProfile
        });

        if (isAdminOrManager || isOwnProfile || mightBeOwnProfile) {
          return true;
        } else {
          console.log('❌ Access denied, redirecting to dashboard');
          this.router.navigate(['/dashboard']);
          return false;
        }
      })
    );
  }
}
