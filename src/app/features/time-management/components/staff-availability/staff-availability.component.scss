.availability-container {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;

  .availability-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 4px;

    h2 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      color: #333;

      mat-icon {
        color: #1976d2;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      button {
        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }

  .availability-filters {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;

    mat-form-field {
      min-width: 200px;
    }
  }

  .availability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    flex: 1;
    overflow-y: auto;

    .staff-availability-card {
      height: fit-content;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }

      mat-card-header {
        .staff-avatar {
          background: #1976d2;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        mat-card-title {
          font-size: 1.125rem;
          font-weight: 600;
        }

        mat-card-subtitle {
          color: #666;
          font-size: 0.875rem;
        }
      }

      .weekly-schedule {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 8px;
        margin: 16px 0;

        .day-availability {
          text-align: center;
          padding: 8px 4px;
          border-radius: 6px;
          font-size: 0.75rem;
          transition: all 0.2s ease;

          &.available {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
          }

          &.unavailable {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
          }

          .day-name {
            font-weight: 600;
            margin-bottom: 4px;
          }

          .day-hours {
            font-size: 0.7rem;
            line-height: 1.2;
          }

          .day-unavailable {
            font-size: 0.7rem;
            opacity: 0.8;
          }
        }
      }

      .time-off-summary {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #e0e0e0;

        h4 {
          margin: 0 0 8px 0;
          font-size: 0.875rem;
          font-weight: 600;
          color: #666;
        }

        mat-chip-set {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;

          mat-chip {
            font-size: 0.75rem;
            height: 24px;
          }
        }
      }

      mat-card-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        button {
          flex: 1;
          min-width: 80px;

          mat-icon {
            margin-right: 4px;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    gap: 16px;

    p {
      color: #666;
      margin: 0;
    }
  }

  .ai-insights-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 8px 24px rgba(0,0,0,0.3);

    mat-card-header {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #1976d2;
      }
    }

    mat-card-content {
      ::ng-deep {
        h3 {
          color: #1976d2;
          margin-top: 0;
        }

        ul {
          padding-left: 20px;

          li {
            margin-bottom: 8px;
          }
        }

        strong {
          color: #333;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .availability-container {
    .availability-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .availability-filters {
      flex-direction: column;

      mat-form-field {
        width: 100%;
        min-width: auto;
      }
    }

    .availability-grid {
      grid-template-columns: 1fr;

      .staff-availability-card {
        .weekly-schedule {
          .day-availability {
            padding: 6px 2px;
            font-size: 0.7rem;

            .day-hours {
              font-size: 0.65rem;
            }
          }
        }

        mat-card-actions {
          button {
            font-size: 0.875rem;
          }
        }
      }
    }

    .ai-insights-panel {
      width: 95%;
      max-height: 90vh;
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .availability-container {
    .availability-header h2 {
      color: #e0e0e0;
    }

    .availability-grid {
      .staff-availability-card {
        .weekly-schedule {
          .day-availability {
            &.available {
              background: #1b5e20;
              border-color: #4caf50;
              color: #a5d6a7;
            }

            &.unavailable {
              background: #b71c1c;
              border-color: #f44336;
              color: #ffcdd2;
            }
          }
        }

        .time-off-summary {
          border-top-color: #424242;

          h4 {
            color: #bbb;
          }
        }
      }
    }
  }
}
