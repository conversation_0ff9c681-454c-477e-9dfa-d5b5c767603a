#!/usr/bin/env node

/**
 * StaffManager PrimeNG Migration Script
 * Automates common fixes for Angular Material to PrimeNG migration
 */

const fs = require('fs');
const path = require('path');

class PrimeNGMigrationScript {
  constructor() {
    this.srcPath = './src/app';
    this.fixes = {
      importSyntax: 0,
      templateSyntax: 0,
      serviceImports: 0,
      componentReplacements: 0
    };
  }

  // Fix import syntax errors (missing commas)
  fixImportSyntax(content) {
    let fixed = content;
    let changes = 0;

    // Fix missing commas in imports array
    const importArrayRegex = /imports:\s*\[\s*([\s\S]*?)\s*\]/g;
    fixed = fixed.replace(importArrayRegex, (match, importsContent) => {
      // Split by lines and fix missing commas
      const lines = importsContent.split('\n').map(line => line.trim()).filter(line => line);
      const fixedLines = lines.map((line, index) => {
        // Skip if line is empty or already has comma or is last line
        if (!line || line.endsWith(',') || index === lines.length - 1) {
          return line;
        }
        // Add comma if missing
        if (!line.endsWith(',') && !line.endsWith(']')) {
          changes++;
          return line + ',';
        }
        return line;
      });

      return `imports: [\n    ${fixedLines.join('\n    ')}\n  ]`;
    });

    this.fixes.importSyntax += changes;
    return fixed;
  }

  // Fix service imports (MessageService, MatDialog, etc.)
  fixServiceImports(content) {
    let fixed = content;
    let changes = 0;

    // Add MessageService import if missing but used
    if (fixed.includes('MessageService') && !fixed.includes("import { MessageService }")) {
      const importSection = fixed.match(/(import.*from.*primeng.*;\n)/);
      if (importSection) {
        fixed = fixed.replace(
          importSection[0],
          `${importSection[0]}import { MessageService } from 'primeng/api';\n`
        );
      } else {
        // Add at the beginning of imports
        fixed = fixed.replace(
          /(import.*from.*@angular.*;\n)/,
          `$1import { MessageService } from 'primeng/api';\n`
        );
      }
      changes++;
    }

    // Add DialogService import if missing but used
    if (fixed.includes('DialogService') && !fixed.includes("import { DialogService }")) {
      const importSection = fixed.match(/(import.*from.*primeng.*;\n)/);
      if (importSection) {
        fixed = fixed.replace(
          importSection[0],
          `${importSection[0]}import { DialogService } from 'primeng/dynamicdialog';\n`
        );
      } else {
        fixed = fixed.replace(
          /(import.*from.*@angular.*;\n)/,
          `$1import { DialogService } from 'primeng/dynamicdialog';\n`
        );
      }
      changes++;
    }

    // Replace Angular Material MessageService with PrimeNG MessageService
    fixed = fixed.replace(
      "import { MessageService } from '@angular/material/snack-bar';",
      "import { MessageService } from 'primeng/api';"
    );

    // Replace MatDialog with PrimeNG DialogService
    fixed = fixed.replace(
      /import { MatDialog[^}]*} from '@angular\/material\/dialog';/g,
      "import { DialogService } from 'primeng/dynamicdialog';"
    );

    // Fix service usage patterns
    fixed = fixed.replace(/MatDialog/g, 'DialogService');
    fixed = fixed.replace(/private snackBar:/g, 'private messageService:');
    fixed = fixed.replace(/private snackBar = inject/g, 'private messageService = inject');

    // Fix MessageService.add calls (PrimeNG uses different signature)
    fixed = fixed.replace(
      /this\.messageService\.add\('([^']*)',\s*'[^']*',\s*\{[^}]*\}/g,
      "this.messageService.add({ severity: 'info', summary: '$1' })"
    );

    // Fix remaining MessageService calls with 3 parameters
    fixed = fixed.replace(
      /this\.messageService\.add\(([^,]+),\s*'[^']*',\s*\{[^}]*\}/g,
      "this.messageService.add({ severity: 'info', summary: $1 })"
    );

    // Fix DialogService method calls (PrimeNG uses different API)
    fixed = fixed.replace(/\.afterClosed\(\)/g, '.onClose');
    fixed = fixed.replace(/maxWidth:/g, 'width:');

    // Remove duplicate DialogService imports
    const dialogImportRegex = /import\s*{\s*DialogService[^}]*}\s*from\s*'primeng\/dynamicdialog';\s*/g;
    const dialogImports = fixed.match(dialogImportRegex);
    if (dialogImports && dialogImports.length > 1) {
      // Keep only the first import
      fixed = fixed.replace(dialogImportRegex, '');
      fixed = fixed.replace(
        /(import.*from.*@angular.*;\n)/,
        `$1import { DialogService } from 'primeng/dynamicdialog';\n`
      );
      changes++;
    }

    this.fixes.serviceImports += changes;
    return fixed;
  }

  // Fix template syntax errors
  fixTemplateSyntax(content) {
    let fixed = content;
    let changes = 0;

    // Fix broken p-dropdown with option tags (PrimeNG doesn't use option tags)
    fixed = fixed.replace(
      /<p-dropdown([^>]*optionLabel="label" optionValue="value")>\s*<option[^>]*value="([^"]*)"[^>]*>([^<]*)<\/option>/g,
      '<p-dropdown$1>'
    );

    // Remove all remaining option tags inside p-dropdown
    fixed = fixed.replace(
      /<p-dropdown([^>]*)>[\s\S]*?(<option[\s\S]*?<\/option>[\s\S]*?)*<\/p-dropdown>/g,
      (match, attrs) => {
        changes++;
        return `<p-dropdown${attrs}></p-dropdown>`;
      }
    );

    // Fix broken ng-template structures with extra closing tags
    fixed = fixed.replace(
      /(<ng-template[^>]*>[\s\S]*?)<\/ng-template>\s*(<[^>]*>[\s\S]*?)<\/ng-template>/g,
      (match, templateStart, content) => {
        changes++;
        return `${templateStart}${content}</ng-template>`;
      }
    );

    // Fix p-card with broken ng-template structure
    fixed = fixed.replace(
      /(<ng-template pTemplate="[^"]*">[\s\S]*?)<\/ng-template>\s*(<\/p-card>)/g,
      (match, templateContent, cardClose) => {
        changes++;
        return `${templateContent}</ng-template>${cardClose}`;
      }
    );

    // Fix broken p-button closing tags
    fixed = fixed.replace(
      /<p-button([^>]*)>[\s\S]*?<\/p-button>\s*<\/p-button>/g,
      (match, attrs) => {
        changes++;
        return match.replace(/<\/p-button>\s*<\/p-button>/, '</p-button>');
      }
    );

    // Fix broken p-menu closing tags
    fixed = fixed.replace(
      /<p-menu([^>]*)>[\s\S]*?<\/p-menu>\s*<\/p-menu>/g,
      (match, attrs) => {
        changes++;
        return match.replace(/<\/p-menu>\s*<\/p-menu>/, '</p-menu>');
      }
    );

    // Fix broken ng-container closing tags
    fixed = fixed.replace(
      /<ng-container([^>]*)>[\s\S]*?<\/ng-container>\s*<\/ng-container>/g,
      (match, attrs) => {
        changes++;
        return match.replace(/<\/ng-container>\s*<\/ng-container>/, '</ng-container>');
      }
    );

    // Fix p-button properties
    fixed = fixed.replace(/\[outlined\]="true"/g, 'outlined="true"');
    fixed = fixed.replace(/\[text\]="true"/g, 'text="true"');
    fixed = fixed.replace(/color="primary"/g, 'severity="primary"');
    fixed = fixed.replace(/color="accent"/g, 'severity="secondary"');

    // Fix button p-button syntax (remove p-button attribute)
    fixed = fixed.replace(/<button p-button/g, '<p-button');
    fixed = fixed.replace(/<\/button>/g, '</p-button>');

    // Fix p-chip-set (doesn't exist in PrimeNG)
    fixed = fixed.replace(/<p-chip-set>/g, '<div class="chip-container">');
    fixed = fixed.replace(/<\/p-chip-set>/g, '</div>');

    // Fix malformed p-menu tags
    fixed = fixed.replace(/<\/p-menu<\/ng-template>/g, '</p-menu></ng-template>');

    // Fix broken p-card structures with content outside ng-template
    fixed = fixed.replace(
      /(<ng-template[^>]*>[\s\S]*?)<\/ng-template>\s*(<\/p-card>)/g,
      (match, templateContent, cardClose) => {
        changes++;
        return `${templateContent}</ng-template>${cardClose}`;
      }
    );

    // Fix broken p-button structures with extra closing tags
    fixed = fixed.replace(
      /(<p-button[^>]*>[\s\S]*?)<\/p-button>\s*<\/p-button>/g,
      (match, buttonContent) => {
        changes++;
        return `${buttonContent}</p-button>`;
      }
    );

    // Fix broken div structures with extra closing tags
    fixed = fixed.replace(
      /(<div[^>]*>[\s\S]*?)<\/div>\s*<\/div>/g,
      (match, divContent) => {
        changes++;
        return `${divContent}</div>`;
      }
    );

    // Fix broken p-toolbar structures
    fixed = fixed.replace(
      /(<p-toolbar[^>]*>[\s\S]*?)<\/p-toolbar>\s*<\/p-toolbar>/g,
      (match, toolbarContent) => {
        changes++;
        return `${toolbarContent}</p-toolbar>`;
      }
    );

    // Fix broken p-tabPanel structures
    fixed = fixed.replace(
      /(<p-tabPanel[^>]*>[\s\S]*?)<\/p-tabPanel>\s*<\/p-tabPanel>/g,
      (match, tabContent) => {
        changes++;
        return `${tabContent}</p-tabPanel>`;
      }
    );

    this.fixes.templateSyntax += changes;
    return fixed;
  }

  // Replace Angular Material components with PrimeNG equivalents
  replaceComponents(content) {
    let fixed = content;
    let changes = 0;

    // Fix Angular Material table to PrimeNG table conversion
    if (fixed.includes('mat-table')) {
      // Replace mat-table structure with p-table
      fixed = fixed.replace(
        /<table mat-table \[dataSource\]="([^"]*)"([^>]*)>/g,
        '<p-table [value]="$1"$2>'
      );

      // Convert ng-container matColumnDef to proper p-table structure
      fixed = fixed.replace(
        /<ng-container matColumnDef="([^"]*)">/g,
        '<!-- Column: $1 -->'
      );

      // Convert header and cell definitions
      fixed = fixed.replace(
        /<th mat-header-cell \*matHeaderCellDef>/g,
        '<ng-template pTemplate="header"><tr><th>'
      );

      fixed = fixed.replace(
        /<td mat-cell \*matCellDef="let ([^"]*)">/g,
        '<ng-template pTemplate="body" let-$1><tr><td>'
      );

      changes += 5;
    }

    // Fix component replacements
    const replacements = {
      'mat-card': 'p-card',
      'mat-button': 'p-button',
      'mat-menu': 'p-menu',
      'mat-header-cell': 'th',
      'mat-cell': 'td',
      'mat-header-row': 'tr',
      'mat-row': 'tr'
    };

    Object.entries(replacements).forEach(([material, primeng]) => {
      const regex = new RegExp(`<${material}`, 'g');
      const closeRegex = new RegExp(`</${material}>`, 'g');
      const matches = (fixed.match(regex) || []).length;
      if (matches > 0) {
        fixed = fixed.replace(regex, `<${primeng}`);
        fixed = fixed.replace(closeRegex, `</${primeng}>`);
        changes += matches * 2;
      }
    });

    // Remove Angular Material imports and add PrimeNG equivalents
    const materialToPrimeNG = {
      'MatButtonModule': 'ButtonModule',
      'MatCardModule': 'CardModule',
      'MatTableModule': 'TableModule',
      'MatMenuModule': 'MenuModule',
      'MatDialogModule': 'DialogModule',
      'MatSnackBarModule': 'ToastModule',
      'MatInputModule': 'InputTextModule',
      'MatSelectModule': 'DropdownModule',
      'MatCheckboxModule': 'CheckboxModule',
      'MatRadioModule': 'RadioButtonModule'
    };

    Object.entries(materialToPrimeNG).forEach(([matModule, primeModule]) => {
      if (fixed.includes(matModule)) {
        fixed = fixed.replace(new RegExp(matModule, 'g'), primeModule);
        changes++;
      }
    });

    // Add missing PrimeNG imports if components are used
    const primeNGComponents = {
      'p-button': 'ButtonModule',
      'p-card': 'CardModule',
      'p-table': 'TableModule',
      'p-dropdown': 'DropdownModule',
      'p-menu': 'MenuModule',
      'p-dialog': 'DialogModule',
      'p-toast': 'ToastModule',
      'p-chip': 'ChipModule',
      'p-badge': 'BadgeModule',
      'p-tooltip': 'TooltipModule'
    };

    Object.entries(primeNGComponents).forEach(([component, module]) => {
      if (fixed.includes(component) && !fixed.includes(module)) {
        // Add import if missing
        const importMatch = fixed.match(/imports:\s*\[([\s\S]*?)\]/);
        if (importMatch) {
          const imports = importMatch[1];
          if (!imports.includes(module)) {
            fixed = fixed.replace(
              /imports:\s*\[\s*/,
              `imports: [\n    ${module},\n    `
            );
            // Add corresponding import statement
            const importStatement = `import { ${module} } from 'primeng/${module.toLowerCase().replace('module', '')}';`;
            if (!fixed.includes(importStatement)) {
              fixed = fixed.replace(
                /(import.*from.*primeng.*;\n)/,
                `$1${importStatement}\n`
              );
            }
            changes++;
          }
        }
      }
    });

    this.fixes.componentReplacements += changes;
    return fixed;
  }

  // Fix critical import syntax errors that prevent build
  fixCriticalImportErrors(content) {
    let fixed = content;
    let changes = 0;

    // Fix the most common import array syntax errors
    // Pattern: imports: [ Module1, Module2 Module3 ] (missing comma)
    fixed = fixed.replace(
      /imports:\s*\[\s*([\s\S]*?)\s*\]/g,
      (match, importsContent) => {
        // Split by lines and ensure proper comma separation
        const lines = importsContent.split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('//'));

        const fixedLines = [];
        for (let i = 0; i < lines.length; i++) {
          let line = lines[i];

          // Remove existing comma if present
          line = line.replace(/,$/, '');

          // Add comma to all lines except the last one
          if (i < lines.length - 1) {
            line += ',';
          }

          fixedLines.push(line);
        }

        changes++;
        return `imports: [\n    ${fixedLines.join('\n    ')}\n  ]`;
      }
    );

    // Fix standalone module declarations without proper array structure
    fixed = fixed.replace(
      /imports:\s*\[\s*([A-Z][A-Za-z]*Module)\s*([A-Z][A-Za-z]*Module)/g,
      'imports: [\n    $1,\n    $2'
    );

    this.fixes.importSyntax += changes;
    return fixed;
  }

  // Fix missing imports for PrimeNG modules
  fixMissingImports(content) {
    let fixed = content;
    let changes = 0;

    // Add missing ButtonModule import if not present
    if (fixed.includes('ButtonModule') && !fixed.includes("import { ButtonModule }")) {
      fixed = fixed.replace(
        /(import.*from.*@angular.*;\n)/,
        `$1import { ButtonModule } from 'primeng/button';\n`
      );
      changes++;
    }

    // Add missing ToastModule import if MessageService is used
    if (fixed.includes('MessageService') && !fixed.includes('ToastModule')) {
      const importMatch = fixed.match(/imports:\s*\[([\s\S]*?)\]/);
      if (importMatch && !importMatch[1].includes('ToastModule')) {
        fixed = fixed.replace(
          /imports:\s*\[\s*/,
          'imports: [\n    ToastModule,\n    '
        );
        // Add import statement
        fixed = fixed.replace(
          /(import.*from.*primeng.*;\n)/,
          `$1import { ToastModule } from 'primeng/toast';\n`
        );
        changes++;
      }
    }

    this.fixes.componentReplacements += changes;
    return fixed;
  }

  // Process a single file
  processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;

      // Apply fixes in order of priority
      fixed = this.fixCriticalImportErrors(fixed);
      fixed = this.fixImportSyntax(fixed);
      fixed = this.fixServiceImports(fixed);
      fixed = this.fixTemplateSyntax(fixed);
      fixed = this.replaceComponents(fixed);
      fixed = this.fixMissingImports(fixed);

      // Only write if changes were made
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`✅ Fixed: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
      return false;
    }
  }

  // Recursively find all TypeScript and HTML files
  findFiles(dir, extensions = ['.ts', '.html']) {
    const files = [];

    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.findFiles(fullPath, extensions));
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }

    return files;
  }

  // Run the migration
  run() {
    console.log('🚀 Starting PrimeNG Migration Script...\n');

    const files = this.findFiles(this.srcPath);
    console.log(`📁 Found ${files.length} files to process\n`);

    let processedFiles = 0;
    let modifiedFiles = 0;

    files.forEach(file => {
      processedFiles++;
      if (this.processFile(file)) {
        modifiedFiles++;
      }
    });

    console.log('\n📊 Migration Summary:');
    console.log(`   Files processed: ${processedFiles}`);
    console.log(`   Files modified: ${modifiedFiles}`);
    console.log(`   Import syntax fixes: ${this.fixes.importSyntax}`);
    console.log(`   Template syntax fixes: ${this.fixes.templateSyntax}`);
    console.log(`   Service import fixes: ${this.fixes.serviceImports}`);
    console.log(`   Component replacements: ${this.fixes.componentReplacements}`);
    console.log('\n✨ Migration complete!');
  }
}

// Run the script
if (require.main === module) {
  const migrator = new PrimeNGMigrationScript();
  migrator.run();
}

module.exports = PrimeNGMigrationScript;
