.widget-builder-dialog {
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;

  h2[mat-dialog-title] {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    padding: 24px 24px 16px;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--sm-primary-main, #1976d2);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);

    mat-icon {
      font-size: 1.8rem;
      width: 1.8rem;
      height: 1.8rem;
    }
  }
}

.dialog-content {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.widget-form {
  padding: 24px;
  flex: 1;

  .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    }

    .mat-mdc-tab-body-wrapper {
      padding-top: 24px;
    }
  }
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 300px;
  padding: 16px 0;
}

.full-width {
  width: 100%;
}

.half-width {
  width: calc(50% - 8px);
  display: inline-block;
  margin-right: 16px;

  &:last-child {
    margin-right: 0;
  }
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 16px 0;

  mat-checkbox {
    .mat-mdc-checkbox-label {
      font-size: 0.95rem;
      color: var(--sm-text-primary, #333);
    }
  }
}

.slider-group {
  margin: 16px 0;

  label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--sm-text-secondary, #666);
    margin-bottom: 8px;
  }

  mat-slider {
    width: 100%;
  }
}

.preview-card {
  margin: 0 24px 24px;
  border: 1px solid rgba(25, 118, 210, 0.2);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.1);

  mat-card-header {
    background: rgba(25, 118, 210, 0.05);
    padding: 16px;
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);

    mat-card-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--sm-primary-main, #1976d2);
      margin: 0;
    }
  }

  mat-card-content {
    padding: 16px;
  }
}

.widget-preview {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  min-height: 120px;
  background: #fff;
  transition: all 0.3s ease;

  h3 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: inherit;
  }

  .preview-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80px;
  }

  .number-preview {
    text-align: center;

    .number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--sm-primary-main, #1976d2);
      line-height: 1;
      margin-bottom: 8px;
    }

    .label {
      font-size: 0.9rem;
      color: var(--sm-text-secondary, #666);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .chart-preview {
    text-align: center;
    font-size: 1.2rem;
    color: var(--sm-text-secondary, #666);
    padding: 32px;
    background: rgba(25, 118, 210, 0.05);
    border-radius: 8px;
    border: 2px dashed rgba(25, 118, 210, 0.2);
  }

  .default-preview {
    text-align: center;
    font-size: 1.1rem;
    color: var(--sm-text-secondary, #666);
    padding: 24px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 6px;
    text-transform: capitalize;
  }
}

.dialog-actions {
  padding: 16px 24px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(0, 0, 0, 0.02);

  button {
    margin-left: 8px;

    &:first-child {
      margin-left: 0;
    }
  }
}

// Form field customizations
.mat-mdc-form-field {
  .mat-mdc-form-field-label {
    font-weight: 500;
  }

  &.mat-form-field-appearance-outline {
    .mat-mdc-form-field-outline {
      border-radius: 8px;
    }

    &.mat-focused .mat-mdc-form-field-outline-thick {
      border-color: var(--sm-primary-main, #1976d2);
    }
  }
}

// Tab customizations
.mat-mdc-tab-group {
  .mat-mdc-tab {
    &.mat-mdc-tab-active {
      .mat-mdc-tab-label {
        color: var(--sm-primary-main, #1976d2);
        font-weight: 600;
      }
    }
  }

  .mat-mdc-tab-body-content {
    overflow: visible;
  }
}

// Responsive design
@media (max-width: 768px) {
  .widget-builder-dialog {
    max-width: 95vw;
    max-height: 95vh;
  }

  .dialog-content {
    max-height: 75vh;
  }

  .half-width {
    width: 100%;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .tab-content {
    min-height: 250px;
  }

  .preview-card {
    margin: 0 16px 16px;
  }

  .widget-form {
    padding: 16px;
  }
}

// Dark theme support
.dark-theme {
  .widget-builder-dialog {
    h2[mat-dialog-title] {
      color: var(--sm-primary-light, #42a5f5);
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }
  }

  .dialog-content {
    background: var(--sm-background-paper);
  }

  .preview-card {
    border-color: rgba(66, 165, 245, 0.3);
    box-shadow: 0 4px 8px rgba(66, 165, 245, 0.15);

    mat-card-header {
      background: rgba(66, 165, 245, 0.1);
      border-bottom-color: rgba(66, 165, 245, 0.2);
    }
  }

  .widget-preview {
    border-color: rgba(255, 255, 255, 0.2);
    background: var(--sm-background-paper);

    .chart-preview {
      background: rgba(66, 165, 245, 0.1);
      border-color: rgba(66, 165, 245, 0.3);
    }

    .default-preview {
      background: rgba(255, 255, 255, 0.05);
    }
  }

  .dialog-actions {
    border-top-color: rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
  }
}

// Animation enhancements
.widget-preview {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Accessibility improvements
.mat-mdc-form-field:focus-within {
  .mat-mdc-form-field-outline-thick {
    border-width: 2px;
  }
}

button:focus {
  outline: 2px solid var(--sm-primary-main, #1976d2);
  outline-offset: 2px;
}

// Loading state
.widget-form.loading {
  opacity: 0.7;
  pointer-events: none;
}
