import { trigger, state, style, transition, animate, query, stagger, group, keyframes } from '@angular/animations';

/**
 * StaffManager Animation Library
 * Modern, smooth animations for enhanced user experience
 */

// Page transition animations
export const slideInAnimation = trigger('slideIn', [
  transition(':enter', [
    style({ transform: 'translateX(100%)', opacity: 0 }),
    animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      style({ transform: 'translateX(0)', opacity: 1 })
    )
  ])
]);

export const fadeInAnimation = trigger('fadeIn', [
  transition(':enter', [
    style({ opacity: 0, transform: 'translateY(20px)' }),
    animate('400ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      style({ opacity: 1, transform: 'translateY(0)' })
    )
  ])
]);

// Stagger animations for lists
export const staggerAnimation = trigger('stagger', [
  transition('* => *', [
    query(':enter', [
      style({ opacity: 0, transform: 'translateY(30px)' }),
      stagger(100, [
        animate('400ms cubic-bezier(0.4, 0.0, 0.2, 1)',
          style({ opacity: 1, transform: 'translateY(0)' })
        )
      ])
    ], { optional: true })
  ])
]);

// Card hover animations
export const cardHoverAnimation = trigger('cardHover', [
  state('default', style({ transform: 'scale(1)', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' })),
  state('hovered', style({ transform: 'scale(1.02)', boxShadow: '0 8px 24px rgba(0,0,0,0.15)' })),
  transition('default <=> hovered', animate('200ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
]);

// Button press animation
export const buttonPressAnimation = trigger('buttonPress', [
  state('normal', style({ transform: 'scale(1)' })),
  state('pressed', style({ transform: 'scale(0.95)' })),
  transition('normal <=> pressed', animate('100ms ease-in-out'))
]);

// Sidebar toggle animation
export const sidebarToggleAnimation = trigger('sidebarToggle', [
  state('expanded', style({ width: '240px' })),
  state('collapsed', style({ width: '64px' })),
  transition('expanded <=> collapsed', animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
]);

// Modal/Dialog animations
export const modalAnimation = trigger('modal', [
  transition(':enter', [
    style({ opacity: 0, transform: 'scale(0.8)' }),
    animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      style({ opacity: 1, transform: 'scale(1)' })
    )
  ]),
  transition(':leave', [
    animate('200ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      style({ opacity: 0, transform: 'scale(0.8)' })
    )
  ])
]);

// Loading skeleton animation
export const skeletonAnimation = trigger('skeleton', [
  state('loading', style({ opacity: 0.6 })),
  state('loaded', style({ opacity: 1 })),
  transition('loading <=> loaded', animate('300ms ease-in-out'))
]);

// Notification slide animation
export const notificationSlideAnimation = trigger('notificationSlide', [
  transition(':enter', [
    style({ transform: 'translateX(100%)', opacity: 0 }),
    animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      style({ transform: 'translateX(0)', opacity: 1 })
    )
  ]),
  transition(':leave', [
    animate('200ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      style({ transform: 'translateX(100%)', opacity: 0 })
    )
  ])
]);

// Widget animations
export const widgetEnterAnimation = trigger('widgetEnter', [
  transition(':enter', [
    style({ opacity: 0, transform: 'scale(0.9) translateY(20px)' }),
    animate('400ms cubic-bezier(0.4, 0.0, 0.2, 1)',
      style({ opacity: 1, transform: 'scale(1) translateY(0)' })
    )
  ])
]);

// Pulse animation for notifications
export const pulseAnimation = trigger('pulse', [
  state('normal', style({ transform: 'scale(1)' })),
  state('pulsed', style({ transform: 'scale(1.1)' })),
  transition('normal <=> pulsed', animate('600ms ease-in-out'))
]);

// Smooth height animation
export const expandCollapseAnimation = trigger('expandCollapse', [
  state('collapsed', style({ height: '0px', overflow: 'hidden' })),
  state('expanded', style({ height: '*', overflow: 'visible' })),
  transition('collapsed <=> expanded', animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
]);

// Route transition animations
export const routeTransitionAnimation = trigger('routeTransition', [
  transition('* <=> *', [
    query(':enter, :leave', [
      style({
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%'
      })
    ], { optional: true }),
    query(':enter', [
      style({ opacity: 0, transform: 'translateX(100px)' })
    ], { optional: true }),
    query(':leave', [
      animate('200ms ease-in', style({ opacity: 0, transform: 'translateX(-100px)' }))
    ], { optional: true }),
    query(':enter', [
      animate('300ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
    ], { optional: true })
  ])
]);

// Floating action button animation
export const fabAnimation = trigger('fab', [
  state('hidden', style({ transform: 'scale(0)', opacity: 0 })),
  state('visible', style({ transform: 'scale(1)', opacity: 1 })),
  transition('hidden <=> visible', animate('200ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
]);

// Count up animation for numbers
export const countUpAnimation = trigger('countUp', [
  transition(':increment', [
    animate('500ms ease-out', keyframes([
      style({ transform: 'scale(1)', offset: 0 }),
      style({ transform: 'scale(1.2)', offset: 0.5 }),
      style({ transform: 'scale(1)', offset: 1 })
    ]))
  ])
]);

// Shake animation for errors
export const shakeAnimation = trigger('shake', [
  transition('* => error', [
    animate('500ms ease-in-out', keyframes([
      style({ transform: 'translateX(0)', offset: 0 }),
      style({ transform: 'translateX(-10px)', offset: 0.1 }),
      style({ transform: 'translateX(10px)', offset: 0.2 }),
      style({ transform: 'translateX(-10px)', offset: 0.3 }),
      style({ transform: 'translateX(10px)', offset: 0.4 }),
      style({ transform: 'translateX(-10px)', offset: 0.5 }),
      style({ transform: 'translateX(10px)', offset: 0.6 }),
      style({ transform: 'translateX(-10px)', offset: 0.7 }),
      style({ transform: 'translateX(10px)', offset: 0.8 }),
      style({ transform: 'translateX(0)', offset: 1 })
    ]))
  ])
]);

// Bounce animation for success states
export const bounceAnimation = trigger('bounce', [
  transition('* => success', [
    animate('600ms ease-out', keyframes([
      style({ transform: 'scale(1)', offset: 0 }),
      style({ transform: 'scale(1.3)', offset: 0.3 }),
      style({ transform: 'scale(0.9)', offset: 0.6 }),
      style({ transform: 'scale(1.1)', offset: 0.8 }),
      style({ transform: 'scale(1)', offset: 1 })
    ]))
  ])
]);
