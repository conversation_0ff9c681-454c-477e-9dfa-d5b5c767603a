import { Routes } from '@angular/router';
import { ManagerGuard } from '../../core/auth/auth.guard';

export const goalsRoutes: Routes = [
  {
    path: '',
    loadComponent: () => import('./goals.component').then(m => m.GoalsComponent)
  },
  {
    path: 'create',
    loadComponent: () => import('./components/goal-dialog.component').then(m => m.GoalDialogComponent),
    canActivate: [ManagerGuard]
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('./components/goal-dialog.component').then(m => m.GoalDialogComponent),
    canActivate: [ManagerGuard]
  },
  {
    path: 'details/:id',
    loadComponent: () => import('./components/goal-details.component').then(m => m.GoalDetailsComponent)
  }
];
