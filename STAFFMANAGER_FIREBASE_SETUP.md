# StaffManager Firebase Integration Setup Guide

This guide provides comprehensive instructions for setting up Firebase integration with the StaffManager Angular v19 application, including authentication, Firestore database, and FullCalendar scheduling.

## 🚀 Features Implemented

### Staff (User Profile) Module
- ✅ Firebase Authentication with email/password and Google sign-in
- ✅ Comprehensive staff profiles with goals, tasks, and time management
- ✅ Role-based permissions (admin/manager/staff)
- ✅ Real-time data synchronization with Firestore
- ✅ Profile editing and management
- ✅ Goals tracking with progress indicators
- ✅ Task assignment and management
- ✅ Time-off requests and approvals
- ✅ Work schedule integration

### Calendar/Scheduling Module
- ✅ FullCalendar integration with multiple views (month/week/day/agenda)
- ✅ Drag-and-drop staff assignment to shifts
- ✅ Event creation and management (shifts, meetings, training, time-off)
- ✅ Staff availability tracking
- ✅ Shift swapping functionality
- ✅ Real-time calendar updates
- ✅ Color-coded event types
- ✅ Mobile-responsive design

## 📋 Prerequisites

- Node.js 18+ and npm
- Angular CLI 19+
- Firebase account
- Git

## 🔧 Firebase Project Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `staffmanager-[your-suffix]`
4. Enable Google Analytics (optional)
5. Create project

### 2. Enable Authentication

1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Enable the following providers:
   - **Email/Password**: Enable
   - **Google**: Enable (configure OAuth consent screen)

### 3. Create Firestore Database

1. Go to **Firestore Database** > **Create database**
2. Choose **Start in test mode** (we'll configure security rules later)
3. Select your preferred location
4. Create database

### 4. Configure Security Rules

Replace the default Firestore rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager']);
    }
    
    // Staff data - role-based access
    match /staff/{staffId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager']);
    }
    
    // Goals - staff can read their own, managers can read/write all
    match /goals/{goalId} {
      allow read: if request.auth != null && 
        (request.auth.uid in resource.data.assignedTo || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager']);
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }
    
    // Tasks - similar to goals
    match /tasks/{taskId} {
      allow read: if request.auth != null && 
        (request.auth.uid in resource.data.assignedTo || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager']);
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }
    
    // Calendar events
    match /calendarEvents/{eventId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager'] ||
         request.auth.uid in resource.data.assignedStaff);
    }
    
    // Time entries - staff can create their own
    match /timeEntries/{entryId} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.staffId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager']);
      allow create: if request.auth != null && request.auth.uid == request.resource.data.staffId;
      allow update: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }
    
    // Time off requests
    match /timeOffRequests/{requestId} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.staffId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager']);
      allow create: if request.auth != null && request.auth.uid == request.resource.data.staffId;
      allow update: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'manager'];
    }
  }
}
```

### 5. Get Firebase Configuration

1. Go to **Project Settings** > **General**
2. Scroll to "Your apps" section
3. Click "Add app" > Web app icon
4. Register app name: `staffmanager-web`
5. Copy the Firebase configuration object

## 🛠️ Application Setup

### 1. Update Environment Files

Replace the placeholder values in `src/environments/environment.ts` and `src/environments/environment.prod.ts`:

```typescript
export const environment = {
  production: false, // true for prod
  firebase: {
    apiKey: "your-actual-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789012",
    appId: "1:123456789012:web:abcdef123456"
  }
};
```

### 2. Install Dependencies

The required dependencies are already installed:
- `@angular/fire` - Firebase integration
- `@fullcalendar/angular` - Calendar component
- `firebase` - Firebase SDK

### 3. Run the Application

```bash
npm start
```

The app will be available at `http://localhost:4200` (or next available port).

## 📊 Database Schema

### Collections Structure

```
/users/{userId}
  - uid: string
  - email: string
  - displayName: string
  - role: 'admin' | 'manager' | 'staff'
  - staffId?: string
  - businessIds: string[]
  - primaryBusinessId: string
  - createdAt: Date
  - lastLoginAt: Date

/staff/{staffId}
  - employeeId: string
  - firstName: string
  - lastName: string
  - email: string
  - position: string
  - department: string
  - status: 'active' | 'inactive' | 'on-leave'
  - businessIds: string[]
  - [... other staff fields]

/goals/{goalId}
  - title: string
  - description: string
  - type: 'individual' | 'team' | 'company'
  - category: 'sales' | 'performance' | 'training' | 'attendance' | 'custom'
  - assignedTo: string[]
  - targetDate: Date
  - progress: number (0-100)
  - status: 'not-started' | 'in-progress' | 'completed' | 'overdue'

/tasks/{taskId}
  - title: string
  - description: string
  - assignedTo: string[]
  - assignedBy: string
  - dueDate: Date
  - status: 'pending' | 'in-progress' | 'completed'
  - priority: 'low' | 'medium' | 'high' | 'urgent'

/calendarEvents/{eventId}
  - title: string
  - start: Date
  - end: Date
  - type: 'shift' | 'meeting' | 'training' | 'time-off'
  - assignedStaff: string[]
  - businessId: string
  - status: 'scheduled' | 'confirmed' | 'completed'

/timeEntries/{entryId}
  - staffId: string
  - type: 'clock-in' | 'clock-out' | 'break-start' | 'break-end'
  - timestamp: Date
  - businessId: string

/timeOffRequests/{requestId}
  - staffId: string
  - type: 'vacation' | 'sick' | 'personal'
  - startDate: Date
  - endDate: Date
  - status: 'pending' | 'approved' | 'denied'
  - reason: string
```

## 🔐 Authentication Flow

1. **Registration**: Users can register with email/password or Google
2. **Role Assignment**: Default role is 'staff', admins can promote users
3. **Profile Creation**: User profiles are automatically created in Firestore
4. **Staff Linking**: Admin can link user accounts to staff records

## 🎯 Usage Guide

### For Administrators
1. Register/login to the system
2. Create staff profiles in the Staff section
3. Assign goals and tasks to staff members
4. Manage work schedules in the Calendar
5. Approve time-off requests

### For Managers
1. View and edit staff profiles
2. Create and assign tasks
3. Manage team schedules
4. Review time entries and requests

### For Staff
1. View personal profile and goals
2. Track task progress
3. View personal schedule
4. Submit time-off requests
5. Clock in/out (time tracking)

## 🚨 Important Notes

1. **Security**: Always use the provided Firestore security rules in production
2. **Environment Variables**: Never commit actual Firebase config to version control
3. **Permissions**: Test role-based access thoroughly before deployment
4. **Data Migration**: Use the provided services to migrate existing data
5. **Backup**: Regularly backup your Firestore data

## 🔧 Customization

### Adding New Event Types
1. Update `CalendarEvent` interface in `calendar.model.ts`
2. Add color mapping in `CalendarService.getEventTypeColor()`
3. Update FullCalendar CSS classes

### Extending Staff Profiles
1. Update `StaffMember` interface in `staff.model.ts`
2. Modify Firestore security rules if needed
3. Update profile components and forms

### Custom Goals/Tasks Categories
1. Extend the category enums in respective models
2. Update form dropdowns and filters
3. Add appropriate icons and colors

## 📞 Support

For issues or questions:
1. Check the Firebase Console for authentication/database errors
2. Review browser console for client-side errors
3. Verify Firestore security rules are correctly applied
4. Ensure all environment variables are properly configured

## 🎉 Next Steps

1. Set up Firebase hosting for deployment
2. Configure Firebase Functions for advanced server-side logic
3. Add push notifications using Firebase Cloud Messaging
4. Implement advanced analytics and reporting
5. Add file upload capabilities using Firebase Storage
