// Shared Staff Models for StaffHub and TimeHub
export interface StaffMember {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  position: string;
  department: string;
  status: 'active' | 'inactive' | 'on-leave';
  businessIds: string[];
  primaryBusinessId: string;
  
  // Contact Information
  phone?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  
  // Employment Details
  hireDate: Date;
  terminationDate?: Date;
  employmentType: 'full-time' | 'part-time' | 'contract' | 'intern';
  payRate?: number;
  payType: 'hourly' | 'salary' | 'commission';
  
  // Permissions and Access
  role: 'admin' | 'manager' | 'staff';
  permissions: string[];
  
  // Profile Information
  profilePicture?: string;
  bio?: string;
  skills?: string[];
  certifications?: string[];
  
  // Preferences
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastLoginAt?: Date;
}

export interface StaffHubProfile {
  staffId: string;
  dashboardLayout: {
    widgets: DashboardWidget[];
    layout: any;
  };
  quickActions: string[];
  recentActivity: StaffActivity[];
  favoriteFeatures: string[];
}

export interface DashboardWidget {
  id: string;
  type: 'schedule' | 'tasks' | 'timesheet' | 'goals' | 'notifications' | 'weather' | 'custom';
  title: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  settings: any;
  isVisible: boolean;
}

export interface StaffActivity {
  id: string;
  staffId: string;
  type: 'clock-in' | 'clock-out' | 'task-completed' | 'schedule-updated' | 'goal-achieved';
  description: string;
  timestamp: Date;
  metadata?: any;
}

// Time Clock PIN Authentication
export interface StaffPinAuth {
  staffId: string;
  pin: string; // Hashed PIN
  isActive: boolean;
  lastUsed?: Date;
  failedAttempts: number;
  lockedUntil?: Date;
}

// Staff availability and scheduling
export interface StaffAvailability {
  staffId: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isAvailable: boolean;
  notes?: string;
}

export interface StaffSchedulePreferences {
  staffId: string;
  preferredShifts: string[];
  maxHoursPerWeek: number;
  minHoursPerWeek: number;
  preferredDaysOff: number[];
  canWorkWeekends: boolean;
  canWorkHolidays: boolean;
  notes?: string;
}
