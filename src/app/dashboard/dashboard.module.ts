import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatGridListModule } from '@angular/material/grid-list';
import { ButtonModule } from 'primeng/button';

import { ToastModule } from 'primeng/toast';
import { DashboardComponent } from './dashboard.component';
import { WidgetSettingsDialogComponent } from './widget-settings-dialog.component';
// TODO: Uncomment after creating these components
// import { WidgetSelectorDialogComponent } from './widget-selector-dialog.component';
// import { CustomWidgetBuilderComponent } from './custom-widget-builder.component';

@NgModule({
  imports: [
    CommonModule,
    DragDropModule,
    MatGridListModule,
    ButtonModule,
    ToastModule,
    DashboardComponent,
    WidgetSettingsDialogComponent
  ],
  declarations: [],
  exports: []
})
export class DashboardModule {} 