import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { TabViewModule } from 'primeng/tabview';

import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';

import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TooltipModule } from 'primeng/tooltip';

// Services
import { AIAssistantService } from '../../../../core/services/ai-assistant.service';
import { AuthService } from '../../../../core/auth/auth.service';

@Component({
  selector: 'app-scheduling-interface',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule,
    TabViewModule,
    DropdownModule,
    CalendarModule,
    ProgressSpinnerModule,
    TooltipModule
  ],
  template: `
    <div class="scheduling-container">
      <div class="scheduling-header">
        <h2>
          <i class="pi pi-calendar"></i>
          Schedule Management
        </h2>
        <div class="header-actions">
          <p-button (click)="createManualSchedule()">
            <i class="pi pi-plus"></i>
            Manual Schedule
          </p-button>
          <p-button (click)="generateAISchedule()" [disabled]="isGenerating">
            <i class="pi pi-circle"></i>
            <p-progressSpinner *ngIf="isGenerating"></p-progressSpinner>
            {{ isGenerating ? 'Generating...' : 'AI Schedule' }}
          </p-button>
        </div>

      <p-tabView class="scheduling-tabs">
        <!-- Manual Scheduling Tab -->
        <p-tabPanel label="Manual Scheduling">
          <div class="tab-content">
            <div class="manual-scheduling-section">
              <p-card class="scheduling-controls">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-calendar-plus"></i>
                    Create Schedule
                  </h3>
                  <p>Manually create and assign shifts</p>
                <ng-template pTemplate="content">
                  <form [formGroup]="manualScheduleForm" class="schedule-form">
                    <div class="form-row">
                      <div class="p-field">
                        <label>Week Starting</label>
                        <input pInputText formControlName="weekStart">
                      </div>

                      <div class="p-field">
                        <label>Department</label>
                        <p-dropdown formControlName="department"
                                   [options]="departmentOptions"
                                   optionLabel="label"
                                   optionValue="value"></p-dropdown>
                      </div>

                    <div class="form-row">
                      <div class="p-field">
                        <label>Shift Template</label>
                        <p-dropdown formControlName="template"
                                   [options]="templateOptions"
                                   optionLabel="label"
                                   optionValue="value"></p-dropdown>
                      </div>

                      <div class="p-field">
                        <label>Coverage Level</label>
                        <p-dropdown formControlName="coverage"
                                   [options]="coverageOptions"
                                   optionLabel="label"
                                   optionValue="value"></p-dropdown>
                      </div>
                  </form>
                <ng-template pTemplate="footer">
                  <p-button (click)="buildManualSchedule()">
                    <i class="pi pi-circle"></i>
                    Build Schedule
                  </p-button>
                  <p-button (click)="resetForm()">
                    <i class="pi pi-circle"></i>
                    Reset
                  </p-button>
                </p-card>

              <!-- Schedule Grid Placeholder -->
              <p-card class="schedule-grid-card">
                <ng-template pTemplate="header">
                  <h3>Weekly Schedule Grid</h3>
                </ng-template>
                <ng-template pTemplate="content">
                  <div class="schedule-placeholder">
                    <i class="pi pi-circle"></i>
                    <p>Schedule grid will appear here after building a schedule</p>
                  </div>
                </p-card>
            </div>
        </p-tabPanel>

        <!-- AI Scheduling Tab -->
        <p-tabPanel label="AI-Powered Scheduling">
          <div class="tab-content">
            <div class="ai-scheduling-section">
              <p-card class="ai-controls">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-brain"></i>
                    AI Schedule Generator
                  </h3>
                  <p>Powered by Gemini 2.5 Flash - Intelligent scheduling with optimization</p>
                <ng-template pTemplate="content">
                  <form [formGroup]="aiScheduleForm" class="ai-form">
                    <div class="form-row">
                      <div class="p-field" appearance="outline">
                        <label>Schedule Period</label>
                        <p-dropdown formControlName="period" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                      </div>

                      <div class="p-field" appearance="outline">
                        <label>Optimization Priority</label>
                        <p-dropdown formControlName="priority" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                      </div>

                    <div class="ai-features">
                      <h4>AI Features Enabled:</h4>
                      <div class="feature-list">
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Staff availability analysis</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Time off integration</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Hours of operation compliance</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Workload balancing</span>
                        </div>
                        <div class="feature-item">
                          <i class="pi pi-check-circle"></i>
                          <span>Skill-based assignments</span>
                        </div>
                    </div>
                  </form>
                <ng-template pTemplate="footer">
                  <p-button (click)="generateAISchedule()" [disabled]="isGenerating">
                    <i class="pi pi-circle"></i>
                    <p-progressSpinner *ngIf="isGenerating"></p-progressSpinner>
                    {{ isGenerating ? 'Generating AI Schedule...' : 'Generate AI Schedule' }}
                  </p-button>
                  <p-button (click)="previewAISchedule()" [disabled]="isGenerating">
                    <i class="pi pi-eye"></i>
                    Preview
                  </p-button>
                </ng-template></p-card>

              <!-- AI Schedule Results -->
              <p-card class="ai-results" *ngIf="aiScheduleResults">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-sparkles"></i>
                    AI Generated Schedule
                  </h3>
                  <p>Optimized schedule with {{ aiScheduleResults.confidence }}% confidence</p>
                <ng-template pTemplate="content">
                  <div class="ai-insights">
                    <h4>AI Insights:</h4>
                    <div [innerHTML]="aiScheduleResults.insights"></div>

                  <div class="schedule-preview">
                    <p>Schedule preview will be displayed here</p>
                  </div>
                <ng-template pTemplate="footer">
                  <p-button (click)="applyAISchedule()">
                    <i class="pi pi-check"></i>
                    Apply Schedule
                  </p-button>
                  <p-button (click)="modifyAISchedule()">
                    <i class="pi pi-pencil"></i>
                    Modify
                  </p-button>
                  <p-button (click)="regenerateAISchedule()">
                    <i class="pi pi-circle"></i>
                    Regenerate
                  </p-button>
                </p-card>
            </div>
        </p-tabPanel>

        <!-- Schedule Templates Tab -->
        <p-tabPanel label="Templates">
          <div class="tab-content">
            <div class="templates-section">
              <p-card class="templates-card">
                <ng-template pTemplate="header">
                  <h3>
                    <i class="pi pi-book"></i>
                    Schedule Templates
                  </h3>
                  <p>Save and reuse common scheduling patterns</p>
                </ng-template>

                <ng-template pTemplate="content">
                  <div class="templates-placeholder">
                    <i class="pi pi-clock"></i>
                    <p>Schedule templates will be available here</p>
                    <p-button>
                      <i class="pi pi-plus"></i>
                      Create Template
                    </p-button>
                  </div>
                </ng-template></p-card>
            </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  `,
  styleUrls: ['./scheduling-interface.component.scss']
})
export class SchedulingInterfaceComponent implements OnInit {
  private aiService = inject(AIAssistantService);
  private authService = inject(AuthService);
  private fb = inject(FormBuilder);

  // Forms
  manualScheduleForm: FormGroup;
  aiScheduleForm: FormGroup;

  // State
  isGenerating = false;
  aiScheduleResults: any = null;

  // Dropdown options
  departmentOptions = [
    { label: 'All Departments', value: 'all' },
    { label: 'Sales', value: 'sales' },
    { label: 'Support', value: 'support' },
    { label: 'Management', value: 'management' }
  ];

  templateOptions = [
    { label: 'Standard (9 AM - 5 PM)', value: 'standard' },
    { label: 'Early (7 AM - 3 PM)', value: 'early' },
    { label: 'Late (1 PM - 9 PM)', value: 'late' },
    { label: 'Custom', value: 'custom' }
  ];

  coverageOptions = [
    { label: 'Minimal', value: 'minimal' },
    { label: 'Standard', value: 'standard' },
    { label: 'Full Coverage', value: 'full' }
  ];

  constructor() {
    this.manualScheduleForm = this.fb.group({
      weekStart: [new Date()],
      department: ['all'],
      template: ['standard'],
      coverage: ['standard']
    });

    this.aiScheduleForm = this.fb.group({
      period: ['week'],
      priority: ['balance']
    });
  }

  ngOnInit(): void {
    // Initialize component
  }

  createManualSchedule(): void {
    console.log('Create manual schedule');
  }

  buildManualSchedule(): void {
    console.log('Build manual schedule with:', this.manualScheduleForm.value);
  }

  resetForm(): void {
    this.manualScheduleForm.reset();
  }

  async generateAISchedule(): Promise<void> {
    this.isGenerating = true;

    try {
      // TODO: Implement AI schedule generation using Gemini 2.5 Flash
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate API call

      this.aiScheduleResults = {
        confidence: 92,
        insights: `
          <p><strong>Optimization Results:</strong></p>
          <ul>
            <li>Achieved 95% coverage with optimal staff distribution</li>
            <li>Balanced workload across all team members</li>
            <li>Respected all availability constraints and time off requests</li>
            <li>Minimized overtime costs while maintaining service levels</li>
          </ul>
        `
      };
    } catch (error) {
      console.error('AI schedule generation failed:', error);
    } finally {
      this.isGenerating = false;
    }
  }

  previewAISchedule(): void {
    console.log('Preview AI schedule');
  }

  applyAISchedule(): void {
    console.log('Apply AI schedule');
  }

  modifyAISchedule(): void {
    console.log('Modify AI schedule');
  }

  regenerateAISchedule(): void {
    this.aiScheduleResults = null;
    this.generateAISchedule();
  }
}
