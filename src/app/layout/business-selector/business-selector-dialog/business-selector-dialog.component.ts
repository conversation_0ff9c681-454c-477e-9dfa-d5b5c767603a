import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { FormsModule } from '@angular/forms';
import { Business } from '../business-selector.component';

@Component({
  selector: 'app-business-selector-dialog',
  standalone: true,
  imports: [CommonModule, CheckboxModule, ButtonModule, ChipModule, FormsModule],
  templateUrl: './business-selector-dialog.component.html',
  styleUrl: './business-selector-dialog.component.scss'
})
export class BusinessSelectorDialogComponent {
  search = '';
  selectedIds: string[];

  constructor(
    public dialogRef: DynamicDialogRef<BusinessSelectorDialogComponent>,
    @Inject(DynamicDialogConfig) public data: { businesses: Business[], selectedBusinessIds: string[] }
  ) {
    this.selectedIds = [...data.selectedBusinessIds];
  }

  get filteredBusinesses(): Business[] {
    return this.data.businesses.filter(b =>
      b.name.toLowerCase().includes(this.search.toLowerCase())
    );
  }

  toggleBusiness(id: string) {
    if (this.selectedIds.includes(id)) {
      this.selectedIds = this.selectedIds.filter(i => i !== id);
    } else if (this.selectedIds.length < 20) {
      this.selectedIds = [...this.selectedIds, id];
    }
  }

  selectAll() {
    this.selectedIds = this.data.businesses.map(b => b.id).slice(0, 20);
  }

  clearAll() {
    this.selectedIds = [];
  }

  confirm() {
    this.dialogRef.close(this.selectedIds);
  }

  cancel() {
    this.dialogRef.close(null);
  }
}
