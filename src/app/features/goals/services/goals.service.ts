import { Injectable, inject } from '@angular/core';
import { Observable, map, combineLatest, of } from 'rxjs';

// Services
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { StaffGoalExtended, StaffMember } from '../../staff/models/staff.model';

export interface GoalStatistics {
  total: number;
  active: number;
  completed: number;
  overdue: number;
  notStarted: number;
  byCategory: { [key: string]: number };
  byType: { [key: string]: number };
  byPriority: { [key: string]: number };
  averageProgress: number;
  completionRate: number;
}

export interface GoalFilters {
  status?: string;
  type?: string;
  category?: string;
  priority?: string;
  assignedTo?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchTerm?: string;
}

export interface GoalTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'individual' | 'team' | 'company';
  defaultPriority: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number; // in days
  milestoneTemplates: {
    title: string;
    daysFromStart: number;
  }[];
  tags: string[];
}

@Injectable({
  providedIn: 'root'
})
export class GoalsService {
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);

  // Goal Templates for quick creation
  private goalTemplates: GoalTemplate[] = [
    {
      id: 'sales-quarterly',
      name: 'Quarterly Sales Target',
      description: 'Achieve quarterly sales targets and improve customer acquisition',
      category: 'sales',
      type: 'individual',
      defaultPriority: 'high',
      estimatedDuration: 90,
      milestoneTemplates: [
        { title: 'Month 1 Target (33%)', daysFromStart: 30 },
        { title: 'Month 2 Target (66%)', daysFromStart: 60 },
        { title: 'Final Target (100%)', daysFromStart: 90 }
      ],
      tags: ['sales', 'quarterly', 'revenue']
    },
    {
      id: 'training-certification',
      name: 'Professional Certification',
      description: 'Complete professional certification to enhance skills',
      category: 'training',
      type: 'individual',
      defaultPriority: 'medium',
      estimatedDuration: 60,
      milestoneTemplates: [
        { title: 'Complete Course Material', daysFromStart: 30 },
        { title: 'Pass Practice Exams', daysFromStart: 45 },
        { title: 'Take Final Certification Exam', daysFromStart: 60 }
      ],
      tags: ['training', 'certification', 'skills']
    },
    {
      id: 'team-productivity',
      name: 'Team Productivity Improvement',
      description: 'Improve team productivity and collaboration metrics',
      category: 'performance',
      type: 'team',
      defaultPriority: 'high',
      estimatedDuration: 120,
      milestoneTemplates: [
        { title: 'Baseline Assessment', daysFromStart: 14 },
        { title: 'Implement Improvements', daysFromStart: 60 },
        { title: 'Measure Results', daysFromStart: 120 }
      ],
      tags: ['productivity', 'team', 'performance']
    },
    {
      id: 'attendance-improvement',
      name: 'Attendance Improvement',
      description: 'Improve attendance rates and reduce absenteeism',
      category: 'attendance',
      type: 'individual',
      defaultPriority: 'medium',
      estimatedDuration: 90,
      milestoneTemplates: [
        { title: 'Identify Attendance Patterns', daysFromStart: 14 },
        { title: 'Implement Attendance Plan', daysFromStart: 30 },
        { title: 'Review Progress', daysFromStart: 90 }
      ],
      tags: ['attendance', 'improvement', 'reliability']
    }
  ];

  /**
   * Get all goals with optional filtering
   */
  getGoals(businessId?: string, filters?: GoalFilters): Observable<StaffGoalExtended[]> {
    return this.staffService.subscribeToAllGoals().pipe(
      map(goals => this.applyFilters(goals, filters))
    );
  }

  /**
   * Get goals for a specific user
   */
  getUserGoals(userId: string, filters?: GoalFilters): Observable<StaffGoalExtended[]> {
    return this.getGoals(undefined, filters).pipe(
      map(goals => goals.filter(goal => goal.assignedTo.includes(userId)))
    );
  }

  /**
   * Get goals by type
   */
  getGoalsByType(type: 'individual' | 'team' | 'company', filters?: GoalFilters): Observable<StaffGoalExtended[]> {
    return this.getGoals(undefined, { ...filters, type });
  }

  /**
   * Get goal statistics
   */
  getGoalStatistics(businessId?: string): Observable<GoalStatistics> {
    return this.getGoals(businessId).pipe(
      map(goals => this.calculateStatistics(goals))
    );
  }

  /**
   * Get overdue goals
   */
  getOverdueGoals(businessId?: string): Observable<StaffGoalExtended[]> {
    return this.getGoals(businessId).pipe(
      map(goals => goals.filter(goal => this.isGoalOverdue(goal)))
    );
  }

  /**
   * Get goals due soon (within next 7 days)
   */
  getGoalsDueSoon(businessId?: string): Observable<StaffGoalExtended[]> {
    return this.getGoals(businessId).pipe(
      map(goals => goals.filter(goal => this.isGoalDueSoon(goal)))
    );
  }

  /**
   * Get goal templates
   */
  getGoalTemplates(): Observable<GoalTemplate[]> {
    return of(this.goalTemplates);
  }

  /**
   * Create goal from template
   */
  createGoalFromTemplate(
    templateId: string,
    customizations: Partial<StaffGoalExtended>
  ): Observable<StaffGoalExtended> {
    const template = this.goalTemplates.find(t => t.id === templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    const startDate = new Date();
    const targetDate = new Date(startDate.getTime() + (template.estimatedDuration * 24 * 60 * 60 * 1000));

    const goalData: Omit<StaffGoalExtended, 'id' | 'createdAt' | 'updatedAt'> = {
      title: customizations.title || template.name,
      description: customizations.description || template.description,
      type: customizations.type || template.type,
      category: (customizations.category || template.category) as 'sales' | 'performance' | 'training' | 'attendance' | 'custom',
      priority: customizations.priority || template.defaultPriority,
      assignedTo: customizations.assignedTo || [],
      assignedBy: '', // Will be set when creating
      createdBy: '', // Will be set when creating
      targetDate: customizations.targetDate || targetDate,
      status: 'not-started',
      progress: 0,
      tags: customizations.tags || template.tags,
      milestones: template.milestoneTemplates.map((mt, index) => ({
        id: `milestone_${index + 1}`,
        title: mt.title,
        targetDate: new Date(startDate.getTime() + (mt.daysFromStart * 24 * 60 * 60 * 1000)),
        completed: false
      })),
      comments: [],
      ...customizations
    };

    return this.staffService.createGoal(goalData);
  }

  /**
   * Update goal progress and auto-update status
   */
  updateGoalProgress(goalId: string, progress: number): Observable<void> {
    const updates: Partial<StaffGoalExtended> = {
      progress,
      status: this.getStatusFromProgress(progress)
    };

    return this.staffService.updateGoal(goalId, updates);
  }

  /**
   * Get goal completion rate for a user
   */
  getUserGoalCompletionRate(userId: string): Observable<number> {
    return this.getUserGoals(userId).pipe(
      map(goals => {
        if (goals.length === 0) return 0;
        const completedGoals = goals.filter(goal => goal.status === 'completed').length;
        return (completedGoals / goals.length) * 100;
      })
    );
  }

  /**
   * Get team goal performance
   */
  getTeamGoalPerformance(teamMemberIds: string[]): Observable<{
    totalGoals: number;
    completedGoals: number;
    averageProgress: number;
    memberPerformance: { [userId: string]: { completed: number; total: number; progress: number } };
  }> {
    return combineLatest(
      teamMemberIds.map(userId => this.getUserGoals(userId))
    ).pipe(
      map(userGoalsArray => {
        const memberPerformance: { [userId: string]: { completed: number; total: number; progress: number } } = {};
        let totalGoals = 0;
        let completedGoals = 0;
        let totalProgress = 0;

        teamMemberIds.forEach((userId, index) => {
          const userGoals = userGoalsArray[index];
          const completed = userGoals.filter(g => g.status === 'completed').length;
          const avgProgress = userGoals.length > 0 ?
            userGoals.reduce((sum, g) => sum + g.progress, 0) / userGoals.length : 0;

          memberPerformance[userId] = {
            completed,
            total: userGoals.length,
            progress: avgProgress
          };

          totalGoals += userGoals.length;
          completedGoals += completed;
          totalProgress += avgProgress;
        });

        return {
          totalGoals,
          completedGoals,
          averageProgress: teamMemberIds.length > 0 ? totalProgress / teamMemberIds.length : 0,
          memberPerformance
        };
      })
    );
  }

  // Private helper methods

  private applyFilters(goals: StaffGoalExtended[], filters?: GoalFilters): StaffGoalExtended[] {
    if (!filters) return goals;

    return goals.filter(goal => {
      if (filters.status && goal.status !== filters.status) return false;
      if (filters.type && goal.type !== filters.type) return false;
      if (filters.category && goal.category !== filters.category) return false;
      if (filters.priority && goal.priority !== filters.priority) return false;
      if (filters.assignedTo && !goal.assignedTo.includes(filters.assignedTo)) return false;

      if (filters.dateRange) {
        const goalDate = new Date(goal.targetDate);
        if (goalDate < filters.dateRange.start || goalDate > filters.dateRange.end) return false;
      }

      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        const titleMatch = goal.title.toLowerCase().includes(searchLower);
        const descriptionMatch = goal.description.toLowerCase().includes(searchLower);
        const tagsMatch = goal.tags?.some(tag => tag.toLowerCase().includes(searchLower));
        if (!titleMatch && !descriptionMatch && !tagsMatch) return false;
      }

      return true;
    });
  }

  private calculateStatistics(goals: StaffGoalExtended[]): GoalStatistics {
    const total = goals.length;
    const active = goals.filter(g => g.status === 'in-progress').length;
    const completed = goals.filter(g => g.status === 'completed').length;
    const overdue = goals.filter(g => this.isGoalOverdue(g)).length;
    const notStarted = goals.filter(g => g.status === 'not-started').length;

    const byCategory: { [key: string]: number } = {};
    const byType: { [key: string]: number } = {};
    const byPriority: { [key: string]: number } = {};

    goals.forEach(goal => {
      byCategory[goal.category] = (byCategory[goal.category] || 0) + 1;
      byType[goal.type] = (byType[goal.type] || 0) + 1;
      byPriority[goal.priority] = (byPriority[goal.priority] || 0) + 1;
    });

    const averageProgress = total > 0 ?
      goals.reduce((sum, goal) => sum + goal.progress, 0) / total : 0;

    const completionRate = total > 0 ? (completed / total) * 100 : 0;

    return {
      total,
      active,
      completed,
      overdue,
      notStarted,
      byCategory,
      byType,
      byPriority,
      averageProgress,
      completionRate
    };
  }

  private isGoalOverdue(goal: StaffGoalExtended): boolean {
    const today = new Date();
    const targetDate = new Date(goal.targetDate);
    return targetDate < today && goal.status !== 'completed';
  }

  private isGoalDueSoon(goal: StaffGoalExtended): boolean {
    const today = new Date();
    const targetDate = new Date(goal.targetDate);
    const daysUntilDue = Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilDue <= 7 && daysUntilDue >= 0 && goal.status !== 'completed';
  }

  private getStatusFromProgress(progress: number): 'not-started' | 'in-progress' | 'completed' {
    if (progress === 0) return 'not-started';
    if (progress >= 100) return 'completed';
    return 'in-progress';
  }
}
