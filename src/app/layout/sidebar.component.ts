import { Component, Input, Output, EventEmitter } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { SidebarModule } from 'primeng/sidebar';
import { MenuModule } from 'primeng/menu';
import { DividerModule } from 'primeng/divider';
import { BadgeModule } from 'primeng/badge';
import { TooltipModule } from 'primeng/tooltip';
import { PanelMenuModule } from 'primeng/panelmenu';
import { BusinessSelectorComponent, Business } from './business-selector/business-selector.component';
import { UserIconComponent } from './user-menu/user-menu.component';
import { StaffManagerThemeService } from '../core/theme/staffmanager-theme';
import { sidebarToggleAnimation, fadeInAnimation, staggerAnimation, buttonPressAnimation, expandCollapseAnimation } from '../core/animations/staffmanager-animations';
import { IconService } from '../core/services/icon.service';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, ButtonModule, SidebarModule, MenuModule, DividerModule, BadgeModule, TooltipModule, PanelMenuModule, BusinessSelectorComponent, UserIconComponent],
  animations: [sidebarToggleAnimation, fadeInAnimation, staggerAnimation, buttonPressAnimation, expandCollapseAnimation],
  host: {
    '[class.collapsed]': 'collapsed'
  },
  template: `
    <nav [style.width]="collapsed ? '64px' : '240px'"
         [style.background]="'linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%)'"
         [style.height]="'100vh'"
         [style.display]="'flex'"
         [style.flex-direction]="'column'"
         [style.box-shadow]="'2px 0 8px rgba(0,0,0,0.1)'"
         [style.border-right]="'1px solid #e0e0e0'"
         [style.transition]="'all 0.3s ease'"
         [style.overflow]="'hidden'"
         [@fadeIn]
         aria-label="Main navigation">

      <!-- Hamburger Toggle Button -->
      <div [ngStyle]="{
             'padding': '12px 8px',
             'border-bottom': '1px solid #e0e0e0',
             'background': '#ffffff',
             'display': 'flex',
             'justify-content': collapsed ? 'center' : 'flex-start'
           }">
        <p-button (onClick)="onToggleSidebar()"
                  icon="pi pi-bars"
                  [text]="true"
                  [rounded]="true"
                  [ngStyle]="{
                    'width': '44px',
                    'height': '44px',
                    'background': '#f5f5f5',
                    'border': '1px solid #e0e0e0',
                    'border-radius': '6px',
                    'transition': 'all 0.2s ease'
                  }"
                  [attr.aria-label]="collapsed ? 'Expand sidebar' : 'Collapse sidebar'">
        </p-button>
      </div>

      <!-- Business Selector Section -->
      <div [style.padding]="'16px 8px'"
           [style.border-bottom]="'1px solid #e0e0e0'"
           [style.background]="'#ffffff'"
           [style.display]="'flex'"
           [style.justify-content]="collapsed ? 'center' : 'flex-start'">
        <app-business-selector
          [businesses]="businesses"
          [selectedBusinessIds]="selectedBusinessIds"
          [collapsed]="collapsed"
          (selectionChange)="onBusinessSelectionChange($event)"
        ></app-business-selector>
      </div>

      <!-- Navigation List -->
      <div [style.flex]="'1'"
           [style.overflow-y]="'auto'"
           [style.padding]="'16px 0'">

        <div *ngIf="!collapsed"
             [style.font-size]="'1rem'"
             [style.font-weight]="'700'"
             [style.color]="'#1976d2'"
             [style.margin]="'16px 0 6px 24px'"
             [style.letter-spacing]="'0.04em'">Main</div>

        <a routerLink="/dashboard"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="Dashboard">
          <i class="pi pi-th-large"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">Dashboard</span>
        </a>
        <a routerLink="/calendar"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="Calendar">
          <i class="pi pi-calendar"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">Calendar</span>
        </a>
        <a routerLink="/staff"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="Staff">
          <i class="pi pi-users"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">Staff</span>
        </a>
        <a routerLink="/tasks"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="Tasks">
          <i class="pi pi-check-square"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">Tasks</span>
        </a>
        <a routerLink="/goals"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="Goals">
          <i class="pi pi-target"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">Goals</span>
        </a>
        <a routerLink="/time"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="Time Management">
          <i class="pi pi-clock"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">Time</span>
        </a>

        <!-- StaffHub Section -->
        <div *ngIf="!collapsed"
             [style.font-size]="'1rem'"
             [style.font-weight]="'700'"
             [style.color]="'#1976d2'"
             [style.margin]="'16px 0 6px 24px'"
             [style.letter-spacing]="'0.04em'">StaffHub</div>

        <a routerLink="/staff/staff-hub"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="StaffHub Management">
          <i class="pi pi-mobile"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">StaffHub PWA</span>
          <span *ngIf="!collapsed && staffHubStatus === 'online'"
                class="pi pi-circle-fill"
                [style.color]="'#4caf50'"
                [style.margin-left]="'auto'"
                [style.font-size]="'0.5rem'"></span>
          <span *ngIf="!collapsed && staffHubStatus === 'offline'"
                class="pi pi-circle-fill"
                [style.color]="'#f44336'"
                [style.margin-left]="'auto'"
                [style.font-size]="'0.5rem'"></span>
        </a>

        <a routerLink="/settings"
           routerLinkActive="active"
           [ngStyle]="{
             'display': 'flex',
             'align-items': 'center',
             'padding': '8px 12px',
             'margin': '2px 8px',
             'border-radius': '8px',
             'text-decoration': 'none',
             'color': '#333333',
             'font-weight': '500',
             'min-height': '48px',
             'transition': 'all 0.2s ease',
             'cursor': 'pointer'
           }"
           aria-label="Settings">
          <i class="pi pi-cog"
             [ngStyle]="{
               'margin-right': collapsed ? '0' : '18px',
               'color': '#666666',
               'font-size': collapsed ? '2rem' : '1.6rem'
             }"></i>
          <span *ngIf="!collapsed">Settings</span>
        </a>
      </div>

      <!-- User Icon at Bottom -->
      <app-user-icon [collapsed]="collapsed"></app-user-icon>
    </nav>
  `,
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  @Input() collapsed = false;

  @Output() businessSelectionChange = new EventEmitter<string[]>();
  @Output() toggleSidebar = new EventEmitter<void>();

  staffMenuOpen = false;
  selectedBusinessIds: string[] = ['1'];
  businesses: Business[] = [
    { id: '1', name: 'S&E Jewelers', logoUrl: '' },
    { id: '2', name: 'Diamond District', logoUrl: '' },
    { id: '3', name: 'Gold Rush', logoUrl: '' },
    { id: '4', name: 'Precious Gems Co.', logoUrl: '' }
  ];
  taskBadgeCount = 5;
  goalBadgeCount = 3;
  staffHubStatus: 'online' | 'offline' | 'unknown' = 'unknown';

  constructor(
    public themeService: StaffManagerThemeService,
    private iconService: IconService
  ) {}

  get navigationItems() {
    return [
      { route: '/dashboard', icon: 'dashboard', label: 'Dashboard' },
      { route: '/calendar', icon: 'calendar', label: 'Calendar' },
      { route: '/staff', icon: 'staff', label: 'Staff' },
      { route: '/tasks', icon: 'tasks', label: 'Tasks' },
      { route: '/goals', icon: 'goals', label: 'Goals' },
      { route: '/time', icon: 'time', label: 'Time' },
      { route: '/settings', icon: 'settings', label: 'Settings' }
    ];
  }

  getIcon(semanticName: string): string {
    return this.iconService.getIconName(semanticName);
  }

  toggleStaffMenu() {
    this.staffMenuOpen = !this.staffMenuOpen;
  }

  onToggleSidebar() {
    console.log('🔥 HAMBURGER CLICKED - TOGGLE SIDEBAR');
    this.toggleSidebar.emit();
  }

  onBusinessSelectionChange(ids: string[]) {
    this.selectedBusinessIds = ids;
    this.businessSelectionChange.emit(ids);
  }
}