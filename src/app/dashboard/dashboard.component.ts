import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { DialogService } from 'primeng/dynamicdialog';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { Subscription, Observable } from 'rxjs';
import { NumberWidgetComponent } from './widgets/number-widget.component';
import { UpcomingShiftsWidgetComponent } from './widgets/upcoming-shifts-widget.component';
import { DashboardStateService, DashboardWidget } from './services/dashboard-state.service';
import { WidgetDataService } from './services/widget-data.service';
import { staggerAnimation, fadeInAnimation } from '../core/animations/staffmanager-animations';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule, ButtonModule, CardModule, DragDropModule, NumberWidgetComponent, UpcomingShiftsWidgetComponent
  ],
  animations: [staggerAnimation, fadeInAnimation],
  template: `
    <div class="dashboard-container" [@fadeIn]>
      <div class="dashboard-toolbar">
        <p-button (onClick)="toggleEditMode()"
                  [icon]="editMode ? 'pi pi-times' : 'pi pi-pencil'"
                  text="true"
                  class="edit-toggle-btn transition-fast"
                  [label]="editMode ? 'Exit Edit' : 'Edit'">
        </p-button>
        <span *ngIf="editMode" class="edit-mode-indicator">Edit Mode Enabled</span>
      </div>

      <div class="dashboard-grid" [@stagger]="widgets.length">
        <div class="grid">
          <div *ngFor="let widget of widgets; trackBy: trackByWidgetId"
               cdkDrag
               class="col-12 md:col-6 lg:col-3 widget-tile transition-normal">
            <p-card class="widget-card">
              <ng-template pTemplate="header" *ngIf="editMode">
                <div class="widget-tile-header">
                  <p-button (onClick)="openWidgetSettings(widget)"
                            icon="pi pi-cog"
                            text="true"
                            size="small">
                  </p-button>
                </div></ng-template>
        <ng-container [ngSwitch]="widget.id">
          <app-number-widget
            *ngSwitchCase="'staff-on-shift'"
            [title]="'Staff On Shift'"
            [value]="(staffOnShift$ | async) ?? 0"
            [description]="'Staff members currently on shift'"
            [icon]="'people'"
            [color]="'primary'"
            [trend]="'+2 from yesterday'"
            [trendDirection]="'up'"
          ></app-number-widget>
          <app-upcoming-shifts-widget
            *ngSwitchCase="'upcoming-shifts'"
            [title]="'Upcoming Shifts'"
            [value]="((upcomingShifts$ | async)?.length ?? 0)"
            [description]="'Upcoming shifts today'"
            [shifts]="(upcomingShifts$ | async) ?? []"
          ></app-upcoming-shifts-widget>
          <app-number-widget
            *ngSwitchCase="'notifications'"
            [title]="'Notifications'"
            [value]="(notifications$ | async) ?? 0"
            [description]="'Unread notifications'"
            [icon]="'notifications'"
            [color]="'accent'"
            [trend]="'3 new today'"
            [trendDirection]="'up'"
          ></app-number-widget>
          <app-number-widget
            *ngSwitchCase="'messages'"
            [title]="'Messages'"
            [value]="(messages$ | async) ?? 0"
            [description]="'Unread messages'"
            [icon]="'message'"
            [color]="'primary'"
            [trend]="'All caught up'"
            [trendDirection]="'neutral'"
          ></app-number-widget>
          <div *ngSwitchDefault class="widget-placeholder">
            {{ widget.title }}
          </div>
        </ng-container>
            </p-card>
          </div>
      </div>
    </div>
  `,
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnDestroy {
  widgets: DashboardWidget[] = [];
  editMode = false;
  private sub = new Subscription();

  staffOnShift$: Observable<number>;
  upcomingShifts$: Observable<any[]>;
  notifications$: Observable<number>;
  messages$: Observable<number>;

  constructor(
    private dialogService: DialogService,
    private dashboardState: DashboardStateService,
    private widgetData: WidgetDataService
  ) {
    this.sub.add(this.dashboardState.widgets$.subscribe(w => this.widgets = w));
    this.sub.add(this.dashboardState.editMode$.subscribe(e => this.editMode = e));
    this.staffOnShift$ = this.widgetData.getStaffOnShift();
    this.upcomingShifts$ = this.widgetData.getUpcomingShifts();
    this.notifications$ = this.widgetData.getNotifications();
    this.messages$ = this.widgetData.getMessages();
  }

  toggleEditMode() {
    this.dashboardState.setEditMode(!this.editMode);
  }

  openWidgetSettings(widget: DashboardWidget) {
    // Dynamically import the dialog only when needed
    import('./widget-settings-dialog.component').then(({ WidgetSettingsDialogComponent }) => {
      const dialogRef = this.dialog.open(WidgetSettingsDialogComponent, {
        data: { settings: widget.settings || {} }
      });
      dialogRef.onClose.subscribe(result => {
        if (result) {
          this.dashboardState.updateWidgetSettings(widget.id, result);
        }
      });
    });
  }

  trackByWidgetId(index: number, widget: DashboardWidget): string {
    return widget.id;
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }
}