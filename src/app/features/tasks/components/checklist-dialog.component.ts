import { Component, Inject, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { Observable, map } from 'rxjs';

// Angular Material

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';

import { ButtonModule } from 'primeng/button';

import { CheckboxModule } from 'primeng/checkbox';
import { AccordionModule } from 'primeng/accordion';
import { DividerModule } from 'primeng/divider';
import { ListboxModule } from 'primeng/listbox';
import { MessageService } from '@angular/material/snack-bar';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';

// Services
import { TaskManagementService } from '../services/task-management.service';
import { AITaskAssistantService } from '../services/ai-task-assistant.service';
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { Checklist, ChecklistItem } from '../models/task.model';
import { StaffMember } from '../../staff/models/staff.model';

export interface ChecklistDialogData {
  checklist?: Checklist;
  businessId: string;
  mode: 'create' | 'edit';
}

@Component({
  selector: 'app-checklist-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DragDropModuleInputTextModule, DropdownModule,
    CalendarModule,
    ButtonModule
    CheckboxModule,
    AccordionModule,
    DividerModule,
    ListboxModule
  ],
  template: `
    <div class="checklist-dialog">
      <h2 mat-dialog-title>
        <i class="pi pi-circle"></i>
        {{ data.mode === 'create' ? 'Create New Checklist' : 'Edit Checklist' }}
      </h2>

      <mat-dialog-content>
        <form [formGroup]="checklistForm" class="checklist-form">

          <!-- Basic Information -->
          <div class="form-section">
            <h3>Basic Information</h3>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Checklist Title</label>
              <input pInputText formControlName="title" placeholder="Enter checklist title" required>
              <mat-error *ngIf="checklistForm.get('title')?.hasError('required')">
                Checklist title is required
              </mat-error>
            </div>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Description</label>
              <textarea pInputText formControlName="description" rows="3"
                       placeholder="Describe the checklist purpose and context"></textarea>
            </div>

            <div class="form-row">
              <div class="p-field" appearance="outline">
                <label>Priority</label>
                <p-dropdown formControlName="priority" required>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                  <option value="critical">Critical</option>
                </p-dropdown>
              </div>

              <div class="p-field" appearance="outline">
                <label>Category</label>
                <p-dropdown formControlName="category" required>
                  <option value="administrative">Administrative</option>
                  <option value="customer-service">Customer Service</option>
                  <option value="training">Training</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="project">Project</option>
                  <option value="safety">Safety</option>
                  <option value="compliance">Compliance</option>
                  <option value="custom">Custom</option>
                </p-dropdown>
              </div>

              <div class="p-field" appearance="outline">
                <label>Status</label>
                <p-dropdown formControlName="status" required>
                  <option value="pending">Pending</option>
                  <option value="in-progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="on-hold">On Hold</option>
                  <option value="cancelled">Cancelled</option>
                </p-dropdown>
              </div>
            </div>
          </div>

          <!-- Assignment and Scheduling -->
          <div class="form-section">
            <h3>Assignment & Scheduling</h3>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Assign to Staff</label>
              <p-dropdown formControlName="assignedTo" multiple required>
                <option *ngFor="let staff of staff$ | async" [value]="staff.id">
                  {{ staff.firstName }} {{ staff.lastName }} - {{ staff.position }}
                </option>
              </p-dropdown>
              <mat-hint>Select one or more staff members</mat-hint>
            </div>

            <div class="form-row">
              <div class="p-field" appearance="outline">
                <label>Start Date</label>
                <input pInputText  formControlName="startDate">
                
                
              </div>

              <div class="p-field" appearance="outline">
                <label>Due Date</label>
                <input pInputText  formControlName="dueDate">
                
                
              </div>
            </div>
          </div>

          <!-- Checklist Settings -->
          <div class="form-section">
            <h3>Checklist Settings</h3>

            <p-checkbox formControlName="allowPartialCompletion">
              Allow partial completion (checklist can be marked complete with some items pending)
            </p-checkbox>

            <p-checkbox formControlName="requireAllItems">
              Require all items to be completed
            </p-checkbox>

            <p-checkbox formControlName="verificationRequired">
              Require manager verification for completion
            </p-checkbox>

            <p-checkbox formControlName="syncWithCalendar">
              Sync with calendar
            </p-checkbox>
          </div>

          <!-- Checklist Items -->
          <div class="form-section">
            <div class="items-header">
              <h3>Checklist Items</h3>
              <div class="items-actions">
                <button type="button" p-button [outlined]="true" color="primary"
                        (click)="generateAIItems()" [disabled]="isLoadingAI()">
                  <i class="pi pi-brain"></i>
                  AI Generate
                </p-button>
                <button type="button" p-button color="accent" (click)="addItem()">
                  <i class="pi pi-plus"></i>
                  Add Item
                </p-button>
              </div>
            </div>

            <div class="checklist-items" cdkDropList (cdkDropListDropped)="dropItem($event)">
              <div *ngFor="let item of items.controls; let i = index"
                   class="checklist-item"
                   cdkDrag>
                <div class="item-content" [formGroup]="$any(item)">
                  <div class="item-header">
                    <i class="pi pi-circle"></i>
                    <span class="item-number">{{ i + 1 }}</span>
                    <button type="button" p-button [text]="true" color="warn"
                            (click)="removeItem(i)"
                            pTooltip="Remove item">
                      <i class="pi pi-trash"></i>
                    </p-button>
                  </div>

                  <div class="item-form">
                    <div class="p-field" appearance="outline" class="item-title">
                      <label>Item Title</label>
                      <input pInputText formControlName="title" placeholder="Enter checklist item" required>
                    </div>

                    <div class="p-field" appearance="outline" class="item-description">
                      <label>Description (optional)</label>
                      <textarea pInputText formControlName="description" rows="2"
                               placeholder="Additional details or instructions"></textarea>
                    </div>

                    <div class="item-options">
                      <p-checkbox formControlName="isRequired">Required</p-checkbox>
                      <p-checkbox formControlName="requiresVerification">Requires Verification</p-checkbox>
                      <p-checkbox formControlName="photoRequired">Photo Required</p-checkbox>
                      <p-checkbox formControlName="signatureRequired">Signature Required</p-checkbox>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div *ngIf="items.length === 0" class="empty-items">
              <i class="pi pi-circle"></i>
              <p>No checklist items yet. Add items manually or use AI to generate them.</p>
            </div>
          </div>

          <!-- Advanced Options -->
          <p-accordionTab class="advanced-options">
            <p-accordionTab-header>
              <mat-panel-title>
                <i class="pi pi-cog"></i>
                Advanced Options
              </mat-panel-title>
            </mat-expansion-panel-header>

            <div class="form-section">
              <p-checkbox formControlName="isTemplate">
                Save as template for future use
              </p-checkbox>

              <div class="p-field" appearance="outline" class="full-width"
                             *ngIf="checklistForm.get('isTemplate')?.value">
                <label>Template Category</label>
                <input pInputText formControlName="templateCategory"
                       placeholder="e.g., Opening Procedures, Safety Checks">
              </div>

              <div class="p-field" appearance="outline" class="full-width">
                <label>Location</label>
                <input pInputText formControlName="location" placeholder="Checklist location (optional)">
              </div>
            </div>
          </p-accordionTab>
        </form>
      </mat-dialog-content>

      <mat-dialog-actions align="end">
        <button p-button (click)="onCancel()">Cancel</p-button>
        <p-button color="primary"
                (click)="onSave()"
                [disabled]="checklistForm.invalid || isSaving() || items.length === 0">
          <i class="pi pi-circle"></i>
          {{ data.mode === 'create' ? 'Create Checklist' : 'Save Changes' }}
        </p-button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .checklist-dialog {
      width: 100%;
      max-width: 900px;

      h2 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;

        mat-icon {
          color: var(--mdc-theme-primary);
        }
      }

      .checklist-form {
        .form-section {
          margin-bottom: 24px;

          h3 {
            margin: 0 0 16px 0;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--mdc-theme-on-surface);
          }

          .full-width {
            width: 100%;
          }

          .form-row {
            display: flex;
            gap: 16px;
            align-items: flex-start;

            mat-form-field {
              flex: 1;
            }
          }

          mat-checkbox {
            margin-bottom: 8px;
            display: block;
          }

          .items-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .items-actions {
              display: flex;
              gap: 8px;
            }
          }

          .checklist-items {
            .checklist-item {
              margin-bottom: 16px;
              border: 1px solid var(--mdc-theme-outline-variant);
              border-radius: 8px;
              background: var(--mdc-theme-surface);

              &.cdk-drag-preview {
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
              }

              .item-content {
                padding: 16px;

                .item-header {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  margin-bottom: 12px;

                  .drag-handle {
                    cursor: move;
                    color: var(--mdc-theme-on-surface-variant);
                  }

                  .item-number {
                    font-weight: 500;
                    color: var(--mdc-theme-primary);
                    min-width: 20px;
                  }

                  button {
                    margin-left: auto;
                  }
                }

                .item-form {
                  .item-title {
                    width: 100%;
                    margin-bottom: 8px;
                  }

                  .item-description {
                    width: 100%;
                    margin-bottom: 12px;
                  }

                  .item-options {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 16px;

                    mat-checkbox {
                      margin-bottom: 0;
                    }
                  }
                }
              }
            }
          }

          .empty-items {
            text-align: center;
            padding: 32px;
            color: var(--mdc-theme-on-surface-variant);
            border: 2px dashed var(--mdc-theme-outline-variant);
            border-radius: 8px;

            mat-icon {
              font-size: 3rem;
              width: 3rem;
              height: 3rem;
              margin-bottom: 16px;
              opacity: 0.5;
            }

            p {
              margin: 0;
            }
          }
        }

        .advanced-options {
          margin-bottom: 16px;

          mat-expansion-panel-header {
            mat-panel-title {
              display: flex;
              align-items: center;
              gap: 8px;

              mat-icon {
                color: var(--mdc-theme-primary);
              }
            }
          }
        }
      }
    }

    @media (max-width: 600px) {
      .checklist-dialog {
        .checklist-form {
          .form-section {
            .form-row {
              flex-direction: column;
              gap: 8px;
            }

            .items-header {
              flex-direction: column;
              align-items: stretch;
              gap: 12px;

              .items-actions {
                justify-content: center;
              }
            }

            .checklist-items {
              .checklist-item {
                .item-content {
                  .item-options {
                    flex-direction: column;
                    gap: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
  `]
})
export class ChecklistDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private taskService = inject(TaskManagementService);
  private aiService = inject(AITaskAssistantService);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private snackBar = inject(MessageService);

  // Signals
  isLoadingAI = signal(false);
  isSaving = signal(false);

  // Form
  checklistForm!: FormGroup;

  // Data
  staff$!: Observable<StaffMember[]>;

  constructor(
    public dialogRef: DynamicDialogRef<ChecklistDialogComponent>,
    @Inject(DynamicDialogConfig) public data: ChecklistDialogData
  ) {}

  get items(): FormArray {
    return this.checklistForm.get('items') as FormArray;
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadStaff();

    if (this.data.checklist) {
      this.populateForm(this.data.checklist);
    }
  }

  private initializeForm(): void {
    this.checklistForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      priority: ['medium', Validators.required],
      category: ['administrative', Validators.required],
      status: ['pending', Validators.required],
      assignedTo: [[], Validators.required],
      startDate: [null],
      dueDate: [null],
      allowPartialCompletion: [false],
      requireAllItems: [true],
      verificationRequired: [false],
      syncWithCalendar: [true],
      isTemplate: [false],
      templateCategory: [''],
      location: [''],
      items: this.fb.array([])
    });
  }

  private loadStaff(): void {
    this.staff$ = this.staffService.getActiveStaff(this.data.businessId);
  }

  private populateForm(checklist: Checklist): void {
    this.checklistForm.patchValue({
      title: checklist.title,
      description: checklist.description,
      priority: checklist.priority,
      category: checklist.category,
      status: checklist.status,
      assignedTo: checklist.assignedTo,
      startDate: checklist.startDate,
      dueDate: checklist.dueDate,
      allowPartialCompletion: checklist.allowPartialCompletion,
      requireAllItems: checklist.requireAllItems,
      verificationRequired: checklist.verificationRequired,
      syncWithCalendar: checklist.syncWithCalendar,
      isTemplate: checklist.isTemplate,
      templateCategory: checklist.templateCategory,
      location: checklist.location
    });

    // Populate items
    checklist.items.forEach(item => {
      this.items.push(this.createItemFormGroup(item));
    });
  }

  private createItemFormGroup(item?: ChecklistItem): FormGroup {
    return this.fb.group({
      id: [item?.id || `item-${Date.now()}-${Math.random()}`],
      title: [item?.title || '', Validators.required],
      description: [item?.description || ''],
      isRequired: [item?.isRequired ?? true],
      requiresVerification: [item?.requiresVerification ?? false],
      photoRequired: [item?.photoRequired ?? false],
      signatureRequired: [item?.signatureRequired ?? false],
      order: [item?.order || this.items.length + 1]
    });
  }

  addItem(): void {
    this.items.push(this.createItemFormGroup());
  }

  removeItem(index: number): void {
    this.items.removeAt(index);
    this.updateItemOrders();
  }

  dropItem(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.items.controls, event.previousIndex, event.currentIndex);
    this.updateItemOrders();
  }

  private updateItemOrders(): void {
    this.items.controls.forEach((control, index) => {
      control.get('order')?.setValue(index + 1);
    });
  }

  async generateAIItems(): Promise<void> {
    this.isLoadingAI.set(true);

    try {
      const category = this.checklistForm.get('category')?.value;
      const title = this.checklistForm.get('title')?.value;
      const description = this.checklistForm.get('description')?.value;

      const context = `${title} - ${description}`;
      const aiItems = await this.aiService.generateChecklistTemplate(category, context);

      if (aiItems.length > 0) {
        // Clear existing items and add AI generated ones
        while (this.items.length > 0) {
          this.items.removeAt(0);
        }

        aiItems.forEach(item => {
          this.items.push(this.createItemFormGroup(item));
        });

        this.snackBar.open(`Generated ${aiItems.length} checklist items with AI`, 'Close', { duration: 5000 });
      }
    } catch (error) {
      this.snackBar.open('Error generating AI checklist items', 'Close', { duration: 3000 });
      console.error('AI checklist generation error:', error);
    } finally {
      this.isLoadingAI.set(false);
    }
  }

  onSave(): void {
    if (this.checklistForm.valid && this.items.length > 0) {
      this.isSaving.set(true);

      const formValue = this.checklistForm.value;
      const currentUser = this.authService.userProfile$.pipe(map(u => u)).toPromise();

      currentUser.then(user => {
        if (!user) return;

        const checklistData: Omit<Checklist, 'id' | 'createdAt' | 'updatedAt'> = {
          ...formValue,
          type: 'checklist' as const,
          businessId: this.data.businessId,
          createdBy: user.uid,
          lastModifiedBy: user.uid,
          items: formValue.items.map((item: any, index: number) => ({
            ...item,
            order: index + 1,
            isCompleted: false,
            aiGenerated: false
          })),
          completionPercentage: 0,
          progress: 0
        };

        if (this.data.mode === 'create') {
          this.taskService.createChecklist(checklistData).subscribe({
            next: () => {
              this.snackBar.open('Checklist created successfully', 'Close', { duration: 3000 });
              this.dialogRef.close(true);
            },
            error: (error: any) => {
              this.snackBar.open('Error creating checklist', 'Close', { duration: 3000 });
              console.error('Checklist save error:', error);
              this.isSaving.set(false);
            }
          });
        } else {
          this.taskService.updateTask(this.data.checklist!.id, checklistData).subscribe({
            next: () => {
              this.snackBar.open('Checklist updated successfully', 'Close', { duration: 3000 });
              this.dialogRef.close(true);
            },
            error: (error: any) => {
              this.snackBar.open('Error updating checklist', 'Close', { duration: 3000 });
              console.error('Checklist save error:', error);
              this.isSaving.set(false);
            }
          });
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
