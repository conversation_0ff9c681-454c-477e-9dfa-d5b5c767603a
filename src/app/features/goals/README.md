# Goals Management Module

A comprehensive goal management system for StaffManager that enables organizations to set, track, and achieve objectives at individual, team, and company levels.

## Features

### 🎯 Goal Management
- **Create & Edit Goals**: Comprehensive goal creation with templates and AI suggestions
- **Goal Types**: Individual, Team, and Company-wide goals
- **Categories**: Sales, Performance, Training, Attendance, and Custom categories
- **Priority Levels**: Low, Medium, High, and Critical priorities
- **Progress Tracking**: Visual progress bars and percentage completion
- **Status Management**: Not Started, In Progress, Completed, and Overdue statuses

### 📊 Progress Tracking
- **Real-time Progress Updates**: Live progress tracking with visual indicators
- **Target Values**: Set measurable targets with units (dollars, hours, percentage, etc.)
- **Milestone Management**: Break goals into smaller, manageable milestones
- **Completion Rates**: Track completion rates across individuals and teams
- **Overdue Detection**: Automatic detection and highlighting of overdue goals

### 👥 Collaboration
- **Multi-user Assignment**: Assign goals to multiple team members
- **Comments System**: Collaborative commenting on goals
- **Activity Tracking**: Track goal-related activities and updates
- **Team Performance**: Monitor team goal performance and metrics

### 📈 Analytics & Reporting
- **Goal Statistics**: Comprehensive statistics dashboard
- **Completion Rates**: Track completion rates by category, type, and priority
- **Performance Metrics**: Individual and team performance analytics
- **Progress Visualization**: Charts and graphs for goal progress

### 🤖 AI Integration
- **Goal Suggestions**: AI-powered goal recommendations
- **Progress Analysis**: Intelligent progress analysis and recommendations
- **Template Generation**: AI-assisted goal template creation

## Components

### Main Components

#### `GoalsComponent`
The main goals management interface featuring:
- Tabbed view (All Goals, My Goals, Team Goals, Company Goals)
- Advanced filtering and search
- Goal statistics dashboard
- Bulk operations

#### `GoalCardComponent`
Reusable goal display card with:
- Progress visualization
- Status and priority indicators
- Milestone progress
- Quick actions menu

#### `GoalDialogComponent`
Comprehensive goal creation/editing form:
- Multi-step form with validation
- Milestone management
- Assignment controls
- Advanced options (recurring goals, tags, etc.)

#### `GoalDetailsComponent`
Detailed goal view with:
- Full goal information
- Milestone management
- Comments and collaboration
- Assigned staff management
- Progress tracking

#### `GoalProgressWidgetComponent`
Dashboard widget for goal overview:
- Overall completion statistics
- Recent goals summary
- Quick actions

### Services

#### `GoalsService`
Core service providing:
- Goal CRUD operations
- Advanced filtering and search
- Statistics calculation
- Template management
- Performance analytics

## Usage

### Basic Goal Creation

```typescript
import { GoalsService } from './features/goals';

// Create a new goal
const goalData = {
  title: 'Increase Sales by 20%',
  description: 'Achieve 20% increase in quarterly sales',
  type: 'individual',
  category: 'sales',
  priority: 'high',
  assignedTo: ['user123'],
  targetDate: new Date('2024-12-31'),
  targetValue: 120000,
  currentValue: 100000,
  unit: 'dollars'
};

this.goalsService.createGoal(goalData).subscribe();
```

### Using Goal Templates

```typescript
// Create goal from template
this.goalsService.createGoalFromTemplate('sales-quarterly', {
  title: 'Q2 Sales Target',
  assignedTo: ['user123'],
  targetValue: 150000
}).subscribe();
```

### Filtering Goals

```typescript
// Filter goals by status and type
const filters = {
  status: 'in-progress',
  type: 'team',
  category: 'sales'
};

this.goalsService.getGoals(undefined, filters).subscribe();
```

### Getting Statistics

```typescript
// Get goal statistics
this.goalsService.getGoalStatistics().subscribe(stats => {
  console.log('Total goals:', stats.total);
  console.log('Completion rate:', stats.completionRate);
  console.log('Overdue goals:', stats.overdue);
});
```

## Navigation

The Goals module is accessible via:
- **Main Navigation**: `/goals` - Main goals management interface
- **Create Goal**: `/goals/create` - Goal creation form
- **Edit Goal**: `/goals/edit/:id` - Goal editing form
- **Goal Details**: `/goals/details/:id` - Detailed goal view

## Integration

### Staff Profiles
Goals are integrated into staff profiles showing:
- Individual goals assigned to the staff member
- Progress tracking
- Quick actions for goal management

### Dashboard
Goal progress widget can be added to dashboards for:
- Overview of goal completion
- Recent goal activity
- Quick access to goal management

### Calendar Integration
Goals with due dates are integrated with the calendar system for:
- Deadline tracking
- Milestone reminders
- Schedule coordination

## Permissions

Goal management respects the following permission levels:
- **View**: All authenticated users can view goals assigned to them
- **Create/Edit**: Managers and above can create and edit goals
- **Delete**: Managers and above can delete goals
- **Assign**: Managers can assign goals to team members

## Data Models

### StaffGoalExtended
```typescript
interface StaffGoalExtended {
  id: string;
  title: string;
  description: string;
  type: 'individual' | 'team' | 'company';
  category: 'sales' | 'performance' | 'training' | 'attendance' | 'custom';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo: string[];
  assignedBy: string;
  targetDate: Date;
  status: 'not-started' | 'in-progress' | 'completed' | 'overdue';
  progress: number; // 0-100
  targetValue?: number;
  currentValue?: number;
  unit?: string;
  tags?: string[];
  milestones?: GoalMilestone[];
  comments?: GoalComment[];
  isRecurring?: boolean;
  recurringPattern?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

## Future Enhancements

- **Goal Dependencies**: Link goals with dependencies
- **Automated Progress Updates**: Integration with external systems
- **Advanced Analytics**: Machine learning insights
- **Goal Templates Marketplace**: Shared goal templates
- **Mobile App Integration**: Native mobile goal tracking
- **Gamification**: Achievement badges and leaderboards

## Contributing

When contributing to the Goals module:
1. Follow the established component patterns
2. Maintain TypeScript strict mode compliance
3. Add comprehensive tests for new features
4. Update documentation for API changes
5. Ensure accessibility compliance (WCAG 2.1 AA)
