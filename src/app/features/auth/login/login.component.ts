import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CardModule } from 'primeng/card';

import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';

import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { DividerModule } from 'primeng/divider';
import { AuthService } from '../../../core/auth/auth.service';
import { Auth } from '@angular/fire/auth';
import { Firestore } from '@angular/fire/firestore';
import { FirebaseContextService } from '../../../core/services/firebase-context.service';
import { fadeInAnimation, slideInAnimation, buttonPressAnimation } from '../../../core/animations/staffmanager-animations';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    CardModule
    InputTextModule,
    ButtonModule
    ProgressSpinnerModule,
    ToastModule,
    DividerModule
  ],
  animations: [fadeInAnimation, slideInAnimation, buttonPressAnimation],
  template: `
    <div class="login-container" [@fadeIn]>
      <p-card class="login-card shadow-xl" [@slideIn]>
        <ng-template pTemplate="header">
          <h3>
            <i class="pi pi-building"></i>
            StaffManager Login
          </ng-template>
          <p>Content</p></ng-template>

        <ng-template pTemplate="content">
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <div class="p-field" appearance="outline" class="full-width">
              <label>Email</label>
              <input pInputText type="email" formControlName="email" required>
              <i class="pi pi-circle"></i>
              <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                Email is required
              </mat-error>
              <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                Please enter a valid email
              </mat-error>
            </div>

            <div class="p-field" appearance="outline" class="full-width">
              <label>Password</label>
              <input pInputText [type]="hidePassword ? 'password' : 'text'" formControlName="password" required>
              <p-button [text]="true" matSuffix (click)="hidePassword = !hidePassword" type="button">
                <i class="pi pi-circle"></i>
              </p-button>
              <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                Password is required
              </mat-error>
            </div>

            <p-button color="primary" type="submit"
                    class="full-width login-button modern-btn"
                    [disabled]="loginForm.invalid || loading"
                    [@buttonPress]>
              <p-progressSpinner></p-progressSpinner>
              <span *ngIf="!loading">Sign In</span>
            </p-button>
          </form>

          <p-divider class="divider">OR</p-divider>

          <p-button [outlined]="true" class="full-width google-button"
                  (click)="signInWithGoogle()"
                  [disabled]="loading">
            <i class="pi pi-circle"></i>
            Sign in with Google
          </p-button></ng-template>

        <ng-template pTemplate="footer">
          <p>Don't have an account?
            <a p-button color="primary" routerLink="/auth/register">Sign up</a>
          </p></ng-template></p-card>
    </div>
  `,
  styles: [`
    .login-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: var(--sm-spacing-lg);
      background: linear-gradient(135deg,
        var(--sm-primary-main) 0%,
        var(--sm-secondary-main) 50%,
        var(--sm-accent-main) 100%);
      position: relative;
      overflow: hidden;
    }

    .login-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.02) 100%);
      pointer-events: none;
    }

    .login-card {
      width: 100%;
      max-width: 420px;
      padding: var(--sm-spacing-2xl);
      border-radius: var(--sm-border-radius-2xl);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      position: relative;
      z-index: 1;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .login-button {
      height: 48px;
      margin: var(--sm-spacing-lg) 0;
      border-radius: var(--sm-border-radius-lg);
      font-weight: 600;
      letter-spacing: 0.025em;
      transition: all var(--sm-transition-fast);
      box-shadow: var(--sm-shadow-md);
    }

    .login-button:hover:not([disabled]) {
      transform: translateY(-2px);
      box-shadow: var(--sm-shadow-lg);
    }

    .google-button {
      height: 48px;
      margin-bottom: var(--sm-spacing-md);
      border-radius: var(--sm-border-radius-lg);
      transition: all var(--sm-transition-fast);
    }

    .google-button:hover {
      transform: translateY(-1px);
      box-shadow: var(--sm-shadow-sm);
    }

    .divider {
      margin: 20px 0;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: var(--sm-spacing-sm);
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--sm-text-primary);
    }

    .title-icon {
      color: var(--sm-primary-main);
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    mat-card-subtitle {
      color: var(--sm-text-secondary);
      margin-top: var(--sm-spacing-xs);
    }

    mat-card-actions p {
      margin: 0;
      text-align: center;
      color: var(--sm-text-secondary);
    }

    .divider {
      margin: var(--sm-spacing-lg) 0;
      opacity: 0.6;
    }

    // Form field enhancements
    mat-form-field {
      .mat-mdc-form-field-outline {
        border-radius: var(--sm-border-radius-md);
      }
    }

    // Responsive design
    @media (max-width: 600px) {
      .login-container {
        padding: var(--sm-spacing-md);
      }

      .login-card {
        padding: var(--sm-spacing-lg);
        max-width: 100%;
      }
    }
  `]
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private snackBar = inject(MessageService);
  private auth = inject(Auth);
  private firestore = inject(Firestore);
  private firebaseContext = inject(FirebaseContextService);

  loginForm: FormGroup;
  loading = false;
  hidePassword = true;

  constructor() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  async onSubmit(): Promise<void> {
    if (this.loginForm.valid && !this.loading) {
      this.loading = true;
      const { email, password } = this.loginForm.value;

      console.log('🔐 Attempting login with:', email);

      try {
        // Use AuthService for login
        this.authService.signInWithEmail(email, password).subscribe({
          next: async (user) => {
            console.log('✅ Login successful:', user);

            if (user) {
              // Check if profile exists, create if missing using FirebaseContextService
              this.firebaseContext.getDocument(`users/${user.uid}`).subscribe({
                next: (docSnap) => {
                  if (!docSnap.exists()) {
                    console.log('📝 Creating missing user profile...');
                    const userProfile = {
                      uid: user.uid,
                      email: user.email!,
                      displayName: user.displayName || 'User',
                      role: 'admin',
                      businessIds: [],
                      primaryBusinessId: '',
                      createdAt: new Date(),
                      lastLoginAt: new Date()
                    };

                    this.firebaseContext.setDocument(`users/${user.uid}`, userProfile).subscribe({
                      next: () => {
                        console.log('✅ User profile created successfully');
                      },
                      error: (error) => {
                        console.error('❌ Error creating user profile:', error);
                      }
                    });
                  } else {
                    console.log('✅ User profile found');
                  }
                },
                error: (error) => {
                  console.error('❌ Error checking user profile:', error);
                }
              });

              this.loading = false;
              this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
              this.router.navigate(['/dashboard']);
            } else {
              this.loading = false;
              this.snackBar.open('Login failed: No user returned', 'Close', { duration: 3000 });
            }
          },
          error: (error) => {
            this.loading = false;
            console.error('❌ Login error:', error);
            this.snackBar.open(this.getErrorMessage(error), 'Close', { duration: 5000 });
          }
        });
      } catch (error: any) {
        this.loading = false;
        console.error('❌ Login error:', error);
        this.snackBar.open(this.getErrorMessage(error), 'Close', { duration: 5000 });
      }
    }
  }

  signInWithGoogle(): void {
    if (!this.loading) {
      this.loading = true;
      console.log('🔐 Attempting Google sign-in...');

      this.authService.signInWithGoogle().subscribe({
        next: (user) => {
          this.loading = false;
          console.log('✅ Google sign-in successful:', user);
          if (user) {
            this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
            this.router.navigate(['/dashboard']);
          }
        },
        error: (error) => {
          this.loading = false;
          console.error('❌ Google sign-in error:', error);
          this.snackBar.open(this.getErrorMessage(error), 'Close', { duration: 5000 });
        }
      });
    }
  }

  private getErrorMessage(error: any): string {
    switch (error.code) {
      case 'auth/user-not-found':
        return 'No account found with this email address.';
      case 'auth/wrong-password':
        return 'Incorrect password.';
      case 'auth/invalid-email':
        return 'Invalid email address.';
      case 'auth/user-disabled':
        return 'This account has been disabled.';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      default:
        return 'An error occurred during login. Please try again.';
    }
  }
}
