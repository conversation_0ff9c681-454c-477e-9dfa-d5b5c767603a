import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ChipModule } from 'primeng/chip';
import { BadgeModule } from 'primeng/badge';
import { TooltipModule } from 'primeng/tooltip';
import { RouterModule } from '@angular/router';
import { Observable } from 'rxjs';

import { StaffService } from '../services/staff.service';
import { StaffMember, StaffListResponse } from '../models/staff.model';

@Component({
  selector: 'app-staff-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    CardModule,
    ButtonModule,
    TableModule,
    InputTextModule,
    ChipModule,
    BadgeModule,
    TooltipModule
  ],
  template: `
    <div class="staff-list-container">
      <!-- Header -->
      <div class="staff-header">
        <div class="header-left">
          <h1>
            <i class="pi pi-users"></i>
            Staff Directory
          </h1>
          <p>Manage your team members and their information</p>
        </div>
        <div class="header-actions">
          <p-button routerLink="/staff/new">
            <i class="pi pi-user-plus"></i>
            Add Staff Member
          </p-button>
        </div>
      </div>

      <!-- Search and Filters -->
      <p-card class="filters-card">
        <ng-template pTemplate="content">
          <div class="filters-row">
            <div class="p-field search-field">
              <label>Search staff...</label>
              <input pInputText
                     placeholder="Name, email, position..."
                     (input)="onSearchChange($event)">
              <i class="pi pi-search"></i>
            </div>

            <p-button outlined="true">
              <i class="pi pi-filter"></i>
              Filters
            </p-button>

            <p-button outlined="true">
              <i class="pi pi-download"></i>
              Export
            </p-button>
          </div>
        </ng-template>
      </p-card>

      <!-- Staff Stats -->
      <div class="stats-row">
        <p-card class="stat-card">
          <ng-template pTemplate="content">
            <div class="stat-content">
              <i class="pi pi-users"></i>
              <div class="stat-text">
                <h3>{{ (staffData$ | async)?.pagination?.totalCount || 0 }}</h3>
                <p>Total Staff</p>
              </div>
            </div></ng-template></p-card>

        <p-card class="stat-card">
          <ng-template pTemplate="content">
            <div class="stat-content">
              <i class="pi pi-check-circle"></i>
              <div class="stat-text">
                <h3>{{ activeStaffCount }}</h3>
                <p>Active</p>
              </div>
            </div></ng-template></p-card>

        <p-card class="stat-card">
          <ng-template pTemplate="content">
            <div class="stat-content">
              <i class="pi pi-building"></i>
              <div class="stat-text">
                <h3>{{ departmentCount }}</h3>
                <p>Departments</p>
              </div>
            </div></ng-template></p-card>
      </div>

      <!-- Staff Table -->
      <p-card class="table-card">
        <ng-template pTemplate="content">
          <div class="table-container">
            <p-table [value]="(staffData$ | async)?.staff || []"
                     class="staff-table"
                     [paginator]="true"
                     [rows]="10"
                     [totalRecords]="(staffData$ | async)?.pagination?.totalCount || 0"
                     [loading]="false"
                     responsiveLayout="scroll">

              <!-- Staff Member Column -->
              <ng-template pTemplate="header">
                <tr>
                  <th>Staff Member</th>
                  <th>Position</th>
                  <th>Status</th>
                  <th>Type</th>
                  <th>Hire Date</th>
                  <th>Actions</th>
                </tr>
              </ng-template>

              <ng-template pTemplate="body" let-staff>
                <tr class="staff-row" [routerLink]="['/staff', staff.id]">
                  <td>
                    <div class="staff-info">
                      <div class="avatar">
                        <i class="pi pi-user"></i>
                      </div>
                      <div class="info">
                        <div class="name">{{ staff.firstName }} {{ staff.lastName }}</div>
                        <div class="email">{{ staff.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="position-info">
                      <div class="position">{{ staff.position }}</div>
                      <div class="department">{{ staff.department }}</div>
                    </div>
                  </td>
                  <td>
                    <p-chip [class]="'status-' + staff.status">
                      {{ staff.status | titlecase }}
                    </p-chip>
                  </td>
                  <td>
                    {{ staff.employmentType | titlecase }}
                  </td>
                  <td>
                    {{ staff.hireDate | date:'MMM d, y' }}
                  </td>
                  <td>
                    <div class="actions">
                      <p-button text="true" [routerLink]="['/staff', staff.id]" pTooltip="View Details">
                        <i class="pi pi-eye"></i>
                      </p-button>
                      <p-button text="true" [routerLink]="['/staff', staff.id, 'edit']" pTooltip="Edit">
                        <i class="pi pi-pencil"></i>
                      </p-button>
                      <p-button text="true" pTooltip="More Options">
                        <i class="pi pi-ellipsis-v"></i>
                      </p-button>
                    </div>
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </ng-template>
      </p-card>
    </div>
  `,
  styleUrls: ['./staff-list.component.scss']
})
export class StaffListComponent implements OnInit {
  staffData$: Observable<StaffListResponse>;
  displayedColumns = ['name', 'position', 'status', 'employmentType', 'hireDate', 'actions'];

  activeStaffCount = 0;
  departmentCount = 0;
  Math = Math;

  constructor(private staffService: StaffService) {
    this.staffData$ = this.staffService.paginatedStaff$;
  }

  ngOnInit(): void {
    // Load initial data and stats
    this.staffService.getActiveStaff().subscribe(activeStaff => {
      this.activeStaffCount = activeStaff.length;
    });

    this.staffService.getDepartments().subscribe(departments => {
      this.departmentCount = departments.length;
    });
  }

  onSearchChange(event: any): void {
    const searchTerm = event.target.value;
    this.staffService.setFilter({ search: searchTerm });
  }
}
