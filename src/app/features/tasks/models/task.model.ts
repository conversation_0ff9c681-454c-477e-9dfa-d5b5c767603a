// Task and Checklist Management Models
// Enhanced models for comprehensive task and checklist functionality

export interface Task {
  id: string;
  title: string;
  description?: string;
  type: 'task' | 'checklist';
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'urgent' | 'critical';
  category: 'administrative' | 'customer-service' | 'training' | 'maintenance' | 'project' | 'safety' | 'compliance' | 'custom';

  // Assignment and ownership
  assignedTo: string[]; // Staff IDs
  assignedBy: string; // Manager/Admin ID
  createdBy: string;
  businessId: string;
  departmentId?: string;

  // Scheduling and deadlines
  dueDate?: Date;
  startDate?: Date;
  completedDate?: Date;
  estimatedDuration?: number; // minutes
  actualDuration?: number; // minutes

  // Recurrence settings
  isRecurring?: boolean;
  recurrencePattern?: RecurrencePattern;
  recurrenceEndDate?: Date;
  parentTaskId?: string; // For recurring task instances

  // Progress and completion
  progress: number; // 0-100
  completionRequirements?: CompletionRequirement[];
  verificationRequired: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;

  // Metadata and organization
  tags?: string[];
  location?: string;
  attachments?: TaskAttachment[];
  comments?: TaskComment[];
  dependencies?: string[]; // Task IDs that must be completed first

  // AI and automation
  aiGenerated?: boolean;
  aiSuggestions?: AISuggestion[];
  automationRules?: AutomationRule[];

  // Calendar integration
  calendarEventId?: string;
  syncWithCalendar: boolean;
  reminderSettings?: ReminderSettings;

  // Audit trail
  createdAt: Date;
  updatedAt: Date;
  lastModifiedBy: string;
  statusHistory?: StatusChange[];
}

export interface ChecklistItem {
  id: string;
  title: string;
  description?: string;
  isCompleted: boolean;
  completedBy?: string;
  completedAt?: Date;
  isRequired: boolean;
  order: number;

  // Verification and validation
  requiresVerification: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;
  verificationNotes?: string;

  // File uploads and evidence
  attachments?: TaskAttachment[];
  photoRequired?: boolean;
  signatureRequired?: boolean;

  // Sub-items for complex checklists
  subItems?: ChecklistSubItem[];

  // AI assistance
  aiGenerated?: boolean;
  aiInstructions?: string;
}

export interface ChecklistSubItem {
  id: string;
  title: string;
  isCompleted: boolean;
  completedBy?: string;
  completedAt?: Date;
  order: number;
}

export interface Checklist extends Omit<Task, 'type'> {
  type: 'checklist';
  items: ChecklistItem[];
  completionPercentage: number;
  allowPartialCompletion: boolean;
  requireAllItems: boolean;

  // Template settings
  isTemplate: boolean;
  templateCategory?: string;
  templateTags?: string[];
}

export interface RecurrencePattern {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';
  interval: number; // Every X days/weeks/months
  daysOfWeek?: number[]; // 0-6 (Sunday-Saturday)
  dayOfMonth?: number; // 1-31
  weekOfMonth?: number; // 1-4
  monthOfYear?: number; // 1-12
  endDate?: Date;
  maxOccurrences?: number;
}

export interface CompletionRequirement {
  type: 'file-upload' | 'photo' | 'signature' | 'comment' | 'verification' | 'time-tracking';
  isRequired: boolean;
  description: string;
  metadata?: any;
}

export interface TaskAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  uploadedBy: string;
  uploadedAt: Date;
  description?: string;
}

export interface TaskComment {
  id: string;
  userId: string;
  userName: string;
  userRole: string;
  content: string;
  type: 'comment' | 'status-update' | 'verification' | 'ai-suggestion';
  createdAt: Date;
  editedAt?: Date;
  attachments?: TaskAttachment[];
  mentions?: string[]; // User IDs mentioned in comment
}

export interface StatusChange {
  fromStatus: string;
  toStatus: string;
  changedBy: string;
  changedAt: Date;
  reason?: string;
  notes?: string;
}

export interface AISuggestion {
  id: string;
  type: 'task-optimization' | 'scheduling' | 'resource-allocation' | 'completion-help';
  suggestion: string;
  confidence: number; // 0-1
  reasoning: string;
  createdAt: Date;
  appliedAt?: Date;
  appliedBy?: string;
  feedback?: 'helpful' | 'not-helpful' | 'partially-helpful';
}

export interface AutomationRule {
  id: string;
  trigger: 'status-change' | 'due-date' | 'completion' | 'assignment';
  condition: any;
  action: 'notify' | 'assign' | 'update-status' | 'create-task' | 'send-reminder';
  actionData: any;
  isActive: boolean;
}

export interface ReminderSettings {
  enabled: boolean;
  reminders: Reminder[];
}

export interface Reminder {
  type: 'email' | 'push' | 'sms' | 'in-app';
  timing: 'immediate' | 'minutes' | 'hours' | 'days' | 'weeks';
  value: number; // How many minutes/hours/days before due date
  message?: string;
  recipients?: string[]; // Override default assignees
}

// Task Management Analytics and Reporting
export interface TaskAnalytics {
  businessId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };

  // Overall metrics
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  averageCompletionTime: number; // hours
  completionRate: number; // percentage

  // By category
  tasksByCategory: { [category: string]: number };
  completionByCategory: { [category: string]: number };

  // By staff member
  tasksByStaff: TaskStaffMetrics[];

  // By priority
  tasksByPriority: { [priority: string]: number };

  // Trends
  completionTrends: TrendData[];
  productivityTrends: TrendData[];

  // AI insights
  aiInsights: AIInsight[];
}

export interface TaskStaffMetrics {
  staffId: string;
  staffName: string;
  assignedTasks: number;
  completedTasks: number;
  overdueTasks: number;
  averageCompletionTime: number;
  completionRate: number;
  productivityScore: number;
}

export interface TrendData {
  date: Date;
  value: number;
  label: string;
}

export interface AIInsight {
  type: 'productivity' | 'workload' | 'efficiency' | 'recommendation';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  actionable: boolean;
  suggestedActions?: string[];
  confidence: number;
  generatedAt: Date;
}

// Task Templates for common workflows
export interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'task' | 'checklist';

  // Template data
  templateData: Partial<Task | Checklist>;

  // Usage and metadata
  businessId: string;
  isPublic: boolean; // Available to all businesses
  usageCount: number;
  rating: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;

  // AI enhancement
  aiOptimized: boolean;
  aiSuggestions?: string[];
}

// Notification and Communication
export interface TaskNotification {
  id: string;
  taskId: string;
  recipientId: string;
  type: 'assignment' | 'due-soon' | 'overdue' | 'completed' | 'verification-needed' | 'comment';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
  readAt?: Date;
  actionUrl?: string;
  metadata?: any;
}

// Integration with Calendar and Staff modules
export interface TaskCalendarEvent {
  taskId: string;
  calendarEventId: string;
  syncStatus: 'synced' | 'pending' | 'error';
  lastSyncAt: Date;
  syncError?: string;
}

export interface TaskStaffAssignment {
  taskId: string;
  staffId: string;
  assignedAt: Date;
  assignedBy: string;
  role: 'assignee' | 'reviewer' | 'approver' | 'observer';
  notificationPreferences: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

// Task Filters and Search
export interface TaskFilter {
  status?: string[];
  priority?: string[];
  category?: string[];
  assignedTo?: string[];
  assignedBy?: string[];
  businessId?: string;
  departmentId?: string;
  tags?: string[];
  dueDateRange?: {
    start?: Date;
    end?: Date;
  };
  createdDateRange?: {
    start?: Date;
    end?: Date;
  };
  searchText?: string;
  includeCompleted?: boolean;
  includeArchived?: boolean;
}

export interface TaskSortOptions {
  field: 'title' | 'dueDate' | 'priority' | 'status' | 'createdAt' | 'updatedAt' | 'progress';
  direction: 'asc' | 'desc';
}

// Bulk Operations
export interface BulkTaskOperation {
  operation: 'assign' | 'update-status' | 'update-priority' | 'add-tags' | 'set-due-date' | 'delete';
  taskIds: string[];
  data: any;
  performedBy: string;
  performedAt: Date;
}

// Task Import/Export
export interface TaskImportData {
  tasks: Partial<Task>[];
  checklists: Partial<Checklist>[];
  templates: Partial<TaskTemplate>[];
  businessId: string;
  importedBy: string;
  importedAt: Date;
}

export interface TaskExportOptions {
  format: 'json' | 'csv' | 'excel';
  includeCompleted: boolean;
  includeArchived: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  fields: string[];
}
