import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

/**
 * StaffManager Theme Configuration
 * Based on the design patterns from previous iterations
 */

export interface StaffManagerTheme {
  primary: {
    main: string;
    light: string;
    dark: string;
    contrastText: string;
  };
  secondary: {
    main: string;
    light: string;
    dark: string;
    contrastText: string;
  };
  accent: {
    main: string;
    light: string;
    dark: string;
  };
  background: {
    default: string;
    paper: string;
    surface: string;
  };
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  spacing: {
    unit: number;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    small: string;
    medium: string;
    large: string;
  };
  shadows: {
    light: string;
    medium: string;
    heavy: string;
  };
}

export const STAFFMANAGER_LIGHT_THEME: StaffManagerTheme = {
  primary: {
    main: '#1976d2',      // Blue 700 - Professional and trustworthy
    light: '#42a5f5',     // Blue 400 - Lighter variant
    dark: '#1565c0',      // Blue 800 - Darker variant
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#7c3aed',      // Purple 600 - Modern purple
    light: '#a855f7',     // Purple 500 - Lighter variant
    dark: '#5b21b6',      // Purple 700 - Darker variant
    contrastText: '#ffffff',
  },
  accent: {
    main: '#f59e0b',      // Amber 500 - Warm accent
    light: '#fbbf24',     // Amber 400 - Lighter variant
    dark: '#d97706',      // Amber 600 - Darker variant
  },
  background: {
    default: '#f8fafc',   // Slate 50 - Clean, modern background
    paper: '#ffffff',     // Pure white for cards/surfaces
    surface: '#f1f5f9',   // Slate 100 - Subtle surface variation
  },
  text: {
    primary: '#0f172a',   // Slate 900 - High contrast text
    secondary: '#64748b', // Slate 500 - Medium contrast text
    disabled: '#94a3b8',  // Slate 400 - Low contrast text
  },
  spacing: {
    unit: 8,
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
  },
  shadows: {
    light: '0 2px 4px rgba(0,0,0,0.1)',
    medium: '0 4px 8px rgba(0,0,0,0.12)',
    heavy: '0 8px 16px rgba(0,0,0,0.15)',
  },
};

export const STAFFMANAGER_DARK_THEME: StaffManagerTheme = {
  primary: {
    main: '#3b82f6',      // Blue 500 - Vibrant blue for dark mode
    light: '#60a5fa',     // Blue 400 - Lighter variant
    dark: '#2563eb',      // Blue 600 - Darker variant
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#8b5cf6',      // Violet 500 - Modern purple for dark mode
    light: '#a78bfa',     // Violet 400 - Lighter variant
    dark: '#7c3aed',      // Violet 600 - Darker variant
    contrastText: '#ffffff',
  },
  accent: {
    main: '#fbbf24',      // Amber 400 - Warm accent for dark mode
    light: '#fcd34d',     // Amber 300 - Lighter variant
    dark: '#f59e0b',      // Amber 500 - Darker variant
  },
  background: {
    default: '#0f172a',   // Slate 900 - Deep dark background
    paper: '#1e293b',     // Slate 800 - Card/surface background
    surface: '#334155',   // Slate 700 - Elevated surface
  },
  text: {
    primary: '#f8fafc',   // Slate 50 - High contrast white text
    secondary: '#cbd5e1', // Slate 300 - Medium contrast text
    disabled: '#64748b',  // Slate 500 - Low contrast text
  },
  spacing: {
    unit: 8,
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
  },
  shadows: {
    light: '0 2px 4px rgba(0,0,0,0.3)',
    medium: '0 4px 8px rgba(0,0,0,0.4)',
    heavy: '0 8px 16px rgba(0,0,0,0.5)',
  },
};

@Injectable({
  providedIn: 'root'
})
export class StaffManagerThemeService {
  private currentTheme: StaffManagerTheme = STAFFMANAGER_LIGHT_THEME;
  private isDarkMode = false;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  getCurrentTheme(): StaffManagerTheme {
    return this.currentTheme;
  }

  toggleTheme(): void {
    this.isDarkMode = !this.isDarkMode;
    this.currentTheme = this.isDarkMode ? STAFFMANAGER_DARK_THEME : STAFFMANAGER_LIGHT_THEME;
    this.applyThemeToDocument();
  }

  setTheme(isDark: boolean): void {
    this.isDarkMode = isDark;
    this.currentTheme = isDark ? STAFFMANAGER_DARK_THEME : STAFFMANAGER_LIGHT_THEME;
    this.applyThemeToDocument();
  }

  private applyThemeToDocument(): void {
    // CRITICAL FIX: Only apply theme changes in browser environment
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const root = document.documentElement;
    const theme = this.currentTheme;

    // Apply CSS custom properties
    root.style.setProperty('--sm-primary-main', theme.primary.main);
    root.style.setProperty('--sm-primary-light', theme.primary.light);
    root.style.setProperty('--sm-primary-dark', theme.primary.dark);
    root.style.setProperty('--sm-secondary-main', theme.secondary.main);
    root.style.setProperty('--sm-accent-main', theme.accent.main);
    root.style.setProperty('--sm-background-default', theme.background.default);
    root.style.setProperty('--sm-background-paper', theme.background.paper);
    root.style.setProperty('--sm-text-primary', theme.text.primary);
    root.style.setProperty('--sm-text-secondary', theme.text.secondary);
    root.style.setProperty('--sm-spacing-md', theme.spacing.md);
    root.style.setProperty('--sm-border-radius-medium', theme.borderRadius.medium);
    root.style.setProperty('--sm-shadow-medium', theme.shadows.medium);

    // Toggle dark mode class
    if (this.isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }
  }

  isDark(): boolean {
    return this.isDarkMode;
  }
}
