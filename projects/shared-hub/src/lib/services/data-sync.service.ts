import { Injectable, inject } from '@angular/core';
import { Firestore, collection, doc, onSnapshot, query, where, orderBy, limit,
         addDoc, updateDoc, deleteDoc, serverTimestamp, Timestamp } from '@angular/fire/firestore';
import { Observable, BehaviorSubject, Subject, merge, fromEvent } from 'rxjs';
import { map, filter, distinctUntilChanged, debounceTime, shareReplay } from 'rxjs/operators';

export interface SyncEvent {
  type: 'create' | 'update' | 'delete';
  collection: string;
  documentId: string;
  data?: any;
  timestamp: Date;
  source: 'local' | 'remote';
}

export interface SyncStatus {
  isOnline: boolean;
  lastSync: Date | null;
  pendingChanges: number;
  syncErrors: string[];
}

@Injectable({
  providedIn: 'root'
})
export class DataSyncService {
  private firestore = inject(Firestore);

  // Sync status
  private syncStatusSubject = new BehaviorSubject<SyncStatus>({
    isOnline: navigator.onLine,
    lastSync: null,
    pendingChanges: 0,
    syncErrors: []
  });

  // Real-time sync events
  private syncEventsSubject = new Subject<SyncEvent>();

  // Offline queue for pending changes
  private offlineQueue: any[] = [];

  // Active listeners for cleanup
  private activeListeners = new Map<string, () => void>();

  // Public observables
  syncStatus$ = this.syncStatusSubject.asObservable();
  syncEvents$ = this.syncEventsSubject.asObservable();

  constructor() {
    this.initializeNetworkMonitoring();
    this.initializeOfflineSupport();
  }

  // Initialize network status monitoring
  private initializeNetworkMonitoring(): void {
    const online$ = fromEvent(window, 'online').pipe(map(() => true));
    const offline$ = fromEvent(window, 'offline').pipe(map(() => false));

    merge(online$, offline$).subscribe(isOnline => {
      this.updateSyncStatus({ isOnline });

      if (isOnline && this.offlineQueue.length > 0) {
        this.processOfflineQueue();
      }
    });
  }

  // Initialize offline support
  private initializeOfflineSupport(): void {
    // Enable Firestore offline persistence
    // This would be configured in the main app module
  }

  // Subscribe to real-time updates for a collection
  subscribeToCollection<T>(
    collectionName: string,
    filters?: { field: string; operator: any; value: any }[],
    orderByField?: string,
    limitCount?: number
  ): Observable<T[]> {
    let q = collection(this.firestore, collectionName);

    // Apply filters
    if (filters) {
      filters.forEach(filter => {
        q = query(q as any, where(filter.field, filter.operator, filter.value)) as any;
      });
    }

    // Apply ordering
    if (orderByField) {
      q = query(q as any, orderBy(orderByField)) as any;
    }

    // Apply limit
    if (limitCount) {
      q = query(q as any, limit(limitCount)) as any;
    }

    const listenerId = `${collectionName}_${Date.now()}`;

    return new Observable<T[]>(observer => {
      const unsubscribe = onSnapshot(q as any,
        (snapshot: any) => {
          const data = snapshot.docs.map((doc: any) => ({
            id: doc.id,
            ...doc.data(),
            // Convert Firestore timestamps to Date objects
            ...this.convertTimestamps(doc.data())
          })) as T[];

          observer.next(data);

          // Emit sync events for each document change
          snapshot.docChanges().forEach((change: any) => {
            this.syncEventsSubject.next({
              type: change.type === 'added' ? 'create' :
                    change.type === 'modified' ? 'update' : 'delete',
              collection: collectionName,
              documentId: change.doc.id,
              data: change.doc.data(),
              timestamp: new Date(),
              source: 'remote'
            });
          });

          this.updateSyncStatus({ lastSync: new Date() });
        },
        (error: any) => {
          console.error(`Error subscribing to ${collectionName}:`, error);
          this.addSyncError(error.message);
          observer.error(error);
        }
      );

      this.activeListeners.set(listenerId, unsubscribe);

      return () => {
        unsubscribe();
        this.activeListeners.delete(listenerId);
      };
    }).pipe(
      shareReplay(1)
    );
  }

  // Subscribe to a specific document
  subscribeToDocument<T>(collectionName: string, documentId: string): Observable<T | null> {
    const docRef = doc(this.firestore, collectionName, documentId);

    return new Observable<T | null>(observer => {
      const unsubscribe = onSnapshot(docRef,
        (docSnap) => {
          if (docSnap.exists()) {
            const data = {
              id: docSnap.id,
              ...docSnap.data(),
              ...this.convertTimestamps(docSnap.data())
            } as T;
            observer.next(data);
          } else {
            observer.next(null);
          }

          this.updateSyncStatus({ lastSync: new Date() });
        },
        (error) => {
          console.error(`Error subscribing to document ${documentId}:`, error);
          this.addSyncError(error.message);
          observer.error(error);
        }
      );

      return () => unsubscribe();
    }).pipe(
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  // Create a new document
  async createDocument(collectionName: string, data: any): Promise<string> {
    try {
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      if (this.syncStatusSubject.value.isOnline) {
        const docRef = await addDoc(collection(this.firestore, collectionName), docData);

        this.syncEventsSubject.next({
          type: 'create',
          collection: collectionName,
          documentId: docRef.id,
          data: docData,
          timestamp: new Date(),
          source: 'local'
        });

        return docRef.id;
      } else {
        // Queue for offline processing
        const tempId = this.generateTempId();
        this.addToOfflineQueue('create', collectionName, tempId, docData);
        return tempId;
      }
    } catch (error) {
      console.error('Error creating document:', error);
      this.addSyncError(`Failed to create document: ${error}`);
      throw error;
    }
  }

  // Update an existing document
  async updateDocument(collectionName: string, documentId: string, data: any): Promise<void> {
    try {
      const updateData = {
        ...data,
        updatedAt: serverTimestamp()
      };

      if (this.syncStatusSubject.value.isOnline) {
        const docRef = doc(this.firestore, collectionName, documentId);
        await updateDoc(docRef, updateData);

        this.syncEventsSubject.next({
          type: 'update',
          collection: collectionName,
          documentId,
          data: updateData,
          timestamp: new Date(),
          source: 'local'
        });
      } else {
        // Queue for offline processing
        this.addToOfflineQueue('update', collectionName, documentId, updateData);
      }
    } catch (error) {
      console.error('Error updating document:', error);
      this.addSyncError(`Failed to update document: ${error}`);
      throw error;
    }
  }

  // Delete a document
  async deleteDocument(collectionName: string, documentId: string): Promise<void> {
    try {
      if (this.syncStatusSubject.value.isOnline) {
        const docRef = doc(this.firestore, collectionName, documentId);
        await deleteDoc(docRef);

        this.syncEventsSubject.next({
          type: 'delete',
          collection: collectionName,
          documentId,
          timestamp: new Date(),
          source: 'local'
        });
      } else {
        // Queue for offline processing
        this.addToOfflineQueue('delete', collectionName, documentId, null);
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      this.addSyncError(`Failed to delete document: ${error}`);
      throw error;
    }
  }

  // Process offline queue when connection is restored
  private async processOfflineQueue(): Promise<void> {
    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    for (const operation of queue) {
      try {
        switch (operation.type) {
          case 'create':
            await this.createDocument(operation.collection, operation.data);
            break;
          case 'update':
            await this.updateDocument(operation.collection, operation.documentId, operation.data);
            break;
          case 'delete':
            await this.deleteDocument(operation.collection, operation.documentId);
            break;
        }
      } catch (error) {
        console.error('Error processing offline operation:', error);
        // Re-queue failed operations
        this.offlineQueue.push(operation);
      }
    }

    this.updateSyncStatus({ pendingChanges: this.offlineQueue.length });
  }

  // Add operation to offline queue
  private addToOfflineQueue(type: string, collection: string, documentId: string, data: any): void {
    this.offlineQueue.push({
      type,
      collection,
      documentId,
      data,
      timestamp: new Date()
    });

    this.updateSyncStatus({ pendingChanges: this.offlineQueue.length });
  }

  // Convert Firestore timestamps to Date objects
  private convertTimestamps(data: any): any {
    const converted: any = {};

    Object.keys(data).forEach(key => {
      const value = data[key];
      if (value instanceof Timestamp) {
        converted[key] = value.toDate();
      } else if (value && typeof value === 'object' && value.seconds) {
        // Handle server timestamps
        converted[key] = new Date(value.seconds * 1000);
      }
    });

    return converted;
  }

  // Update sync status
  private updateSyncStatus(updates: Partial<SyncStatus>): void {
    const currentStatus = this.syncStatusSubject.value;
    this.syncStatusSubject.next({ ...currentStatus, ...updates });
  }

  // Add sync error
  private addSyncError(error: string): void {
    const currentStatus = this.syncStatusSubject.value;
    const syncErrors = [...currentStatus.syncErrors, error].slice(-10); // Keep last 10 errors
    this.updateSyncStatus({ syncErrors });
  }

  // Generate temporary ID for offline operations
  private generateTempId(): string {
    return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup all active listeners
  destroy(): void {
    this.activeListeners.forEach(unsubscribe => unsubscribe());
    this.activeListeners.clear();
  }
}
