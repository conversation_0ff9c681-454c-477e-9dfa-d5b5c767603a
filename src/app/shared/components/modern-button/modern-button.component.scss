.modern-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--sm-spacing-sm);
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-weight: 600;
  letter-spacing: 0.025em;
  text-decoration: none;
  transition: all var(--sm-transition-fast);
  overflow: hidden;
  outline: none;
  user-select: none;
  
  // Base sizes
  &.size-small {
    padding: var(--sm-spacing-xs) var(--sm-spacing-md);
    font-size: 0.875rem;
    min-height: 32px;
    border-radius: var(--sm-border-radius-sm);
  }
  
  &.size-medium {
    padding: var(--sm-spacing-sm) var(--sm-spacing-lg);
    font-size: 0.9375rem;
    min-height: 40px;
    border-radius: var(--sm-border-radius-md);
  }
  
  &.size-large {
    padding: var(--sm-spacing-md) var(--sm-spacing-xl);
    font-size: 1rem;
    min-height: 48px;
    border-radius: var(--sm-border-radius-lg);
  }
  
  // Variants
  &.variant-filled {
    &.color-primary {
      background: var(--sm-primary-main);
      color: white;
      
      &:hover:not(.disabled) {
        background: var(--sm-primary-dark);
        transform: translateY(-1px);
      }
    }
    
    &.color-secondary {
      background: var(--sm-secondary-main);
      color: white;
      
      &:hover:not(.disabled) {
        background: var(--sm-secondary-dark);
        transform: translateY(-1px);
      }
    }
    
    &.color-accent {
      background: var(--sm-accent-main);
      color: white;
      
      &:hover:not(.disabled) {
        background: var(--sm-accent-dark);
        transform: translateY(-1px);
      }
    }
    
    &.color-warn {
      background: #ef4444;
      color: white;
      
      &:hover:not(.disabled) {
        background: #dc2626;
        transform: translateY(-1px);
      }
    }
    
    &.color-success {
      background: #10b981;
      color: white;
      
      &:hover:not(.disabled) {
        background: #059669;
        transform: translateY(-1px);
      }
    }
  }
  
  &.variant-outlined {
    background: transparent;
    border: 2px solid;
    
    &.color-primary {
      border-color: var(--sm-primary-main);
      color: var(--sm-primary-main);
      
      &:hover:not(.disabled) {
        background: rgba(var(--sm-primary-main), 0.1);
        transform: translateY(-1px);
      }
    }
    
    &.color-secondary {
      border-color: var(--sm-secondary-main);
      color: var(--sm-secondary-main);
      
      &:hover:not(.disabled) {
        background: rgba(var(--sm-secondary-main), 0.1);
        transform: translateY(-1px);
      }
    }
  }
  
  &.variant-text {
    background: transparent;
    
    &.color-primary {
      color: var(--sm-primary-main);
      
      &:hover:not(.disabled) {
        background: rgba(var(--sm-primary-main), 0.1);
      }
    }
    
    &.color-secondary {
      color: var(--sm-secondary-main);
      
      &:hover:not(.disabled) {
        background: rgba(var(--sm-secondary-main), 0.1);
      }
    }
  }
  
  &.variant-fab {
    border-radius: 50%;
    width: 56px;
    height: 56px;
    padding: 0;
    
    &.size-small {
      width: 40px;
      height: 40px;
    }
    
    &.size-large {
      width: 72px;
      height: 72px;
    }
  }
  
  &.variant-icon {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;
    background: transparent;
    
    &:hover:not(.disabled) {
      background: rgba(var(--sm-primary-main), 0.1);
    }
  }
  
  // Modifiers
  &.icon-only {
    .button-text {
      display: none;
    }
  }
  
  &.full-width {
    width: 100%;
  }
  
  &.rounded {
    border-radius: var(--sm-border-radius-full);
  }
  
  &.elevated {
    box-shadow: var(--sm-shadow-sm);
    
    &:hover:not(.disabled) {
      box-shadow: var(--sm-shadow-md);
    }
    
    &:active:not(.disabled) {
      box-shadow: var(--sm-shadow-xs);
    }
  }
  
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
  
  &.loading {
    cursor: wait;
    
    .button-text {
      opacity: 0.7;
    }
  }
  
  // Focus states
  &:focus-visible {
    outline: 2px solid var(--sm-primary-main);
    outline-offset: 2px;
  }
  
  // Active states
  &:active:not(.disabled) {
    transform: translateY(0) scale(0.98);
  }
}

// Icon positioning
.icon-left {
  order: -1;
}

.icon-right {
  order: 1;
}

// Button spinner
.button-spinner {
  ::ng-deep circle {
    stroke: currentColor;
  }
}

// Ripple effect
.button-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  pointer-events: none;
  
  &.ripple-active {
    animation: ripple 0.6s ease-out;
  }
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

// Dark theme support
.dark-theme {
  .modern-button {
    &.variant-filled {
      &.color-primary {
        background: var(--sm-primary-light);
        
        &:hover:not(.disabled) {
          background: var(--sm-primary-main);
        }
      }
      
      &.color-secondary {
        background: var(--sm-secondary-light);
        
        &:hover:not(.disabled) {
          background: var(--sm-secondary-main);
        }
      }
    }
    
    &.variant-outlined {
      &.color-primary {
        border-color: var(--sm-primary-light);
        color: var(--sm-primary-light);
        
        &:hover:not(.disabled) {
          background: rgba(59, 130, 246, 0.1);
        }
      }
    }
    
    &.variant-text {
      &.color-primary {
        color: var(--sm-primary-light);
        
        &:hover:not(.disabled) {
          background: rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .modern-button {
    &.size-medium {
      min-height: 44px; // Touch-friendly
    }
    
    &.size-large {
      min-height: 48px;
    }
    
    &.variant-fab {
      width: 48px;
      height: 48px;
      
      &.size-large {
        width: 56px;
        height: 56px;
      }
    }
  }
}
