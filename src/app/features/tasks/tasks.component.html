<div class="tasks-container">
  <!-- Header Section -->
  <div class="tasks-header">
    <div class="header-content">
      <div class="title-section">
        <h1>
          <i class="pi pi-circle"></i>
          Tasks & Checklists
        </h1>
        <p class="subtitle">Manage tasks, checklists, and team productivity with AI assistance</p>
      </div>

      <div class="header-actions">
        <p-button severity="primary" (click)="createTask()">
          <i class="pi pi-circle"></i>
          Create Task
        </p-button>
        <p-button severity="secondary" (click)="createChecklist()">
          <i class="pi pi-circle"></i>
          Create Checklist
        </p-button>
        <p-button outlined="true" severity="primary" (click)="getAITaskSuggestions()">
          <i class="pi pi-brain"></i>
          AI Suggestions
        </p-button>
      </div>

    <!-- Search and Filters -->
    <div class="filters-section">
      <mat-form-field appearance="outline" class="search-field">
        <mat-label>Search tasks and checklists</mat-label>
        <input pInputText [(ngModel)]="searchText" placeholder="Search by title, description, or assignee">
        <i class="pi pi-circle"></i>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Status</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let status of statusOptions" [value]="status.value">
            <p-chip [color]="status.color">{{ status.label }}</p-chip>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Priority</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let priority of priorityOptions" [value]="priority.value">
            <p-chip [color]="priority.color">{{ priority.label }}</p-chip>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Category</mat-label>
        <mat-select multiple>
          <mat-option *ngFor="let category of categoryOptions" [value]="category.value">
            {{ category.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <p-button text="true" (click)="clearFilters()" matTooltip="Clear all filters">
        <i class="pi pi-circle"></i>
      </p-button>
    </div>

  <!-- Loading Indicator -->
  <mat-progress-bar *ngIf="isLoading()" mode="indeterminate" class="loading-bar"></mat-progress-bar>

  <!-- Main Content Tabs -->
  <mat-tab-group [(selectedIndex)]="selectedTab" class="tasks-tabs">

    <!-- Tasks Tab -->
    <mat-tab label="Tasks">
      <ng-template matTabContent>
        <div class="tab-content">

          <!-- Tasks Summary Cards -->
          <div class="summary-cards" *ngIf="tasks$ | async as tasks">
            <p-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <i class="pi pi-circle"></i>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'pending').length }}</h3>
                    <p>Pending Tasks</p>
                  </div></p-card>

            <p-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <i class="pi pi-circle"></i>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'in-progress').length }}</h3>
                    <p>In Progress</p>
                  </div></p-card>

            <p-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <i class="pi pi-circle"></i>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'completed').length }}</h3>
                    <p>Completed</p>
                  </div></p-card>

            <p-card class="summary-card">
              <ng-template pTemplate="content">
                <div class="summary-content">
                  <i class="pi pi-circle"></i>
                  <div class="summary-text">
                    <h3>{{ getTasksByStatus(tasks, 'overdue').length }}</h3>
                    <p>Overdue</p>
                  </div></ng-template></p-card>
          </div>

          <!-- Tasks Table -->
          <p-card class="tasks-table-card">
            <ng-template pTemplate="header">
              <ng-template pTemplate="title">
                <i class="pi pi-circle"></i>
                All Tasks
              <div class="table-actions">
                <p-button text="true" [matMenuTriggerFor]="sortMenu" matTooltip="Sort options">
                  <i class="pi pi-circle"></i>
                </p-button>
                <p-menu #sortMenu="matMenu">
                  <button mat-menu-item (click)="applySorting({field: 'title', direction: 'asc'})">
                    <i class="pi pi-circle"></i>
                    Title A-Z
                  </p-button>
                  <button mat-menu-item (click)="applySorting({field: 'dueDate', direction: 'asc'})">
                    <i class="pi pi-clock"></i>
                    Due Date
                  </p-button>
                  <button mat-menu-item (click)="applySorting({field: 'priority', direction: 'desc'})">
                    <i class="pi pi-circle"></i>
                    Priority
                  </p-button>
                  <button mat-menu-item (click)="applySorting({field: 'createdAt', direction: 'desc'})">
                    <i class="pi pi-circle"></i>
                    Created Date
                  </p-button>
                </p-menu>
              </div><ng-template pTemplate="content">
              <div class="table-container">
                <p-table [value]="(tasks$ | async) || []" class="tasks-table">

                  <!-- Title Column -->
                  <!-- Column: title -->
                    <ng-template pTemplate="header"><tr><th>Task</th>
                    <ng-template pTemplate="body" let-task><tr><td>
                      <div class="task-title-cell">
                        <h4>{{ task.title }}</h4>
                        <p *ngIf="task.description">{{ task.description | slice:0:100 }}{{ task.description.length > 100 ? '...' : '' }}</p>
                        <div class="task-tags" *ngIf="task.tags && task.tags.length > 0">
                          <p-chip *ngFor="let tag of task.tags | slice:0:3" class="task-tag">{{ tag }}</p-chip>
                          <span *ngIf="task.tags.length > 3" class="more-tags">+{{ task.tags.length - 3 }} more</span>
                        </div>
                    </td>
                  </ng-container>

                  <!-- Status Column -->
                  <!-- Column: status -->
                    <ng-template pTemplate="header"><tr><th>Status</th>
                    <ng-template pTemplate="body" let-task><tr><td>
                      <p-chip [color]="getStatusColor(task.status)">
                        {{ task.status | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <!-- Priority Column -->
                  <!-- Column: priority -->
                    <ng-template pTemplate="header"><tr><th>Priority</th>
                    <ng-template pTemplate="body" let-task><tr><td>
                      <p-chip [color]="getPriorityColor(task.priority)">
                        {{ task.priority | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <!-- Assigned To Column -->
                  <!-- Column: assignedTo -->
                    <ng-template pTemplate="header"><tr><th>Assigned To</th>
                    <ng-template pTemplate="body" let-task><tr><td>
                      <div class="assignees">
                        {{ formatStaffNames(task.assignedTo) | async }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Due Date Column -->
                  <!-- Column: dueDate -->
                    <ng-template pTemplate="header"><tr><th>Due Date</th>
                    <ng-template pTemplate="body" let-task><tr><td>
                      <div class="due-date" [class.overdue]="isTaskOverdue(task)">
                        <i class="pi pi-circle"></i>
                        {{ task.dueDate ? (task.dueDate | date:'mediumDate') : 'No due date' }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Progress Column -->
                  <!-- Column: progress -->
                    <ng-template pTemplate="header"><tr><th>Progress</th>
                    <ng-template pTemplate="body" let-task><tr><td>
                      <div class="progress-cell">
                        <mat-progress-bar [value]="task.progress" mode="determinate"></mat-progress-bar>
                        <span class="progress-text">{{ task.progress }}%</span>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <!-- Column: actions -->
                    <ng-template pTemplate="header"><tr><th>Actions</th>
                    <ng-template pTemplate="body" let-task><tr><td>
                      <p-button text="true" [matMenuTriggerFor]="taskMenu" matTooltip="Task actions">
                        <i class="pi pi-circle"></i>
                      </p-button>
                      <p-menu #taskMenu="matMenu">
                        <button mat-menu-item (click)="editTask(task)">
                          <i class="pi pi-circle"></i>
                          Edit
                        </p-button>
                        <button mat-menu-item>
                          <i class="pi pi-eye"></i>
                          View Details
                        </p-button>
                        <button mat-menu-item>
                          <i class="pi pi-circle"></i>
                          Duplicate
                        </p-button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item (click)="deleteTask(task)" class="delete-action">
                          <i class="pi pi-circle"></i>
                          Delete
                        </p-button>
                      </p-menu>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="taskColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: taskColumns;" class="task-row"></tr>
                </table>

                <!-- Empty State -->
                <div *ngIf="(tasks$ | async)?.length === 0" class="empty-state">
                  <i class="pi pi-circle"></i>
                  <h3>No tasks found</h3>
                  <p>Create your first task or adjust your filters to see results.</p>
                  <p-button severity="primary" (click)="createTask()">
                    <i class="pi pi-circle"></i>
                    Create Task
                  </p-button>
                </div></p-card>
        </div></ng-template>
    </mat-tab>

    <!-- Checklists Tab -->
    <mat-tab label="Checklists">
      <ng-template matTabContent>
        <div class="tab-content">
          <!-- Checklists content will be similar to tasks but with checklist-specific features -->
          <p-card class="checklists-table-card">
            <ng-template pTemplate="header">
              <ng-template pTemplate="title">
                <i class="pi pi-circle"></i>
                All Checklists
              <ng-template pTemplate="content">
              <div class="table-container">
                <p-table [value]="(checklists$ | async) || []" class="checklists-table">

                  <!-- Similar columns to tasks but with completion percentage -->
                  <!-- Column: title -->
                    <ng-template pTemplate="header"><tr><th>Checklist</th>
                    <ng-template pTemplate="body" let-checklist><tr><td>
                      <div class="checklist-title-cell">
                        <h4>{{ checklist.title }}</h4>
                        <p *ngIf="checklist.description">{{ checklist.description | slice:0:100 }}{{ checklist.description.length > 100 ? '...' : '' }}</p>
                        <div class="checklist-info">
                          <span class="item-count">{{ checklist.items?.length || 0 }} items</span>
                        </div>
                    </td>
                  </ng-container>

                  <!-- Column: status -->
                    <ng-template pTemplate="header"><tr><th>Status</th>
                    <ng-template pTemplate="body" let-checklist><tr><td>
                      <p-chip [color]="getStatusColor(checklist.status)">
                        {{ checklist.status | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <!-- Column: priority -->
                    <ng-template pTemplate="header"><tr><th>Priority</th>
                    <ng-template pTemplate="body" let-checklist><tr><td>
                      <p-chip [color]="getPriorityColor(checklist.priority)">
                        {{ checklist.priority | titlecase }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <!-- Column: assignedTo -->
                    <ng-template pTemplate="header"><tr><th>Assigned To</th>
                    <ng-template pTemplate="body" let-checklist><tr><td>
                      <div class="assignees">
                        {{ formatStaffNames(checklist.assignedTo) | async }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Column: dueDate -->
                    <ng-template pTemplate="header"><tr><th>Due Date</th>
                    <ng-template pTemplate="body" let-checklist><tr><td>
                      <div class="due-date" [class.overdue]="isChecklistOverdue(checklist)">
                        <i class="pi pi-circle"></i>
                        {{ checklist.dueDate ? (checklist.dueDate | date:'mediumDate') : 'No due date' }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Column: completion -->
                    <ng-template pTemplate="header"><tr><th>Completion</th>
                    <ng-template pTemplate="body" let-checklist><tr><td>
                      <div class="completion-cell">
                        <mat-progress-bar [value]="checklist.completionPercentage" mode="determinate"></mat-progress-bar>
                        <span class="completion-text">{{ checklist.completionPercentage }}%</span>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Column: actions -->
                    <ng-template pTemplate="header"><tr><th>Actions</th>
                    <ng-template pTemplate="body" let-checklist><tr><td>
                      <p-button text="true" [matMenuTriggerFor]="checklistMenu" matTooltip="Checklist actions">
                        <i class="pi pi-circle"></i>
                      </p-button>
                      <p-menu #checklistMenu="matMenu">
                        <button mat-menu-item (click)="editChecklist(checklist)">
                          <i class="pi pi-circle"></i>
                          Edit
                        </p-button>
                        <button mat-menu-item>
                          <i class="pi pi-eye"></i>
                          View Details
                        </p-button>
                        <button mat-menu-item>
                          <i class="pi pi-circle"></i>
                          Duplicate
                        </p-button>
                        <mat-divider></mat-divider>
                        <button mat-menu-item (click)="deleteChecklist(checklist)" class="delete-action">
                          <i class="pi pi-circle"></i>
                          Delete
                        </p-button>
                      </p-menu>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="checklistColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: checklistColumns;" class="checklist-row"></tr>
                </table>

                <!-- Empty State -->
                <div *ngIf="(checklists$ | async)?.length === 0" class="empty-state">
                  <i class="pi pi-circle"></i>
                  <h3>No checklists found</h3>
                  <p>Create your first checklist or adjust your filters to see results.</p>
                  <p-button severity="secondary" (click)="createChecklist()">
                    <i class="pi pi-circle"></i>
                    Create Checklist
                  </p-button>
                </div></p-card>
        </div></ng-template>
    </mat-tab>

    <!-- Analytics Tab -->
    <mat-tab label="Analytics">
      <ng-template matTabContent>
        <div class="tab-content">
          <div class="analytics-section">
            <p-card class="analytics-card">
              <ng-template pTemplate="header">
                <ng-template pTemplate="title">
                  <i class="pi pi-circle"></i>
                  Productivity Analytics
                <div class="analytics-actions">
                  <p-button severity="primary" (click)="analyzeProductivity()">
                    <i class="pi pi-brain"></i>
                    AI Analysis
                  </p-button>
                </div><ng-template pTemplate="content">
                <p>AI-powered productivity insights and recommendations will be displayed here.</p>
                <p>Click "AI Analysis" to generate personalized productivity recommendations.</p></p-card>
          </div></ng-template>
    </mat-tab>

  </mat-tab-group>
</div>
