import { Injectable } from '@angular/core';

/**
 * Modern Icon Service for StaffManager
 * Provides consistent iconography across the application
 */

export interface IconConfig {
  name: string;
  size?: number;
  color?: string;
  strokeWidth?: number;
  className?: string;
}

@Injectable({
  providedIn: 'root'
})
export class IconService {

  // Icon mapping for consistent usage across the app
  private iconMap: { [key: string]: string } = {
    // Navigation
    'dashboard': 'layout-dashboard',
    'staff': 'users',
    'calendar': 'calendar',
    'tasks': 'check-square',
    'goals': 'target',
    'time': 'clock',
    'settings': 'settings',
    'business': 'building',

    // Actions
    'add': 'plus',
    'edit': 'edit-3',
    'delete': 'trash-2',
    'save': 'save',
    'cancel': 'x',
    'search': 'search',
    'filter': 'filter',
    'sort': 'arrow-up-down',
    'refresh': 'refresh-cw',
    'download': 'download',
    'upload': 'upload',
    'copy': 'copy',
    'share': 'share-2',

    // UI Elements
    'menu': 'menu',
    'close': 'x',
    'back': 'arrow-left',
    'forward': 'arrow-right',
    'up': 'chevron-up',
    'down': 'chevron-down',
    'left': 'chevron-left',
    'right': 'chevron-right',
    'expand': 'expand',
    'collapse': 'minimize',

    // Status & Feedback
    'success': 'check-circle',
    'error': 'alert-circle',
    'warning': 'alert-triangle',
    'info': 'info',
    'loading': 'loader',
    'notification': 'bell',
    'message': 'message-circle',

    // User & Profile
    'user': 'user',
    'profile': 'user-circle',
    'team': 'users',
    'login': 'log-in',
    'logout': 'log-out',
    'account': 'user-cog',

    // Time & Schedule
    'schedule': 'calendar-days',
    'clock-in': 'clock',
    'clock-out': 'clock',
    'time-off': 'calendar-x',
    'overtime': 'clock',
    'break': 'pause-circle',

    // Business & Finance
    'payroll': 'dollar-sign',
    'reports': 'bar-chart-3',
    'analytics': 'trending-up',
    'revenue': 'line-chart',
    'expenses': 'trending-down',

    // Communication
    'chat': 'message-square',
    'email': 'mail',
    'phone': 'phone',
    'video': 'video',
    'announcement': 'megaphone',

    // Files & Documents
    'file': 'file',
    'folder': 'folder',
    'document': 'file-text',
    'image': 'image',
    'pdf': 'file-type',
    'excel': 'file-spreadsheet',

    // System & Technical
    'database': 'database',
    'api': 'code',
    'sync': 'refresh-cw',
    'backup': 'hard-drive',
    'security': 'shield',
    'permissions': 'key',

    // Widgets & Dashboard
    'widget': 'layout-grid',
    'chart': 'bar-chart',
    'graph': 'activity',
    'stats': 'pie-chart',
    'metrics': 'trending-up',

    // Mobile & PWA
    'mobile': 'smartphone',
    'tablet': 'tablet',
    'desktop': 'monitor',
    'qr-code': 'qr-code',
    'install': 'download-cloud',

    // Theme & Appearance
    'light-mode': 'sun',
    'dark-mode': 'moon',
    'theme': 'palette',
    'color': 'droplet',

    // Miscellaneous
    'help': 'help-circle',
    'external': 'external-link',
    'link': 'link',
    'bookmark': 'bookmark',
    'star': 'star',
    'heart': 'heart',
    'flag': 'flag',
    'tag': 'tag',
    'location': 'map-pin',
    'globe': 'globe'
  };

  /**
   * Get the Lucide icon name for a given semantic name
   */
  getIconName(semanticName: string): string {
    return this.iconMap[semanticName] || semanticName;
  }

  /**
   * Get icon configuration with defaults
   */
  getIconConfig(semanticName: string, overrides?: Partial<IconConfig>): IconConfig {
    const iconName = this.getIconName(semanticName);

    return {
      name: iconName,
      size: 24,
      strokeWidth: 2,
      className: 'sm-icon',
      ...overrides
    };
  }

  /**
   * Get icon SVG string (for dynamic usage)
   */
  getIconSvg(semanticName: string, config?: Partial<IconConfig>): string {
    const iconConfig = this.getIconConfig(semanticName, config);

    // This would typically integrate with Lucide's icon generation
    // For now, return a placeholder that can be replaced with actual Lucide integration
    return `<lucide-icon name="${iconConfig.name}" size="${iconConfig.size}" stroke-width="${iconConfig.strokeWidth}"></lucide-icon>`;
  }

  /**
   * Check if an icon exists in our mapping
   */
  hasIcon(semanticName: string): boolean {
    return semanticName in this.iconMap;
  }

  /**
   * Get all available icon names
   */
  getAvailableIcons(): string[] {
    return Object.keys(this.iconMap);
  }

  /**
   * Get icon categories for organization
   */
  getIconCategories(): { [category: string]: string[] } {
    return {
      navigation: ['dashboard', 'staff', 'calendar', 'tasks', 'goals', 'time', 'settings', 'business'],
      actions: ['add', 'edit', 'delete', 'save', 'cancel', 'search', 'filter', 'sort', 'refresh'],
      ui: ['menu', 'close', 'back', 'forward', 'up', 'down', 'left', 'right', 'expand', 'collapse'],
      status: ['success', 'error', 'warning', 'info', 'loading', 'notification', 'message'],
      user: ['user', 'profile', 'team', 'login', 'logout', 'account'],
      time: ['schedule', 'clock-in', 'clock-out', 'time-off', 'overtime', 'break'],
      business: ['payroll', 'reports', 'analytics', 'revenue', 'expenses'],
      communication: ['chat', 'email', 'phone', 'video', 'announcement'],
      files: ['file', 'folder', 'document', 'image', 'pdf', 'excel'],
      system: ['database', 'api', 'sync', 'backup', 'security', 'permissions'],
      widgets: ['widget', 'chart', 'graph', 'stats', 'metrics'],
      mobile: ['mobile', 'tablet', 'desktop', 'qr-code', 'install'],
      theme: ['light-mode', 'dark-mode', 'theme', 'color'],
      misc: ['help', 'external', 'link', 'bookmark', 'star', 'heart', 'flag', 'tag', 'location', 'globe']
    };
  }
}
