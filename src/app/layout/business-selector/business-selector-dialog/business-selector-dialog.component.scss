@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 8px 0 8px 0;
}

.dialog-search {
  display: flex;
  align-items: center;
  margin: 8px 0;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 6px 12px;
  box-shadow: 0 1px 4px rgba(25, 118, 210, 0.06);
  .mat-icon {
    margin-right: 8px;
    color: #1976d2;
  }
  input {
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    font-size: 1rem;
    padding: 6px 0;
  }
}

.dialog-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  .oneview-chip {
    margin-left: auto;
    font-weight: 500;
    height: 22px;
    background: linear-gradient(90deg, #1976d2 60%, #00bcd4 100%);
    color: #fff;
    animation: pulse 1.2s infinite alternate;
    .mat-icon {
      margin-right: 2px;
    }
  }
}

.dialog-list {
  max-height: 260px;
  overflow-y: auto;
  margin-bottom: 8px;
  .dialog-list-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.15s, box-shadow 0.15s;
    &:hover, &:focus {
      background: #e3eaf2;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
    }
    &.selected {
      background: #d1eaff;
      box-shadow: 0 2px 12px rgba(25, 118, 210, 0.12);
    }
    .business-logo, .business-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      margin-right: 12px;
      object-fit: cover;
      background: #f5f5f5;
      border: 2px solid #fff;
      box-shadow: 0 1px 4px rgba(25, 118, 210, 0.10);
    }
    .business-name {
      flex: 1;
      font-weight: 500;
      color: #222;
    }
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 8px;
  .selected-count {
    font-size: 0.95rem;
    color: #1976d2;
    font-weight: 500;
  }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.2);}
  100% { box-shadow: 0 0 8px 4px rgba(25, 118, 210, 0.12);}
}

.dialog-header, .dialog-list-item, .business-name, .oneview-chip, .selected-count {
  font-family: 'Inter', 'Open Sans', Arial, sans-serif;
}

.business-name {
  font-size: 1.08rem;
  font-weight: 700;
  color: #1976d2;
  letter-spacing: 0.01em;
}

.oneview-chip {
  background: linear-gradient(90deg, #1976d2 60%, #00bcd4 100%) !important;
  color: #fff !important;
  font-weight: 600;
  font-size: 0.98rem;
  letter-spacing: 0.01em;
}

.selected-count {
  color: #00bcd4 !important;
  font-weight: 700;
}
