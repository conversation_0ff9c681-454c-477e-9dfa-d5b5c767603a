import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

// Angular Material
import { TabViewModule } from 'primeng/tabview';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ToolbarModule } from 'primeng/toolbar';

// Services
import { AuthService } from '../../core/auth/auth.service';

// Components
import { StaffAvailabilityComponent } from './components/staff-availability/staff-availability.component';
import { TimeOffManagementComponent } from './components/time-off-management/time-off-management.component';
import { SchedulingInterfaceComponent } from './components/scheduling-interface/scheduling-interface.component';
import { TimeTrackingComponent } from './components/time-tracking/time-tracking.component';
import { TimeReportsComponent } from './components/time-reports/time-reports.component';

@Component({
  selector: 'app-time-management',
  standalone: true,
  imports: [
    CommonModule,
    TabViewModule,
    CardModule,
    ButtonModule
    ToolbarModule,
    StaffAvailabilityComponent,
    TimeOffManagementComponent,
    SchedulingInterfaceComponent,
    TimeTrackingComponent,
    TimeReportsComponent
  ],
  template: `
    <div class="time-management-container">
      <div class="time-header">
        <p-toolbar color="primary">
          <i class="pi pi-clock"></i>
          <span>Time Management</span>
          <span class="spacer"></span>
        </p-toolbar>
      </div>

      <div class="time-content">
        <p-tabView class="time-tabs" animationDuration="300ms">
          <!-- Staff Availability Tab -->
          <p-tabPanel label="Availability">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-staff-availability></app-staff-availability>
              </div></ng-template>
          </p-tabPanel>

          <!-- Time Off Requests Tab -->
          <p-tabPanel label="Time Off">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-time-off-management></app-time-off-management>
              </div></ng-template>
          </p-tabPanel>

          <!-- Scheduling Tab -->
          <p-tabPanel label="Scheduling">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-scheduling-interface></app-scheduling-interface>
              </div></ng-template>
          </p-tabPanel>

          <!-- Time Tracking Tab -->
          <p-tabPanel label="Time Tracking">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-time-tracking></app-time-tracking>
              </div></ng-template>
          </p-tabPanel>

          <!-- Reports Tab -->
          <p-tabPanel label="Reports">
            <ng-template matTabContent>
              <div class="tab-content">
                <app-time-reports></app-time-reports>
              </div></ng-template>
          </p-tabPanel>
        </p-tabView>
      </div>
    </div>
  `,
  styleUrls: ['./time-management.component.scss']
})
export class TimeManagementComponent implements OnInit {
  private authService = inject(AuthService);
  private router = inject(Router);

  userProfile$ = this.authService.userProfile$;

  ngOnInit(): void {
    // Check if user has access to time management features
    this.authService.isAdminOrManager().subscribe(hasAccess => {
      if (!hasAccess) {
        // Redirect staff to their personal time view
        this.router.navigate(['/time/personal']);
      }
    });
  }
}
