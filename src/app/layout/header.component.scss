.app-header {
  background: #1976d2 !important;
  color: #fff !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  height: 64px;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1030;
  border-bottom: 1px solid #1565c0;
  transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.02) 100%);
    pointer-events: none;
  }
}

.app-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 700;
  font-size: 1.5rem;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  font-family: 'Inter', 'Roboto', 'Open Sans', <PERSON><PERSON>, sans-serif;
}

.spacer {
  flex: 1;
}

.date-time-pill {
  display: flex;
  align-items: center;
  margin-left: 16px;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2) !important;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.025em;
  color: #ffffff !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
  gap: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 100%);
    transition: left var(--sm-transition-slow);
  }

  &:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06) !important;
    border-color: rgba(255, 255, 255, 0.2);

    &::before {
      left: 100%;
    }
  }

  .date-time-content {
    display: flex;
    align-items: center;
    gap: var(--sm-spacing-sm);
    position: relative;
    z-index: 1;
  }

  .date, .time {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.025em;
    white-space: nowrap;
  }

  .date {
    opacity: 0.9;
  }

  .time {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
  }
}

// TOUCH-FRIENDLY: Modern icon buttons with enhanced interactions
.icon-btn {
  margin: 0 4px;
  transition: all 150ms cubic-bezier(0.4, 0.0, 0.2, 1) !important;
  border-radius: 8px !important;
  width: 44px !important; // Touch-friendly minimum
  height: 44px !important;
  min-width: 44px !important;
  min-height: 44px !important;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width var(--sm-transition-fast), height var(--sm-transition-fast);
  }

  &:hover, &:focus {
    transform: translateY(-1px) !important;
    background: rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 4px 6px rgba(0,0,0,0.07), 0 2px 4px rgba(0,0,0,0.06) !important;

    &::before {
      width: 100%;
      height: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(0.95);
    background: rgba(255, 255, 255, 0.2);
  }

  &:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
  }

  &.pulse {
    animation: pulse 2s infinite;
  }

  mat-icon {
    font-size: 22px;
    width: 22px;
    height: 22px;
    position: relative;
    z-index: 1;
    transition: transform var(--sm-transition-fast);
  }

  &:hover mat-icon {
    transform: scale(1.1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}



.mat-badge-content, .mat-badge-content.mat-badge-warn {
  font-weight: bold;
  font-size: 0.75rem;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  background: var(--mui-palette-error-main, #d32f2f);
  color: #fff;
  box-shadow: 0 1px 2px rgba(0,0,0,0.10);
}

.mat-menu-item .mat-icon {
  margin-right: 8px;
}

.date-time {
  margin-left: 16px;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  opacity: 0.85;
}

// COMPREHENSIVE DARK THEME SUPPORT
.dark-theme {
  .app-header {
    background: linear-gradient(90deg, #1e1e1e 0%, #2a2a2a 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
  }

  .app-title {
    color: var(--sm-primary-light, #42a5f5);
    text-shadow: 0 1px 2px rgba(66, 165, 245, 0.3);
  }

  .date-time-pill {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
  }

  .icon-btn {
    color: rgba(255, 255, 255, 0.9);

    &:hover, &:focus {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
    }

    &:active {
      background: rgba(255, 255, 255, 0.15);
    }
  }

  .mat-badge-content {
    background: var(--sm-error-main, #d32f2f);
    color: #fff;
  }
}