.dashboard-container {
  width: 100%;
  min-height: 100vh;
  padding: var(--sm-spacing-2xl);
  background: var(--sm-background-default);
  transition: background-color var(--sm-transition-normal);
}

.dashboard-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.dashboard-toolbar {
  display: flex;
  align-items: center;
  gap: var(--sm-spacing-md);
  margin-bottom: var(--sm-spacing-2xl);
  padding: var(--sm-spacing-md) var(--sm-spacing-lg);
  background: var(--sm-background-paper);
  border-radius: var(--sm-border-radius-lg);
  box-shadow: var(--sm-shadow-sm);
  border: 1px solid var(--sm-border-light);

  .edit-toggle-btn {
    background: var(--sm-primary-main);
    color: white;
    border-radius: var(--sm-border-radius-md);
    transition: all var(--sm-transition-fast);

    &:hover {
      background: var(--sm-primary-dark);
      transform: translateY(-1px);
      box-shadow: var(--sm-shadow-md);
    }
  }

  .edit-mode-indicator {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--sm-accent-main);
    background: rgba(var(--sm-accent-main), 0.1);
    padding: var(--sm-spacing-xs) var(--sm-spacing-sm);
    border-radius: var(--sm-border-radius-full);
    border: 1px solid rgba(var(--sm-accent-main), 0.2);
  }
}

.dashboard-grid {
  .mat-grid-list {
    .mat-grid-tile {
      border-radius: var(--sm-border-radius-xl);
      overflow: visible;

      .mat-grid-tile-content {
        border-radius: var(--sm-border-radius-xl);
        overflow: hidden;
      }
    }
  }
}

.widget-tile {
  position: relative;

  &.cdk-drag-preview {
    box-shadow: var(--sm-shadow-2xl);
    transform: rotate(2deg);
    border-radius: var(--sm-border-radius-xl);
  }

  &.cdk-drag-placeholder {
    opacity: 0.3;
    background: var(--sm-background-surface);
    border: 2px dashed var(--sm-border-medium);
    border-radius: var(--sm-border-radius-xl);
  }
}

.widget-tile-header {
  position: absolute;
  top: var(--sm-spacing-sm);
  right: var(--sm-spacing-sm);
  z-index: 10;
  opacity: 0;
  transition: opacity var(--sm-transition-normal);

  .widget-tile:hover & {
    opacity: 1;
  }

  button {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: var(--sm-border-radius-full);
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: scale(1.1);
    }
  }
}

.widget-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  background: var(--sm-background-surface);
  border: 2px dashed var(--sm-border-medium);
  border-radius: var(--sm-border-radius-xl);
  color: var(--sm-text-secondary);
  font-size: 1.1rem;
  font-weight: 500;
  transition: all var(--sm-transition-normal);

  &:hover {
    background: var(--sm-background-elevated);
    border-color: var(--sm-primary-main);
    color: var(--sm-primary-main);
  }
}

// Dark theme support
.dark-theme {
  .dashboard-container {
    background: var(--sm-background-default);
  }

  .dashboard-toolbar {
    background: var(--sm-background-paper);
    border-color: var(--sm-border-light);

    .edit-toggle-btn {
      background: var(--sm-primary-main);

      &:hover {
        background: var(--sm-primary-light);
      }
    }

    .edit-mode-indicator {
      background: rgba(251, 191, 36, 0.1);
      border-color: rgba(251, 191, 36, 0.2);
      color: var(--sm-accent-main);
    }
  }

  .widget-placeholder {
    background: var(--sm-background-surface);
    border-color: var(--sm-border-medium);
    color: var(--sm-text-secondary);

    &:hover {
      background: var(--sm-background-elevated);
      border-color: var(--sm-primary-light);
      color: var(--sm-primary-light);
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .dashboard-grid .mat-grid-list {
    .mat-grid-tile {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

@media (max-width: 900px) {
  .dashboard-container {
    padding: var(--sm-spacing-lg);
  }

  .dashboard-grid .mat-grid-list {
    .mat-grid-tile {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .dashboard-toolbar {
    padding: var(--sm-spacing-sm) var(--sm-spacing-md);
    margin-bottom: var(--sm-spacing-lg);

    .edit-mode-indicator {
      font-size: 0.75rem;
      padding: var(--sm-spacing-xs);
    }
  }
}

@media (max-width: 600px) {
  .dashboard-container {
    padding: var(--sm-spacing-md);
  }

  .dashboard-grid .mat-grid-list {
    .mat-grid-tile {
      grid-template-columns: 1fr;
    }
  }

  .dashboard-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--sm-spacing-sm);
  }
}