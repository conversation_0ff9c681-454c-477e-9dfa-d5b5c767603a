import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ProgressBarModule } from 'primeng/progressbar';
import { ChipModule } from 'primeng/chip';
import { DividerModule } from 'primeng/divider';

// Services
import { GoalsService, GoalStatistics } from '../services/goals.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { StaffGoalExtended } from '../../staff/models/staff.model';

@Component({
  selector: 'app-goal-progress-widget',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule
    ProgressBarModule,
    ChipModule,
    DividerModule
  ],
  template: `
    <p-card class="goal-progress-widget">
      <ng-template pTemplate="header">
        <h3>
          <i class="pi pi-circle"></i>
          Goal Progress
        </ng-template>
        <div class="header-actions">
          <p-button [text]="true" (click)="viewAllGoals()">
            <i class="pi pi-circle"></i>
          </p-button>
        </div></ng-template>

      <ng-template pTemplate="content">
        <div class="widget-content" *ngIf="goalStatistics$ | async as stats">
          <!-- Overall Progress -->
          <div class="overall-progress">
            <div class="progress-header">
              <span class="progress-label">Overall Completion</span>
              <span class="progress-value">{{ stats.completionRate | number:'1.0-1' }}%</span>
            </div>
            <p-progressBar
              mode="determinate"
              [value]="stats.completionRate"
              class="main-progress">
            </p-progressBar>
          </div>

          <p-divider></p-divider>

          <!-- Goal Statistics -->
          <div class="goal-stats">
            <div class="stat-item">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">Total Goals</div>
            </div>
            <div class="stat-item">
              <div class="stat-value active">{{ stats.active }}</div>
              <div class="stat-label">Active</div>
            </div>
            <div class="stat-item">
              <div class="stat-value completed">{{ stats.completed }}</div>
              <div class="stat-label">Completed</div>
            </div>
            <div class="stat-item" *ngIf="stats.overdue > 0">
              <div class="stat-value overdue">{{ stats.overdue }}</div>
              <div class="stat-label">Overdue</div>
            </div>
          </div>

          <p-divider></p-divider>

          <!-- Recent Goals -->
          <div class="recent-goals">
            <h4>Recent Goals</h4>
            <div class="goals-list" *ngIf="recentGoals$ | async as goals">
              <div *ngFor="let goal of goals.slice(0, 3)" class="goal-item">
                <div class="goal-info">
                  <div class="goal-title">{{ goal.title }}</div>
                  <div class="goal-meta">
                    <p-chip [class]="'status-' + goal.status" class="status-chip">
                      {{ goal.status | titlecase }}
                    </p-chip>
                    <span class="goal-progress">{{ goal.progress }}%</span>
                  </div>
                </div>
                <p-progressBar
                  mode="determinate"
                  [value]="goal.progress"
                  class="goal-progress-bar">
                </p-progressBar>
              </div>
            </div>

            <div class="empty-state" *ngIf="(recentGoals$ | async)?.length === 0">
              <i class="pi pi-circle"></i>
              <p>No goals yet</p>
              <p-button [outlined]="true" color="primary" (click)="createGoal()">
                Create Goal
              </p-button>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div class="loading-state" *ngIf="!(goalStatistics$ | async)">
          <i class="pi pi-circle"></i>
          <p>Loading goals...</p>
        </div></ng-template>

      <ng-template pTemplate="footer">
        <button p-button (click)="viewAllGoals()">
          <i class="pi pi-eye"></i>
          View All Goals
        </p-button>
        <button p-button (click)="createGoal()">
          <i class="pi pi-plus"></i>
          Create Goal
        </p-button></ng-template></p-card>
  `,
  styles: [`
    .goal-progress-widget {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    mat-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
    }

    .header-actions {
      margin-left: auto;
    }

    .widget-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .overall-progress {
      margin-bottom: 8px;
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .progress-label {
      font-weight: 500;
      color: var(--mdc-theme-text-primary-on-background);
    }

    .progress-value {
      font-weight: 600;
      font-size: 1.2rem;
      color: var(--mdc-theme-primary);
    }

    .main-progress {
      height: 8px;
      border-radius: 4px;
    }

    .goal-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 16px;
      text-align: center;
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--mdc-theme-text-primary-on-background);
    }

    .stat-value.active {
      color: #2196f3;
    }

    .stat-value.completed {
      color: #4caf50;
    }

    .stat-value.overdue {
      color: #f44336;
    }

    .stat-label {
      font-size: 0.75rem;
      color: var(--mdc-theme-text-secondary-on-background);
      margin-top: 4px;
    }

    .recent-goals h4 {
      margin: 0 0 12px 0;
      font-size: 1rem;
      font-weight: 500;
    }

    .goals-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .goal-item {
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .goal-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .goal-title {
      font-weight: 500;
      font-size: 0.875rem;
      flex: 1;
      margin-right: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .goal-meta {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-chip {
      font-size: 0.75rem;
      height: 20px;
      min-height: 20px;
    }

    .status-chip.status-completed {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-chip.status-in-progress {
      background-color: #e3f2fd;
      color: #1565c0;
    }

    .status-chip.status-overdue {
      background-color: #ffebee;
      color: #c62828;
    }

    .status-chip.status-not-started {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .goal-progress {
      font-size: 0.75rem;
      font-weight: 500;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .goal-progress-bar {
      height: 4px;
      border-radius: 2px;
    }

    .empty-state {
      text-align: center;
      padding: 24px 16px;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .empty-state mat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      margin-bottom: 8px;
      opacity: 0.5;
    }

    .empty-state p {
      margin: 0 0 16px 0;
      font-size: 0.875rem;
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px 16px;
      color: var(--mdc-theme-text-secondary-on-background);
    }

    .loading-state mat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      margin-bottom: 8px;
      animation: spin 2s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    mat-card-actions {
      margin-top: auto;
      display: flex;
      gap: 8px;
    }

    mat-card-actions button {
      flex: 1;
    }

    @media (max-width: 768px) {
      .goal-stats {
        grid-template-columns: repeat(2, 1fr);
      }

      .goal-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .goal-meta {
        align-self: flex-end;
      }
    }
  `]
})
export class GoalProgressWidgetComponent implements OnInit {
  private goalsService = inject(GoalsService);
  private authService = inject(AuthService);
  private router = inject(Router);

  goalStatistics$!: Observable<GoalStatistics>;
  recentGoals$!: Observable<StaffGoalExtended[]>;

  ngOnInit(): void {
    this.initializeData();
  }

  private initializeData(): void {
    // Get goal statistics
    this.goalStatistics$ = this.goalsService.getGoalStatistics();

    // Get recent goals for current user
    this.authService.user$.subscribe(currentUser => {
      if (currentUser) {
        this.recentGoals$ = this.goalsService.getUserGoals(currentUser.uid);
      } else {
        this.recentGoals$ = this.goalsService.getGoals();
      }
    });
  }

  viewAllGoals(): void {
    this.router.navigate(['/goals']);
  }

  createGoal(): void {
    this.router.navigate(['/goals/create']);
  }
}
