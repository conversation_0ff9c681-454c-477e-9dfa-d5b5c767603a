// Calendar and Scheduling Models for StaffHub
export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  type: 'shift' | 'meeting' | 'training' | 'time-off' | 'break' | 'appointment' | 'other';
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';

  // Staff assignment
  assignedStaff: string[]; // Staff IDs
  createdBy: string;
  businessId: string;
  location?: string;

  // Recurring events
  isRecurring?: boolean;
  recurringPattern?: RecurringPattern;
  recurringEndDate?: Date;
  parentEventId?: string; // For recurring event instances

  // Additional properties
  color?: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
  tags?: string[];
}

export interface RecurringPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every X days/weeks/months/years
  daysOfWeek?: number[]; // For weekly recurrence (0-6, Sunday-Saturday)
  dayOfMonth?: number; // For monthly recurrence
  endType: 'never' | 'after-occurrences' | 'on-date';
  occurrences?: number;
  endDate?: Date;
}

export interface ShiftTemplate {
  id: string;
  name: string;
  description?: string;
  businessId: string;
  departmentId?: string;
  
  // Shift details
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  duration: number; // minutes
  
  // Staffing requirements
  requiredStaff: number;
  preferredStaff?: string[]; // Staff IDs
  requiredSkills?: string[];
  
  // Break settings
  breaks: ShiftBreak[];
  
  // Metadata
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface ShiftBreak {
  id: string;
  name: string;
  startMinutes: number; // Minutes from shift start
  duration: number; // minutes
  isPaid: boolean;
  isRequired: boolean;
}

export interface ShiftAssignment {
  id: string;
  eventId: string;
  staffId: string;
  businessId: string;
  
  // Assignment details
  assignedAt: Date;
  assignedBy: string;
  status: 'assigned' | 'accepted' | 'declined' | 'completed' | 'no-show';
  
  // Actual time tracking
  actualStartTime?: Date;
  actualEndTime?: Date;
  actualBreaks?: ActualBreak[];
  
  // Notes and feedback
  staffNotes?: string;
  managerNotes?: string;
  rating?: number; // 1-5 performance rating
  
  // Metadata
  updatedAt: Date;
}

export interface ActualBreak {
  startTime: Date;
  endTime?: Date;
  duration?: number; // minutes
  type: 'lunch' | 'break' | 'personal';
  notes?: string;
}

export interface TimeOffRequest {
  id: string;
  staffId: string;
  businessId: string;
  
  // Request details
  type: 'vacation' | 'sick' | 'personal' | 'bereavement' | 'jury-duty' | 'other';
  startDate: Date;
  endDate: Date;
  isPartialDay: boolean;
  startTime?: string; // For partial days
  endTime?: string; // For partial days
  
  // Request information
  reason?: string;
  notes?: string;
  attachments?: string[]; // File URLs
  
  // Approval workflow
  status: 'pending' | 'approved' | 'denied' | 'cancelled';
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
  
  // Impact analysis
  conflictingShifts?: string[]; // Event IDs
  coverageNeeded: boolean;
  suggestedCoverage?: string[]; // Staff IDs
  
  // Metadata
  submittedAt: Date;
  updatedAt: Date;
}

export interface ScheduleConflict {
  id: string;
  type: 'double-booking' | 'overtime' | 'availability' | 'time-off' | 'skill-mismatch';
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  // Conflict details
  staffId: string;
  eventIds: string[];
  description: string;
  suggestedResolution?: string;
  
  // Resolution
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  resolution?: string;
  
  // Metadata
  detectedAt: Date;
  businessId: string;
}

export interface CalendarView {
  id: string;
  name: string;
  type: 'month' | 'week' | 'day' | 'agenda' | 'timeline';
  isDefault: boolean;
  
  // Filter settings
  filters: {
    eventTypes: string[];
    staffIds: string[];
    departments: string[];
    locations: string[];
    statuses: string[];
  };
  
  // Display settings
  displaySettings: {
    showWeekends: boolean;
    startHour: number;
    endHour: number;
    timeFormat: '12h' | '24h';
    firstDayOfWeek: number; // 0-6
  };
  
  // User preferences
  userId: string;
  isShared: boolean;
  sharedWith?: string[]; // User IDs
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}
