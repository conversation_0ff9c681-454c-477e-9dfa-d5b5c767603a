import { Injectable, inject } from '@angular/core';
import { Observable, from, map, switchMap, combineLatest, of } from 'rxjs';
import { where, orderBy, limit as firestoreLimit } from '@angular/fire/firestore';
import { FirestoreBaseService } from '../../../core/services/firestore-base.service';
import { AuthService } from '../../../core/auth/auth.service';
import {
  StaffMember,
  StaffGoalExtended,
  StaffTask,
  TimeEntry,
  TimeOffRequest,
  WorkSchedule
} from '../models/staff.model';

@Injectable({
  providedIn: 'root'
})
export class StaffFirestoreService extends FirestoreBaseService {
  private authService = inject(AuthService);
  private readonly COLLECTION_NAME = 'staff';
  private readonly GOALS_COLLECTION = 'goals';
  private readonly TASKS_COLLECTION = 'tasks';
  private readonly TIME_ENTRIES_COLLECTION = 'timeEntries';
  private readonly TIME_OFF_COLLECTION = 'timeOffRequests';
  private readonly SCHEDULES_COLLECTION = 'workSchedules';

  // Staff CRUD Operations
  createStaff(staff: Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>): Observable<StaffMember> {
    return from(this.create<StaffMember>(this.COLLECTION_NAME, staff));
  }

  updateStaff(id: string, updates: Partial<Omit<StaffMember, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<StaffMember>(this.COLLECTION_NAME, id, updates));
  }

  deleteStaff(id: string): Observable<void> {
    return from(this.delete(this.COLLECTION_NAME, id));
  }

  getStaffById(id: string): Observable<StaffMember | null> {
    return from(this.getById<StaffMember>(this.COLLECTION_NAME, id));
  }

  getAllStaff(businessId?: string): Observable<StaffMember[]> {
    const constraints = businessId ? [
      this.createWhereConstraint('businessIds', 'array-contains', businessId),
      this.createOrderByConstraint('lastName', 'asc')
    ] : [this.createOrderByConstraint('lastName', 'asc')];

    return from(this.getAll<StaffMember>(this.COLLECTION_NAME, constraints));
  }

  getActiveStaff(businessId?: string): Observable<StaffMember[]> {
    const constraints = [
      this.createWhereConstraint('status', '==', 'active'),
      ...(businessId ? [this.createWhereConstraint('businessIds', 'array-contains', businessId)] : []),
      this.createOrderByConstraint('lastName', 'asc')
    ];

    return from(this.getAll<StaffMember>(this.COLLECTION_NAME, constraints));
  }

  getStaffByDepartment(department: string, businessId?: string): Observable<StaffMember[]> {
    const constraints = [
      this.createWhereConstraint('department', '==', department),
      ...(businessId ? [this.createWhereConstraint('businessIds', 'array-contains', businessId)] : []),
      this.createOrderByConstraint('lastName', 'asc')
    ];

    return from(this.getAll<StaffMember>(this.COLLECTION_NAME, constraints));
  }

  // Real-time subscriptions
  subscribeToStaff(businessId?: string): Observable<StaffMember[]> {
    const constraints = businessId ? [
      this.createWhereConstraint('businessIds', 'array-contains', businessId),
      this.createOrderByConstraint('lastName', 'asc')
    ] : [this.createOrderByConstraint('lastName', 'asc')];

    return this.subscribeToCollection<StaffMember>(this.COLLECTION_NAME, constraints);
  }

  subscribeToStaffMember(id: string): Observable<StaffMember | null> {
    return this.subscribeToDocument<StaffMember>(this.COLLECTION_NAME, id);
  }

  // Goals Management
  createGoal(goal: Omit<StaffGoalExtended, 'id' | 'createdAt' | 'updatedAt'>): Observable<StaffGoalExtended> {
    return from(this.create<StaffGoalExtended>(this.GOALS_COLLECTION, goal));
  }

  updateGoal(id: string, updates: Partial<Omit<StaffGoalExtended, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<StaffGoalExtended>(this.GOALS_COLLECTION, id, updates));
  }

  deleteGoal(id: string): Observable<void> {
    return from(this.delete(this.GOALS_COLLECTION, id));
  }

  getGoalById(id: string): Observable<StaffGoalExtended | null> {
    return from(this.getById<StaffGoalExtended>(this.GOALS_COLLECTION, id));
  }

  getAllGoals(): Observable<StaffGoalExtended[]> {
    return from(this.getAll<StaffGoalExtended>(this.GOALS_COLLECTION, []));
  }

  subscribeToAllGoals(): Observable<StaffGoalExtended[]> {
    return this.subscribeToCollection<StaffGoalExtended>(this.GOALS_COLLECTION, []);
  }

  getGoalsByStaff(staffId: string): Observable<StaffGoalExtended[]> {
    const constraints = [
      this.createWhereConstraint('assignedTo', 'array-contains', staffId),
      this.createOrderByConstraint('targetDate', 'asc')
    ];

    return from(this.getAll<StaffGoalExtended>(this.GOALS_COLLECTION, constraints));
  }

  subscribeToStaffGoals(staffId: string): Observable<StaffGoalExtended[]> {
    const constraints = [
      this.createWhereConstraint('assignedTo', 'array-contains', staffId),
      this.createOrderByConstraint('targetDate', 'asc')
    ];

    return this.subscribeToCollection<StaffGoalExtended>(this.GOALS_COLLECTION, constraints);
  }

  // Tasks Management
  createTask(task: Omit<StaffTask, 'id' | 'createdAt' | 'updatedAt'>): Observable<StaffTask> {
    return from(this.create<StaffTask>(this.TASKS_COLLECTION, task));
  }

  updateTask(id: string, updates: Partial<Omit<StaffTask, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<StaffTask>(this.TASKS_COLLECTION, id, updates));
  }

  deleteTask(id: string): Observable<void> {
    return from(this.delete(this.TASKS_COLLECTION, id));
  }

  getTasksByStaff(staffId: string): Observable<StaffTask[]> {
    const constraints = [
      this.createWhereConstraint('assignedTo', 'array-contains', staffId),
      this.createOrderByConstraint('dueDate', 'asc')
    ];

    return from(this.getAll<StaffTask>(this.TASKS_COLLECTION, constraints));
  }

  subscribeToStaffTasks(staffId: string): Observable<StaffTask[]> {
    const constraints = [
      this.createWhereConstraint('assignedTo', 'array-contains', staffId),
      this.createOrderByConstraint('dueDate', 'asc')
    ];

    return this.subscribeToCollection<StaffTask>(this.TASKS_COLLECTION, constraints);
  }

  // Time Management
  createTimeEntry(entry: Omit<TimeEntry, 'id' | 'createdAt' | 'updatedAt'>): Observable<TimeEntry> {
    return from(this.create<TimeEntry>(this.TIME_ENTRIES_COLLECTION, entry));
  }

  getTimeEntriesByStaff(staffId: string, startDate?: Date, endDate?: Date): Observable<TimeEntry[]> {
    let constraints = [
      this.createWhereConstraint('staffId', '==', staffId),
      this.createOrderByConstraint('timestamp', 'desc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('timestamp', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('timestamp', '<=', endDate));
    }

    return from(this.getAll<TimeEntry>(this.TIME_ENTRIES_COLLECTION, constraints));
  }

  // Time Off Requests
  createTimeOffRequest(request: Omit<TimeOffRequest, 'id' | 'createdAt' | 'updatedAt'>): Observable<TimeOffRequest> {
    return from(this.create<TimeOffRequest>(this.TIME_OFF_COLLECTION, request));
  }

  updateTimeOffRequest(id: string, updates: Partial<Omit<TimeOffRequest, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<TimeOffRequest>(this.TIME_OFF_COLLECTION, id, updates));
  }

  getTimeOffRequestsByStaff(staffId: string): Observable<TimeOffRequest[]> {
    const constraints = [
      this.createWhereConstraint('staffId', '==', staffId),
      this.createOrderByConstraint('startDate', 'desc')
    ];

    return from(this.getAll<TimeOffRequest>(this.TIME_OFF_COLLECTION, constraints));
  }

  // Work Schedules
  createWorkSchedule(schedule: Omit<WorkSchedule, 'id' | 'createdAt' | 'updatedAt'>): Observable<WorkSchedule> {
    return from(this.create<WorkSchedule>(this.SCHEDULES_COLLECTION, schedule));
  }

  updateWorkSchedule(id: string, updates: Partial<Omit<WorkSchedule, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<WorkSchedule>(this.SCHEDULES_COLLECTION, id, updates));
  }

  getSchedulesByStaff(staffId: string, startDate?: Date, endDate?: Date): Observable<WorkSchedule[]> {
    let constraints = [
      this.createWhereConstraint('staffId', '==', staffId),
      this.createOrderByConstraint('date', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('date', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('date', '<=', endDate));
    }

    return from(this.getAll<WorkSchedule>(this.SCHEDULES_COLLECTION, constraints));
  }

  subscribeToStaffSchedules(staffId: string, startDate?: Date, endDate?: Date): Observable<WorkSchedule[]> {
    let constraints = [
      this.createWhereConstraint('staffId', '==', staffId),
      this.createOrderByConstraint('date', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('date', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('date', '<=', endDate));
    }

    return this.subscribeToCollection<WorkSchedule>(this.SCHEDULES_COLLECTION, constraints);
  }

  // Utility methods
  searchStaff(query: string, businessId?: string): Observable<StaffMember[]> {
    // Note: Firestore doesn't support full-text search natively
    // This is a simplified implementation - consider using Algolia or similar for production
    return this.getAllStaff(businessId).pipe(
      map(staff => staff.filter(s =>
        s.firstName.toLowerCase().includes(query.toLowerCase()) ||
        s.lastName.toLowerCase().includes(query.toLowerCase()) ||
        s.email.toLowerCase().includes(query.toLowerCase()) ||
        s.employeeId.toLowerCase().includes(query.toLowerCase()) ||
        s.position.toLowerCase().includes(query.toLowerCase()) ||
        s.department.toLowerCase().includes(query.toLowerCase())
      ))
    );
  }
}
