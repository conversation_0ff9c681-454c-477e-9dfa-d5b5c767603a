import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

@Component({
  selector: 'app-widget-settings-dialog',
  standalone: true,
  imports: [
    CommonModule, FormsModule, ButtonModule, InputTextModule
  ],
  template: `
    <h2 mat-dialog-title>Widget Settings</h2>
    <div mat-dialog-content>
      <form>
        <div class="p-field" appearance="fill" style="width: 100%;">
          <label>Auto Refresh</label>
          <input pInputText type="checkbox" [(ngModel)]="settings.autoRefresh" name="autoRefresh">
        </div>
        <div class="p-field" appearance="fill" style="width: 100%;">
          <label>Refresh Interval (seconds)</label>
          <input pInputText type="number" [(ngModel)]="settings.refreshInterval" name="refreshInterval">
        </div>
        <div class="p-field" appearance="fill" style="width: 100%;">
          <label>Width</label>
          <input pInputText type="number" [(ngModel)]="settings.width" name="width">
        </div>
        <div class="p-field" appearance="fill" style="width: 100%;">
          <label>Height</label>
          <input pInputText type="number" [(ngModel)]="settings.height" name="height">
        </div>
      </form>
    </div>
    <div class="dialog-actions">
      <p-button label="Cancel" severity="secondary" (click)="onCancel()"></p-button>
      <p-button label="Save" (click)="onSave()"></p-button>
    </div>
  `
})
export class WidgetSettingsDialogComponent {
  settings = {
    autoRefresh: false,
    refreshInterval: 60,
    width: 3,
    height: 4
  };

  constructor(
    @Inject(DynamicDialogRef) public dialogRef: DynamicDialogRef,
    @Inject(DynamicDialogConfig) public data: any
  ) {
    if (data && data.settings) {
      this.settings = { ...this.settings, ...data.settings };
    }
  }

  onSave() {
    this.dialogRef.close(this.settings);
  }

  onCancel() {
    this.dialogRef.close();
  }
}