.number-widget {
  min-width: 200px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: var(--sm-spacing-lg);
  background: var(--sm-background-paper);
  border-radius: var(--sm-border-radius-xl);
  box-shadow: var(--sm-shadow-md);
  border: 1px solid var(--sm-border-light);
  position: relative;
  overflow: hidden;
  transition: all var(--sm-transition-normal);
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
      var(--sm-primary-main) 0%,
      var(--sm-secondary-main) 50%,
      var(--sm-accent-main) 100%);
    opacity: 0;
    transition: opacity var(--sm-transition-normal);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--sm-shadow-xl);
    border-color: var(--sm-border-medium);

    &::before {
      opacity: 1;
    }

    .widget-background-pattern {
      opacity: 0.1;
      transform: scale(1.1);
    }
  }
}

.number-widget.quicklook {
  min-width: 120px;
  min-height: 80px;
  padding: 8px;
}

.number-widget-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--mui-palette-text-primary, #222);
}

.number-widget-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--mui-palette-primary-main, #1976d2);
  margin: 8px 0 4px 0;
}

.number-widget-description {
  font-size: 0.875rem;
  color: var(--sm-text-secondary);
  line-height: 1.4;
  margin-bottom: var(--sm-spacing-sm);
}

// Widget header with icon
.widget-header {
  position: absolute;
  top: var(--sm-spacing-md);
  right: var(--sm-spacing-md);

  .widget-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: var(--sm-primary-main);
    opacity: 0.7;
    transition: all var(--sm-transition-normal);
  }
}

// Trend indicator
.widget-trend {
  display: flex;
  align-items: center;
  gap: var(--sm-spacing-xs);
  margin-top: var(--sm-spacing-sm);

  .trend-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;

    &.trend-up {
      color: #10b981; // Green
    }

    &.trend-down {
      color: #ef4444; // Red
    }

    &.trend-neutral {
      color: var(--sm-text-secondary);
    }
  }

  .trend-text {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--sm-text-secondary);
  }
}

// Background pattern
.widget-background-pattern {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle,
    rgba(var(--sm-primary-main), 0.03) 0%,
    transparent 70%);
  opacity: 0;
  transition: all var(--sm-transition-slow);
  pointer-events: none;
}

// Color variants
.number-widget.color-primary {
  .number-widget-value {
    color: var(--sm-primary-main);
  }

  &::before {
    background: var(--sm-primary-main);
  }
}

.number-widget.color-accent {
  .number-widget-value {
    color: var(--sm-accent-main);
  }

  &::before {
    background: var(--sm-accent-main);
  }
}

.number-widget.color-warn {
  .number-widget-value {
    color: #ef4444;
  }

  &::before {
    background: #ef4444;
  }
}

// Dark theme support
.dark-theme {
  .number-widget {
    background: var(--sm-background-paper);
    border-color: var(--sm-border-light);

    &:hover {
      border-color: var(--sm-border-medium);
    }
  }

  .number-widget-title {
    color: var(--sm-text-primary);
  }

  .number-widget-value {
    color: var(--sm-primary-light);
  }

  .number-widget-description {
    color: var(--sm-text-secondary);
  }

  .widget-icon {
    color: var(--sm-primary-light) !important;
  }

  .widget-background-pattern {
    background: radial-gradient(circle,
      rgba(59, 130, 246, 0.05) 0%,
      transparent 70%);
  }
}

// Responsive design
@media (max-width: 768px) {
  .number-widget {
    min-width: 160px;
    min-height: 100px;
    padding: var(--sm-spacing-md);
  }

  .number-widget.quicklook {
    min-width: 120px;
    min-height: 80px;
    padding: var(--sm-spacing-sm);
  }

  .number-widget-title {
    font-size: 0.875rem;
  }

  .number-widget-value {
    font-size: 1.75rem;
  }

  .number-widget-description {
    font-size: 0.75rem;
  }
}