import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-time-tracking',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule],
  template: `
    <div class="time-tracking-container">
      <div class="placeholder-content">
        <p-card class="placeholder-card">
          <ng-template pTemplate="header">
            <h3>
              <i class="pi pi-circle"></i>
              Time Tracking
            </ng-template>
            <p>Content</p></ng-template>
          
          <ng-template pTemplate="content">
            <div class="placeholder-message">
              <i class="pi pi-clock"></i>
              <h3>Time Tracking Coming Soon</h3>
              <p>This feature will include:</p>
              <ul>
                <li>Clock in/out functionality</li>
                <li>Break time tracking</li>
                <li>Overtime calculations</li>
                <li>Real-time attendance monitoring</li>
                <li>AI-powered time analysis</li>
              </ul>
            </div></ng-template>
          
          <ng-template pTemplate="footer">
            <p-button color="primary" disabled>
              <i class="pi pi-circle"></i>
              Clock In
            </p-button>
            <button p-button disabled>
              <i class="pi pi-circle"></i>
              Break
            </p-button></ng-template></p-card>
      </div>
    </div>
  `,
  styles: [`
    .time-tracking-container {
      padding: 24px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .placeholder-card {
      max-width: 500px;
      width: 100%;
    }

    .placeholder-message {
      text-align: center;
      padding: 24px;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #1976d2;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 16px 0;
        color: #333;
      }

      p {
        margin: 0 0 16px 0;
        color: #666;
      }

      ul {
        text-align: left;
        color: #555;
        
        li {
          margin-bottom: 8px;
        }
      }
    }

    mat-card-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
    }
  `]
})
export class TimeTrackingComponent {}
