import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Observable } from 'rxjs';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { TableModule } from 'primeng/table';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';

import { ChipModule } from 'primeng/chip';
import { TabViewModule } from 'primeng/tabview';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';
import { BadgeModule } from 'primeng/badge';

// Services
import { AuthService } from '../../../../core/auth/auth.service';

export interface TimeOffRequest {
  id: string;
  staffId: string;
  staffName: string;
  type: 'vacation' | 'sick' | 'personal' | 'other';
  startDate: Date;
  endDate: Date;
  totalDays: number;
  reason: string;
  status: 'pending' | 'approved' | 'denied';
  requestedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  notes?: string;
}

export interface TimeOffBalance {
  staffId: string;
  staffName: string;
  vacation: {
    allocated: number;
    used: number;
    pending: number;
    remaining: number;
  };
  sick: {
    allocated: number;
    used: number;
    pending: number;
    remaining: number;
  };
  personal: {
    allocated: number;
    used: number;
    pending: number;
    remaining: number;
  };
}

@Component({
  selector: 'app-time-off-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule
    TableModule, InputTextModule, DropdownModule,
    CalendarModule,
    ChipModule,
    TabViewModule,
    ProgressSpinnerModule,
    ToastModule,
    BadgeModule
  ],
  template: `
    <div class="time-off-container">
      <div class="time-off-header">
        <h2>
          <i class="pi pi-circle"></i>
          Time Off Management
        </h2>
        <div class="header-actions">
          <p-button color="primary" (click)="openRequestDialog()">
            <i class="pi pi-plus"></i>
            New Request
          </p-button>
          <p-button color="accent" (click)="generateAIInsights()">
            <i class="pi pi-brain"></i>
            AI Insights
          </p-button>
        </div>
      </div>

      <p-tabView class="time-off-tabs">
        <!-- Pending Requests Tab -->
        <p-tabPanel>
          <ng-template mat-tab-label>
            <span>Pending Requests</span>
            <span *ngIf="pendingRequests.length > 0" class="pending-badge">{{ pendingRequests.length }}</span></ng-template>
          <div class="tab-content">
            <div class="requests-grid">
              <p-card *ngFor="let request of pendingRequests" class="request-card pending">
                <ng-template pTemplate="header">
                  <div mat-card-avatar class="request-avatar">
                    <i class="pi pi-user"></i>
                  </div>
                  <h3>
                  <p>Content</p></ng-template>

                <ng-template pTemplate="content">
                  <div class="request-details">
                    <div class="date-range">
                      <i class="pi pi-circle"></i>
                      <span>{{ request.startDate | date:'MMM d, y' }} - {{ request.endDate | date:'MMM d, y' }}</span>
                    </div>
                    <div class="reason" *ngIf="request.reason">
                      <i class="pi pi-circle"></i>
                      <span>{{ request.reason }}</span>
                    </div>
                    <div class="requested-date">
                      <i class="pi pi-clock"></i>
                      <span>Requested {{ request.requestedAt | date:'MMM d, y' }}</span>
                    </div>
                  </div></ng-template>

                <ng-template pTemplate="footer">
                  <p-button color="primary" (click)="approveRequest(request.id)">
                    <i class="pi pi-check"></i>
                    Approve
                  </p-button>
                  <p-button color="warn" (click)="denyRequest(request.id)">
                    <i class="pi pi-times"></i>
                    Deny
                  </p-button>
                  <button p-button (click)="viewDetails(request.id)">
                    <i class="pi pi-eye"></i>
                    Details
                  </p-button></ng-template></p-card>
            </div>

            <div class="empty-state" *ngIf="pendingRequests.length === 0">
              <i class="pi pi-check-circle"></i>
              <h3>No Pending Requests</h3>
              <p>All time off requests have been reviewed.</p>
            </div>
          </div>
        </p-tabPanel>

        <!-- All Requests Tab -->
        <p-tabPanel label="All Requests">
          <div class="tab-content">
            <div class="requests-table">
              <table mat-table [dataSource]="allRequests" class="requests-table">
                <ng-container matColumnDef="staff">
                  <th mat-header-cell *matHeaderCellDef>Staff</th>
                  <td mat-cell *matCellDef="let request">{{ request.staffName }}</td>
                </ng-container>

                <ng-container matColumnDef="type">
                  <th mat-header-cell *matHeaderCellDef>Type</th>
                  <td mat-cell *matCellDef="let request">
                    <p-chip [class]="'type-' + request.type">{{ request.type | titlecase }}</p-chip>
                  </td>
                </ng-container>

                <ng-container matColumnDef="dates">
                  <th mat-header-cell *matHeaderCellDef>Dates</th>
                  <td mat-cell *matCellDef="let request">
                    {{ request.startDate | date:'MMM d' }} - {{ request.endDate | date:'MMM d, y' }}
                  </td>
                </ng-container>

                <ng-container matColumnDef="days">
                  <th mat-header-cell *matHeaderCellDef>Days</th>
                  <td mat-cell *matCellDef="let request">{{ request.totalDays }}</td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let request">
                    <p-chip [class]="'status-' + request.status">{{ request.status | titlecase }}</p-chip>
                  </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let request">
                    <p-button [text]="true" (click)="viewDetails(request.id)">
                      <i class="pi pi-eye"></i>
                    </p-button>
                    <p-button [text]="true" *ngIf="request.status === 'pending'" (click)="editRequest(request.id)">
                      <i class="pi pi-pencil"></i>
                    </p-button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              </table>
            </div>
          </div>
        </p-tabPanel>

        <!-- Balances Tab -->
        <p-tabPanel label="Time Off Balances">
          <div class="tab-content">
            <div class="balances-grid">
              <p-card *ngFor="let balance of timeOffBalances" class="balance-card">
                <ng-template pTemplate="header">
                  <div mat-card-avatar class="balance-avatar">
                    <i class="pi pi-user"></i>
                  </div>
                  <h3>Header</h3></ng-template>

                <ng-template pTemplate="content">
                  <div class="balance-summary">
                    <div class="balance-item">
                      <h4>Vacation</h4>
                      <div class="balance-details">
                        <span class="remaining">{{ balance.vacation.remaining }} days left</span>
                        <div class="balance-breakdown">
                          Used: {{ balance.vacation.used }} | Pending: {{ balance.vacation.pending }}
                        </div>
                      </div>
                    </div>

                    <div class="balance-item">
                      <h4>Sick Time</h4>
                      <div class="balance-details">
                        <span class="remaining">{{ balance.sick.remaining }} days left</span>
                        <div class="balance-breakdown">
                          Used: {{ balance.sick.used }} | Pending: {{ balance.sick.pending }}
                        </div>
                      </div>
                    </div>

                    <div class="balance-item">
                      <h4>Personal</h4>
                      <div class="balance-details">
                        <span class="remaining">{{ balance.personal.remaining }} days left</span>
                        <div class="balance-breakdown">
                          Used: {{ balance.personal.used }} | Pending: {{ balance.personal.pending }}
                        </div>
                      </div>
                    </div>
                  </div></ng-template>

                <ng-template pTemplate="footer">
                  <button p-button color="primary" (click)="adjustBalance(balance.staffId)">
                    <i class="pi pi-pencil"></i>
                    Adjust
                  </p-button>
                  <button p-button (click)="viewHistory(balance.staffId)">
                    <i class="pi pi-circle"></i>
                    History
                  </p-button></ng-template></p-card>
            </div>
          </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  `,
  styleUrls: ['./time-off-management.component.scss']
})
export class TimeOffManagementComponent implements OnInit {
  private authService = inject(AuthService);
  private fb = inject(FormBuilder);

  // Data
  pendingRequests: TimeOffRequest[] = [];
  allRequests: TimeOffRequest[] = [];
  timeOffBalances: TimeOffBalance[] = [];
  displayedColumns = ['staff', 'type', 'dates', 'days', 'status', 'actions'];

  ngOnInit(): void {
    this.loadTimeOffData();
  }

  private loadTimeOffData(): void {
    // TODO: Load from Firestore
    this.loadMockData();
  }

  private loadMockData(): void {
    // Mock data for demonstration
    this.pendingRequests = [
      {
        id: '1',
        staffId: 'staff1',
        staffName: 'John Doe',
        type: 'vacation',
        startDate: new Date('2024-02-15'),
        endDate: new Date('2024-02-19'),
        totalDays: 5,
        reason: 'Family vacation',
        status: 'pending',
        requestedAt: new Date('2024-01-15')
      }
    ];

    this.allRequests = [...this.pendingRequests];

    this.timeOffBalances = [
      {
        staffId: 'staff1',
        staffName: 'John Doe',
        vacation: { allocated: 20, used: 5, pending: 5, remaining: 10 },
        sick: { allocated: 10, used: 2, pending: 0, remaining: 8 },
        personal: { allocated: 5, used: 1, pending: 0, remaining: 4 }
      }
    ];
  }

  openRequestDialog(): void {
    // TODO: Open time off request dialog
    console.log('Open request dialog');
  }

  approveRequest(requestId: string): void {
    // TODO: Approve request
    console.log('Approve request:', requestId);
  }

  denyRequest(requestId: string): void {
    // TODO: Deny request
    console.log('Deny request:', requestId);
  }

  viewDetails(requestId: string): void {
    // TODO: View request details
    console.log('View details:', requestId);
  }

  editRequest(requestId: string): void {
    // TODO: Edit request
    console.log('Edit request:', requestId);
  }

  adjustBalance(staffId: string): void {
    // TODO: Adjust time off balance
    console.log('Adjust balance for:', staffId);
  }

  viewHistory(staffId: string): void {
    // TODO: View time off history
    console.log('View history for:', staffId);
  }

  async generateAIInsights(): Promise<void> {
    // TODO: Generate AI insights for time off patterns
    console.log('Generate AI insights');
  }
}
