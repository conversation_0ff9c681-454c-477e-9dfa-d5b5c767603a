import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { StaffManagerThemeService } from './core/theme/staffmanager-theme';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'staffmanager-web-new';

  constructor(
    private themeService: StaffManagerThemeService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit() {
    // CRITICAL FIX: Only initialize theme in browser environment
    if (isPlatformBrowser(this.platformId)) {
      this.themeService.setTheme(false); // Start with light theme
    }
  }
}
