import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { DialogService } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';

import { TabViewModule } from 'primeng/tabview';
import { ListboxModule } from 'primeng/listbox';
import { ChipModule } from 'primeng/chip';
import { BadgeModule } from 'primeng/badge';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';

import { TableModule } from 'primeng/table';
import { MenuModule } from 'primeng/menu';
// import { QRCodeModule } from 'angularx-qrcode';
import { Observable, BehaviorSubject, interval } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

interface StaffHubUser {
  id: string;
  name: string;
  email: string;
  lastActive: Date;
  status: 'online' | 'offline' | 'away';
  device: string;
  version: string;
  location?: string;
}

interface PWAUpdate {
  id: string;
  version: string;
  description: string;
  releaseDate: Date;
  status: 'pending' | 'deployed' | 'failed';
  targetUsers: string[];
}

@Component({
  selector: 'app-staff-hub-management',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule, TabViewModule,
    ListboxModule,
    ChipModule,
    BadgeModule,
    ProgressSpinnerModule,
    ToastModule,
    TableModule,
    MenuModule
  ],
  template: `
    <div class="staff-hub-container">
      <!-- Header -->
      <div class="page-header">
        <p-card>
          <ng-template pTemplate="header">
            <i class="pi pi-circle"></i>
            <h3>
            <p>Content</p></p-card>
      </div>

      <!-- Main Content -->
      <div class="content-grid">

        <!-- Quick Stats -->
        <p-card class="stats-card">
          <ng-template pTemplate="header"><h3>Header</h3><ng-template pTemplate="content">
            <div class="stats-grid">
              <div class="stat-item">
                <i class="pi pi-circle"></i>
                <div class="stat-content">
                  <span class="stat-value">{{ connectedUsers.length }}</span>
                  <span class="stat-label">Connected Users</span>
                </div>
              <div class="stat-item">
                <i class="pi pi-circle"></i>
                <div class="stat-content">
                  <span class="stat-value">{{ currentVersion }}</span>
                  <span class="stat-label">Current Version</span>
                </div>
              <div class="stat-item">
                <i class="pi pi-circle"></i>
                <div class="stat-content">
                  <span class="stat-value">{{ pendingUpdates.length }}</span>
                  <span class="stat-label">Pending Updates</span>
                </div>
            </div></p-card>

        <!-- QR Code & Installation -->
        <p-card class="qr-card">
          <ng-template pTemplate="header"><h3>
            <p>Content</p><ng-template pTemplate="content">
            <div class="qr-section">
              <div class="qr-placeholder">
                <i class="pi pi-circle"></i>
                <p>QR Code</p>
              </div>
              <div class="qr-info">
                <p class="url-text">{{ staffHubUrl }}</p>
                <div class="action-buttons">
                  <p-button severity="primary" (click)="copyUrl()">
                    <i class="pi pi-circle"></i>
                    Copy Link
                  </p-button>
                  <p-button outlined="true" (click)="downloadQR()">
                    <i class="pi pi-circle"></i>
                    Download QR
                  </p-button>
                </div>
            </div></p-card>

        <!-- Connected Users -->
        <p-card class="users-card">
          <ng-template pTemplate="header"><h3>
            <p>Content</p><ng-template pTemplate="content">
            <mat-list>
              <mat-list-item *ngFor="let user of connectedUsers">
                <i class="pi pi-circle"></i>
                <div matListItemTitle>{{ user.name }}</div>
                <div matListItemLine>{{ user.device }} • v{{ user.version }}</div>
                <div matListItemLine class="last-active">
                  Last active: {{ user.lastActive | date:'short' }}
                </div>
                <p-chip matListItemMeta [color]="getStatusColor(user.status)">
                  {{ user.status }}
                </p-chip>
              </mat-list-item>
            </mat-list>

            <div *ngIf="connectedUsers.length === 0" class="empty-state">
              <i class="pi pi-circle"></i>
              <p>No users currently connected</p>
              <p class="hint">Share the QR code above to get staff started</p>
            </div></ng-template></p-card>

        <!-- Update Management -->
        <p-card class="updates-card">
          <ng-template pTemplate="header"><h3>
            <p>
            <div class="header-actions">
              <p-button severity="primary" (click)="deployUpdate()">
                <i class="pi pi-circle"></i>
                Deploy Update
              </p-button>
            </div><ng-template pTemplate="content">
            <div class="update-list">
              <div *ngFor="let update of pendingUpdates" class="update-item">
                <div class="update-info">
                  <h4>Version {{ update.version }}</h4>
                  <p>{{ update.description }}</p>
                  <span class="update-date">{{ update.releaseDate | date:'medium' }}</span>
                </div>
                <div class="update-actions">
                  <p-chip [color]="getUpdateStatusColor(update.status)">
                    {{ update.status }}
                  </p-chip>
                  <p-button text="true" [matMenuTriggerFor]="updateMenu">
                    <i class="pi pi-ellipsis-v"></i>
                  </p-button>
                  <p-menu #updateMenu="matMenu">
                    <button mat-menu-item (click)="deploySpecificUpdate(update)">
                      <i class="pi pi-circle"></i>
                      Deploy Now
                    </p-button>
                    <button mat-menu-item (click)="viewUpdateDetails(update)">
                      <i class="pi pi-info-circle"></i>
                      View Details
                    </p-button>
                  </p-menu>
                </div>
            </div>

            <div *ngIf="pendingUpdates.length === 0" class="empty-state">
              <i class="pi pi-check-circle"></i>
              <p>All updates deployed</p>
              <p class="hint">Staff devices are up to date</p>
            </div></ng-template></p-card>

      </div>
  `,
  styles: [`
    .staff-hub-container {
      padding: 24px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .content-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
    }

    .stats-card {
      grid-column: span 1;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .stat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .stat-icon.online { color: #4caf50; }
    .stat-icon.warning { color: #ff9800; }

    .stat-content {
      display: flex;
      flex-direction: column;
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 500;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.875rem;
      color: #666;
    }

    .qr-section {
      display: flex;
      gap: 24px;
      align-items: center;
    }

    .qr-placeholder {
      width: 200px;
      height: 200px;
      border: 2px dashed #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #666;
    }

    .qr-placeholder mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
    }

    .qr-placeholder p {
      margin: 8px 0 0 0;
      font-size: 0.875rem;
    }

    .qr-info {
      flex: 1;
    }

    .url-text {
      font-family: monospace;
      background: #f5f5f5;
      padding: 8px 12px;
      border-radius: 4px;
      word-break: break-all;
      margin-bottom: 16px;
    }

    .action-buttons {
      display: flex;
      gap: 12px;
    }

    .users-card {
      grid-column: span 1;
    }

    .mat-list-item-icon.online { color: #4caf50; }
    .mat-list-item-icon.away { color: #ff9800; }
    .mat-list-item-icon.offline { color: #9e9e9e; }

    .last-active {
      font-size: 0.75rem;
      color: #999;
    }

    .updates-card {
      grid-column: span 2;
    }

    .header-actions {
      margin-left: auto;
    }

    .update-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .update-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .update-info h4 {
      margin: 0 0 8px 0;
      font-weight: 500;
    }

    .update-info p {
      margin: 0 0 8px 0;
      color: #666;
    }

    .update-date {
      font-size: 0.875rem;
      color: #999;
    }

    .update-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .empty-state {
      text-align: center;
      padding: 32px;
      color: #666;
    }

    .empty-state mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state .hint {
      font-size: 0.875rem;
      color: #999;
    }

    @media (max-width: 768px) {
      .staff-hub-container {
        padding: 16px;
      }

      .content-grid {
        grid-template-columns: 1fr;
      }

      .updates-card {
        grid-column: span 1;
      }

      .qr-section {
        flex-direction: column;
        text-align: center;
      }

      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class StaffHubManagementComponent implements OnInit {
  private messageService = inject(MessageService);
  private dialog = inject(DialogService);

  staffHubUrl = `${window.location.origin}/staff-hub`;
  currentVersion = '1.0.0';

  connectedUsers: StaffHubUser[] = [
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      lastActive: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      status: 'online',
      device: 'iPhone 14',
      version: '1.0.0'
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      lastActive: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      status: 'away',
      device: 'Samsung Galaxy S23',
      version: '1.0.0'
    }
  ];

  pendingUpdates: PWAUpdate[] = [
    {
      id: '1',
      version: '1.1.0',
      description: 'Bug fixes and performance improvements',
      releaseDate: new Date(),
      status: 'pending',
      targetUsers: ['all']
    }
  ];

  ngOnInit(): void {
    // Simulate real-time updates
    interval(30000).subscribe(() => {
      this.updateUserStatuses();
    });
  }

  copyUrl(): void {
    navigator.clipboard.writeText(this.staffHubUrl).then(() => {
      this.messageService.add({ severity: 'info', summary: 'URL copied to clipboard!' });
    });
  }

  downloadQR(): void {
    // Implementation for downloading QR code
    this.messageService.add({ severity: 'info', summary: 'QR code download started' });
  }

  deployUpdate(): void {
    this.messageService.add({ severity: 'info', summary: 'Deploying update to all connected devices...' });
  }

  deploySpecificUpdate(update: PWAUpdate): void {
    this.messageService.add({ severity: 'info', summary: `Deploying version ${update.version}...` }));
  }

  viewUpdateDetails(update: PWAUpdate): void {
    // Open dialog with update details
    console.log('View update details:', update);
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'online': return 'primary';
      case 'away': return 'accent';
      case 'offline': return '';
      default: return '';
    }
  }

  getUpdateStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'accent';
      case 'deployed': return 'primary';
      case 'failed': return 'warn';
      default: return '';
    }
  }

  private updateUserStatuses(): void {
    // Simulate status changes
    this.connectedUsers.forEach(user => {
      if (Math.random() > 0.8) {
        const statuses: ('online' | 'away' | 'offline')[] = ['online', 'away', 'offline'];
        user.status = statuses[Math.floor(Math.random() * statuses.length)];
        user.lastActive = new Date();
      }
    });
  }
}
