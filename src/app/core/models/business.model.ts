/**
 * Business Profile Models
 * Enhanced business management with operational settings
 */

export interface BusinessProfile {
  id: string;
  
  // Basic Information
  name: string;
  displayName?: string;
  description?: string;
  logoUrl?: string;
  website?: string;
  email?: string;
  phone?: string;
  
  // Business Type & Industry
  businessType: 'retail' | 'restaurant' | 'healthcare' | 'professional-services' | 'manufacturing' | 'other';
  industry: string;
  subIndustry?: string;
  
  // Address Information
  address: BusinessAddress;
  
  // Hours of Operation (HOO)
  hoursOfOperation: HoursOfOperation;
  
  // Hours of Business (HOB) - When business activities occur
  hoursOfBusiness: HoursOfBusiness;
  
  // Operational Settings
  operationalSettings: OperationalSettings;
  
  // Time Zone & Locale
  timeZone: string;
  locale: string;
  currency: string;
  
  // Staff Management Settings
  staffSettings: StaffManagementSettings;
  
  // Scheduling Settings
  schedulingSettings: SchedulingSettings;
  
  // Compliance & Legal
  compliance: ComplianceSettings;
  
  // System Fields
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  ownerId: string;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  
  // Multi-location support
  parentBusinessId?: string;
  childBusinessIds?: string[];
  isHeadquarters?: boolean;
}

export interface BusinessAddress {
  street: string;
  street2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface HoursOfOperation {
  // Standard operating hours
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
  
  // Special hours and exceptions
  holidays: HolidayHours[];
  specialDates: SpecialDateHours[];
  
  // Seasonal adjustments
  seasonalHours?: SeasonalHours[];
  
  // Display settings
  displaySettings: {
    showOnWebsite: boolean;
    showToCustomers: boolean;
    autoUpdateStatus: boolean; // Auto-update "Open/Closed" status
  };
}

export interface DayHours {
  isOpen: boolean;
  openTime?: string; // HH:mm format
  closeTime?: string; // HH:mm format
  breaks?: TimeBreak[];
  notes?: string;
  
  // Multiple shifts support
  shifts?: DayShift[];
}

export interface DayShift {
  name: string;
  openTime: string;
  closeTime: string;
  description?: string;
}

export interface TimeBreak {
  name: string;
  startTime: string;
  endTime: string;
  type: 'lunch' | 'break' | 'maintenance' | 'other';
}

export interface HolidayHours {
  name: string;
  date: Date;
  isRecurring: boolean;
  recurringType?: 'yearly' | 'monthly';
  hours: DayHours;
  description?: string;
}

export interface SpecialDateHours {
  name: string;
  startDate: Date;
  endDate: Date;
  hours: DayHours;
  reason: string;
  isPublic: boolean;
}

export interface SeasonalHours {
  name: string;
  startDate: string; // MM-DD format
  endDate: string; // MM-DD format
  hoursOverride: Partial<HoursOfOperation>;
  description?: string;
}

export interface HoursOfBusiness {
  // When business operations can occur (may extend beyond customer hours)
  operationalHours: {
    monday: DayHours;
    tuesday: DayHours;
    wednesday: DayHours;
    thursday: DayHours;
    friday: DayHours;
    saturday: DayHours;
    sunday: DayHours;
  };
  
  // Staff scheduling windows
  staffSchedulingWindow: {
    earliestStart: string; // HH:mm
    latestEnd: string; // HH:mm
    allowOvernightShifts: boolean;
  };
  
  // Administrative hours
  adminHours?: {
    monday: DayHours;
    tuesday: DayHours;
    wednesday: DayHours;
    thursday: DayHours;
    friday: DayHours;
    saturday: DayHours;
    sunday: DayHours;
  };
}

export interface OperationalSettings {
  // Capacity & Limits
  maxOccupancy?: number;
  maxStaffOnShift?: number;
  minStaffOnShift?: number;
  
  // Service settings
  appointmentDuration?: number; // minutes
  bufferTime?: number; // minutes between appointments
  advanceBookingDays?: number;
  
  // Break and meal policies
  breakPolicies: {
    minimumShiftForBreak: number; // hours
    breakDuration: number; // minutes
    mealBreakDuration: number; // minutes
    minimumShiftForMeal: number; // hours
    maxHoursWithoutBreak: number;
  };
  
  // Overtime policies
  overtimePolicies: {
    dailyOvertimeThreshold: number; // hours
    weeklyOvertimeThreshold: number; // hours
    overtimeMultiplier: number; // e.g., 1.5 for time-and-a-half
    requireApprovalForOvertime: boolean;
  };
}

export interface StaffManagementSettings {
  // Scheduling preferences
  scheduleAdvanceNotice: number; // days
  allowStaffSelfScheduling: boolean;
  allowShiftSwapping: boolean;
  requireManagerApproval: boolean;
  
  // Time tracking
  clockInMethod: 'manual' | 'geofenced' | 'qr-code' | 'biometric';
  allowEarlyClockIn: boolean;
  earlyClockInThreshold?: number; // minutes
  allowLateClockOut: boolean;
  lateClockOutThreshold?: number; // minutes
  
  // Availability requirements
  minimumAvailabilityHours: number; // per week
  requireWeekendAvailability: boolean;
  requireHolidayAvailability: boolean;
}

export interface SchedulingSettings {
  // Auto-scheduling
  enableAutoScheduling: boolean;
  autoSchedulingRules: AutoSchedulingRule[];
  
  // Shift preferences
  defaultShiftLength: number; // hours
  minimumShiftLength: number; // hours
  maximumShiftLength: number; // hours
  
  // Coverage requirements
  minimumCoverageRules: CoverageRule[];
  
  // Notifications
  schedulePublishDay: number; // day of week (0 = Sunday)
  notifyStaffOfChanges: boolean;
  notificationLeadTime: number; // hours
}

export interface AutoSchedulingRule {
  id: string;
  name: string;
  priority: number;
  conditions: any;
  actions: any;
  isActive: boolean;
}

export interface CoverageRule {
  id: string;
  name: string;
  timeSlot: {
    dayOfWeek: number;
    startTime: string;
    endTime: string;
  };
  minimumStaff: number;
  requiredSkills?: string[];
  requiredPositions?: string[];
}

export interface ComplianceSettings {
  // Labor law compliance
  laborLaws: {
    jurisdiction: string;
    minimumWage: number;
    overtimeRules: any;
    breakRequirements: any;
    minorWorkRestrictions?: any;
  };
  
  // Industry-specific compliance
  industryRegulations?: {
    type: string;
    requirements: any;
    certificationRequired: boolean;
  };
  
  // Record keeping
  recordRetention: {
    timesheetRetentionDays: number;
    scheduleRetentionDays: number;
    performanceRetentionDays: number;
  };
}

// Utility interfaces for business operations
export interface BusinessStatus {
  isCurrentlyOpen: boolean;
  nextStatusChange: Date;
  currentShift?: string;
  staffOnDuty: number;
  estimatedCapacity: number;
}

export interface BusinessMetrics {
  businessId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  
  // Operational metrics
  totalOperatingHours: number;
  averageStaffPerShift: number;
  totalCustomersServed?: number;
  averageServiceTime?: number;
  
  // Staff metrics
  totalStaffHours: number;
  overtimeHours: number;
  absenteeismRate: number;
  turnoverRate: number;
  
  // Financial metrics (if applicable)
  revenue?: number;
  laborCostPercentage?: number;
  profitMargin?: number;
}
